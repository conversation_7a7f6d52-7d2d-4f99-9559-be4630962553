# Relay cache still refetches even with TTL + store‐only – need deep diagnosis

## Context
We’ve implemented a first-render cache optimisation in two places:

1. **`ui/src/relay/createPersistedStore.ts`**
   • After IndexedDB hydration we expose
   ```ts
   (window as any).__RELAY_CACHE_HYDRATED_AT__ = Date.now();
   (window as any).__RELAY_CACHE_TTL__         = TTL_MS; // 5 min default
   ```
2. **`ui/src/relay/useSWRQuery.ts`** (custom hook that wraps `useLazyLoadQuery`)
   • On mount we run `environment.check(op)`
   • If `availability.status === 'available'` **AND** either `!isStale` *or* we
     are inside the TTL window, we force:
   ```ts
   fetchPolicy = 'store-only';
   UNSTABLE_renderPolicy = 'full';
   ```
   (otherwise default `store-or-network`).

The goal: **No network request on first navigation** when the hydrated cache is
fresh enough.

## Symptom (latest browser console excerpt)
```
🚀 Starting GraphQL query: TimesheetRosterQuery { fetchPolicy: 'store-only' }
✅ Relay pre-check: 'TimesheetRosterQuery' is AVAILABLE from cache
…
✅ Relay check(): 'TimesheetRosterQuery' reported AVAILABLE but still fetched network
```
We still see a network fetch (~180 ms) followed by `cacheStatus:'MISS'`.  TTL is
0 min old (just hydrated) so we expected a full cache hit.

More diagnostics:
```
🏪 Store Analysis: 2055 records
Cache Hit Probability: 100%
Predicted Reason: network_first_policy
```

## Files to inspect (paths are relative to repository root)
* `ui/src/relay/useSWRQuery.ts` – hook, lines 190–250 contain fetchPolicy logic
* `ui/src/relay/createPersistedStore.ts` – hydration + TTL injection, lines 350–430
* Relay environment is created in `ui/src/relay/initRelayEnvironment.ts` with
  `UNSTABLE_defaultRenderPolicy:'partial'` (still there)

## What we need from you
1. **Find the root cause** of why Relay performs a network request even though:
   * `check()` returns `available`
   * we call `useLazyLoadQuery` with `fetchPolicy:'store-only'` **and**
     `UNSTABLE_renderPolicy:'full'`.
2. Determine whether multiple copies of the same query are mounted, variable
   mismatches, or another internal call is downgrading the policy.
3. Suggest precise code changes (file + line numbers) or alternative approach
   (e.g. using `loadQuery` + retain, using `render-as-you-fetch`, etc.) to fully
   prevent the initial fetch without breaking incremental updates later.
4. Outline how to validate the fix (console logs, Relay dev-tools, unit test).

## Additional hints
* The hook may be called several times in the roster screen; duplicate mounts
  could race.
* Relay might internally normalise variables; check whether `createOperationDescriptor`
  in hook and Relay’s internal call differ due to default values.
* Ensure we are not hitting a `@defer`/connection edge that marks operation
  incomplete and forces fetch.

**Deliver your answer as a concise engineering investigation (bullet points OK), then proposed code diff(s).**
