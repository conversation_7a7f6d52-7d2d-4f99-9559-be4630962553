# Claude Subagents Workflow Improvements

## Introduction

This document provides feedback and recommendations for enhancing your Claude Code workflow and subagent setup. I've reviewed your current agents in `.claude/agents/` and the ClaudeCodeAgents repository on GitHub ([ClaudeCodeAgents](https://github.com/darcyegb/ClaudeCodeAgents?tab=readme-ov-file)). Your existing setup is strong, with specialized agents for different domains, but there's room to improve consistency between implementation plans and actual results by incorporating more quality assurance (QA) focused subagents and structured workflows.

Your main concern is that results don't always match the fed implementation plan. The ClaudeCodeAgents repo emphasizes QA agents that verify, validate, and reality-check implementations, which could help address this by adding post-implementation checks.

## Current Setup Review

Based on the contents of `.claude/agents/`, you have the following subagents:

- **backend-specialist.md**: Handles .NET/C#/GraphQL backend tasks.
- **code-reviewer.md**: Performs code reviews.
- **frontend-specialist.md**: Manages React/TypeScript/Relay frontend development.
- **git-specialist.md**: Specializes in Git operations.
- **integration-specialist.md**: Deals with system integration and deployment.
- **performance-specialist.md**: Focuses on performance optimization.
- **test-specialist.md**: Handles testing across frontend, backend, and integration.
- **workflow-orchestrator.md**: Coordinates complex multi-phase tasks.

This is a solid foundation, covering development, testing, and orchestration. The workflow-orchestrator is particularly useful for delegating to other specialists, aligning with best practices in the Claude Code subagents documentation.

From project files like CLAUDE.md, subagents are configured with YAML frontmatter (name, description, tools) and system prompts, and they're used for automatic delegation or explicit invocation. They enforce standards like TypeScript strict mode and Relay best practices.

## Review of ClaudeCodeAgents Repository

The [ClaudeCodeAgents repo](https://github.com/darcyegb/ClaudeCodeAgents?tab=readme-ov-file) provides a collection of QA-focused agents for Claude Code, emphasizing verification, validation, and quality checks. Key agents include:

- **Jenny (Implementation Verification Agent)**: Verifies if code matches project specifications (e.g., security requirements, schema implementations).
- **Claude MD Compliance Checker**: Ensures changes adhere to guidelines in CLAUDE.md files.
- **Code Quality Pragmatist**: Checks for over-engineering, unnecessary complexity, and maintainability issues.
- **Karen (Reality Check Agent)**: Assesses actual project completion, cuts through incomplete work, and creates realistic plans.
- **Task Completion Validator**: Verifies that tasks are functionally complete, not just stubbed (e.g., end-to-end authentication).
- **UI Comprehensive Tester**: Thoroughly tests UI across platforms using tools like Puppeteer or Playwright.
- **Ultrathink Debugger**: Provides advanced debugging.

These agents are designed for post-implementation QA, helping ensure outputs align with plans by identifying gaps early.

## Recommendations

To address mismatches between implementation plans and results, I recommend extending your setup with QA-focused subagents inspired by the repo. This will add verification layers without overcomplicating your current domain-specific agents. Additionally, refine your workflow to include mandatory QA steps.

### 1. Add New QA Subagents
Create the following new files in `.claude/agents/` based on the repo's agents. Use the standard Markdown format with YAML frontmatter. Start by generating them with Claude Code's `/agents` command for a solid base, then customize.

- **implementation-verifier.md** (Inspired by Jenny):
  - **Description**: Use to verify if implemented code matches specifications and plans.
  - **Tools**: Read, Grep, Glob.
  - **System Prompt**: Focus on checking feature completeness, security, and alignment with input plans.

- **task-validator.md** (Inspired by Task Completion Validator and Karen):
  - **Description**: Validate functional completeness of tasks and provide reality checks on project status.
  - **Tools**: Read, Bash, Grep.
  - **System Prompt**: Ensure end-to-end functionality, identify stubs, and create action plans for gaps.

- **code-quality-checker.md** (Inspired by Code Quality Pragmatist and Claude MD Compliance Checker):
  - **Description**: Check for over-engineering, compliance with CLAUDE.md, and maintainability.
  - **Tools**: Read, Grep.
  - **System Prompt**: Enforce simplicity, no premature optimizations, and adherence to project standards.

- **ui-tester.md** (Inspired by UI Comprehensive Tester):
  - **Description**: Perform comprehensive UI testing for web components.
  - **Tools**: Bash (for running test commands), Read.
  - **System Prompt**: Test user flows, edge cases, and browser compatibility without running the app (per rules: ask user to run apps).

These additions will complement your existing code-reviewer and test-specialist by focusing on plan alignment and pragmatic quality.

### 2. Workflow Improvements
Update your development process to proactively use these subagents, reducing mismatches:

- **Pre-Implementation**: Use workflow-orchestrator to decompose plans and delegate to domain specialists (e.g., frontend-specialist, backend-specialist).
- **During Implementation**: Explicitly invoke specialists as needed, e.g., `> Use the backend-specialist to implement this GraphQL mutation`.
- **Post-Implementation QA Phase** (New Step):
  - Automatically delegate to new QA agents: e.g., after changes, invoke implementation-verifier to check against the original plan.
  - Chain subagents: `> First use task-validator to check completeness, then code-quality-checker for maintainability`.
  - Integrate with code-reviewer: Run it last for overall review.
- **Testing and Validation**: Enhance test-specialist to collaborate with task-validator for functional checks. Remember rules: Don't run apps or interactive commands; ask the user instead.
- **Orchestration Enhancements**: Update workflow-orchestrator's prompt to include a final QA delegation step, ensuring verification before completion.
- **Tool Management**: Limit tools for QA agents to read-only (e.g., Read, Grep) to prevent unintended changes. Use `/agents` command to manage this interactively.

### 3. General Best Practices
- **Start with Claude Generation**: As per Claude Code docs, generate new subagents with `/agents` and customize.
- **Version Control**: Commit `.claude/agents/` to Git for team sharing.
- **Monitor and Iterate**: After using new agents, review if they reduce plan mismatches. Adjust prompts based on results.
- **Alignment with Project Rules**: Ensure subagents follow rules like using pnpm for UI, no interactive commands, and adherence to .NET 9/Hot Chocolate.

Implementing these should make your workflow more robust, ensuring implementations better match plans through systematic verification.

If you need help creating these agent files or further details, let me know!
