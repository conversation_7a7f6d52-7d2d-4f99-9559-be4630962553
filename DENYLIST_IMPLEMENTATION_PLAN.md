# Relay Cache – Deny-List–Only Implementation Plan

*Last updated: 2025-08-04*

## 1. Objective
Implement a **deny-list–only** persistence filter for Relay cache – removing all allow-list maintenance, feature flags, phased roll-outs and custom ESLint rules – while retaining essential security & size-guard rails.  The goal is > 85 % cache-persistence rate with zero sensitive-data leakage.

## 2. Guiding Principles
1. **Security first, simplicity second** – deny only what is provably dangerous; cache everything else.
2. **No legacy support layers** – *absolutely no* feature flags, hybrid modes, compatibility shims or rollback toggles.
3. **No custom ESLint rules** – rely only on the built-in rule sets (`eslint:recommended`, `@typescript-eslint/recommended`, etc.).
4. **Ground decisions in evidence** – use real debug-log and code inspection data (see Appendix C in PRD).
5. **Stop-loss rule** – if a task hits two failed approaches, **do not try a third**.  Document the two attempts and escalate.
6. **Diagnose before patching** – never treat symptoms; create a sub-task to root-cause unexpected errors before modifying code.

## 3. In-Scope
- `ui/src/relay/cacheFilters.ts` and related helpers/tests.
- Removal of allow-list arrays & business-entity enumerations.
- Introduction of small static deny-list patterns (authentication, error, PII, transient records).
- Size-limit guard rail (keep, but make constant & documented).
- Test updates, docs, and ESLint config cleanup.

## 4. Out-of-Scope
- Backend GraphQL schema changes.
- Any feature-flag infrastructure.
- Runtime toggles for old/new behaviour.

## 5. Evidence-Driven Analysis (Current Code)
| Evidence | Source |
|----------|--------|
| Hybrid filter already exists – both allow & deny patterns | `ui/src/relay/cacheFilters.ts` lines 30-64 |
| Unknown-record denial at 72 % rate | Browser debug log, PRD Appendix C |
| Allow-list explicitly enumerates `Employer`, yet logs show `Employer` denied due to typename mismatch inside connections | Code + log sample |
| Custom ESLint rules registered | `ui/eslint.config.js` lines 21-22 |

These findings justify a deny-list-only rewrite and ESLint simplification.

## 6. High-Level Work Breakdown

1. **Remove Allow-List Infrastructure**
   - [ ] Delete `BUSINESS_ENTITY_TYPES`, `ALLOWED_DATAID_PATTERNS`, and associated helper functions (`isBusinessEntity`, `isAllowedDataID`).
   - [ ] Delete comment & debug logic that classifies `allowed_business_entity` / `allowed_whitelist_pattern`.
   - [ ] Update persistence-reason enum accordingly.

2. **Establish Static Deny-List**
   - [ ] Create constant `SECURITY_DENYLIST` (use patterns from PRD Appendix B, limit < 10).
   - [ ] Replace calls to `isDeniedDataID` with direct deny-list match against `SECURITY_DENYLIST`.

3. **Simplify `shouldPersistRecordWithReason`**
   - [ ] New order: (a) root record, (b) error indicators, (c) security deny-list, (d) size limit, (e) default **allow**.
   - [ ] Remove unknown-record analysis & denial; keep debug logging but downgrade to `debug.info` when record *is* persisted.

4. **Size-Limit Guard Rail**
   - [ ] Replace `getSizeThreshold` lookup with a single constant `MAX_RECORD_SIZE_BYTES = 100 * 1024` (100 KB) + special `CustomViews` 30 KB limit.

5. **Tests**
   - [ ] Rewrite unit tests in `ui/src/relay/__tests__` to reflect deny-list behaviour (ensure security patterns denied; business entities persisted by default).
   - [ ] Add regression test for `ChaptersInfoDto` to verify it now persists.

6. **Remove Custom ESLint Rules**
   - [ ] Delete directory `ui/eslint-custom-rules/`.
   - [ ] Update `ui/eslint.config.js` to remove `relay-fragment-dependencies` and `react-html-structure-validation` references; extend with built-in rules only.
   - [ ] Run `pnpm exec eslint . --ext .ts,.tsx` and ensure zero new errors.

7. **Developer Tooling & Docs**
   - [ ] Update `ui/docs/relay-cache.md` (or create) explaining deny-list rationale and patterns.
   - [ ] Remove references to removed feature flags in README and Vite env files.

8. **Manual Verification**
   - [ ] Run the app, navigate Employer + Timesheet rosters, confirm network tab shows single request per resource after first load.
   - [ ] Use Relay DevTools → Store Inspector to confirm > 85 % persistence and that no `UserInfoOutput` or errors are stored.

9. **Code Review & Merge**
   - [ ] Self-review against acceptance criteria.


## 7. Risk & Mitigations
| Risk | Mitigation |
|------|------------|
| Over-caching sensitive data | Security deny-list review + automated tests |
| Large records causing perf issues | 100 KB limit + connection edge trimming retained |
| ESLint removal exposes issues | Keep built-in rule sets; run `eslint --fix` |

## 8. Acceptance Criteria
- All allow-list code paths **deleted**; only deny-list + size check remain.
- Browser session with typical workflows shows > 85 % records persisted; security patterns never persisted.
- No feature flags or compatibility layers added.
- `ui/eslint-custom-rules/` directory removed; ESLint passes with built-in rules.
- Unit tests covering: deny security patterns, allow previously denied business types, size limit.
- Implementor stop-loss: if two alternative approaches fail to solve a problem, **do not attempt a third** – document attempts & escalate (must appear in PR description).
- Any unexpected errors must trigger a separate root-cause sub-task; no speculative fixes.

## 9. Timeline (ideal)
| Week | Tasks |
|------|-------|
| 1 | Steps 1-3, initial test updates |
| 2 | Steps 4-6, ESLint cleanup |
| 3 | Manual verification, docs, PR & review |

*Total: ~3 developer-weeks*

---
**Remember**: prioritise correctness over expedience; rely on evidence; stop after two failed approaches and escalate with a detailed report.
