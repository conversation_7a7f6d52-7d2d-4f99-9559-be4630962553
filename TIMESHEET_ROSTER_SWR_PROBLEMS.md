# Timesheet Roster SWR – Identified Problems, Root Causes & Fixes

_Last updated: 2025-08-04_

---

## Overview
`TimesheetRosterQuery` is expected to follow a **stale-while-revalidate (SWR)** pattern:

1. Serve cached data instantly if present.
2. Skip the network fetch when the cache is fresh (≤ 30 s old).
3. If the cache is stale, fetch fresh data **without** disturbing the paginated data already in the store.
4. Avoid re-rendering the UI when the fresh payload is identical to the cached payload.

Current behaviour violates points 2, 3, and 4.  The three user-visible problems, their root causes, and the high-level fixes are documented below.

---

## Problem 1 – "Always Fetch" (30 s TTL Ignored)

|                       |                                                        |
| --------------------- | ------------------------------------------------------ |
| **Symptom**           | Background request fires on every page/component load—even when the per-operation timestamp is only milliseconds old. |
| **Root cause**        | Logic in `useSWRQuery.ts` OR-combines `availability.status === 'stale'` **before** checking the 30-second time-to-live (TTL). Hydrated records are marked `stale` by Relay because their `sourceEpoch` is `0`, causing the `stale` branch to short-circuit the TTL clause. |
| **Fix**               | Re-order / refine the condition: treat `stale` as actionable **only when** `cacheAge > REVALIDATE_MS`.  In practice: `shouldTrigger = (!savedAt) || (cacheAge > TTL) || (status === 'stale' && cacheAge > TTL)`. |

---

## Problem 2 – Unnecessary Re-renders (Identical Payload)

|                       |                                                        |
| --------------------- | ------------------------------------------------------ |
| **Symptom**           | When the background request completes, the component re-renders even though the data is unchanged. |
| **Root cause**        | We revalidate using `fetchPolicy: "network-only"`. Relay publishes the full payload into the store regardless of equality, assigning new `recordEpoch`s to every touched record.  React Relay therefore propagates a value change, prompting a re-render. |
| **Fix**               | a) Use `fetchPolicy: "store-and-network"` so Relay will reuse the cached response and only publish the network payload if it differs.  b) Alternatively, compute a hash (e.g. SHA-1) of the `payload.data` and bail out early if the hash matches the last one stored for the cacheKey. |

---

## Problem 3 – Paginated Edges Are Lost After Refresh

|                       |                                                        |
| --------------------- | ------------------------------------------------------ |
| **Symptom**           | After the network refresh, the roster displays only the first page; any previously cached pages disappear. |
| **Root cause**        | The revalidation request is the **base query** (`first: 25`, `after: null`).  Relay’s connection updater replaces the entire edge list with the list from the latest payload.  Down-stream edge-trimming then deletes now-orphaned edges, permanently discarding pages 2…N. |
| **Fix**               | a) Include the **current `cursor` window** in the revalidation request (`after: lastSeenCursor`).  b) Or, post-process the payload: merge fresh edges into the existing connection instead of replacing it.  Relay supports this via `@appendNode`/`@prependNode` directives or custom `ConnectionHandler` updaters. |

---

## Consolidated Fix Summary

1. **TTL logic** – honour the 30 s freshness window by factoring `cacheAge` into the `stale` path.
2. **Payload equality** – adopt `store-and-network` (or hash-check) to avoid no-op publishes.
3. **Pagination safety** – revalidate against the same *logical* window or merge edges.

These fixes will restore correct SWR semantics, remove redundant traffic, prevent unnecessary React work, and preserve already-fetched pagination slices.
