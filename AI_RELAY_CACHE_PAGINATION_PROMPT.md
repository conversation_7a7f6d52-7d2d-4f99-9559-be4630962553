# Relay Cache Enhancements – Pagination Caching & True SWR

## Your Mission
Implement three coordinated improvements to our Relay-powered React frontend:

1. **Persist & reuse pagination slices** so that subsequent visits / reloads serve pages 2-N from cache when possible.
2. **Refactor `RelayEnvironmentLayoutQuery` to use our custom `useSWRQuery` hook** (adds TTL-aware store-only behaviour).
3. **Upgrade `useSWRQuery` to true stale-while-revalidate**:
   * Serve cached data immediately.
   * Trigger a background refetch **only** when the cached slice is > 1 minute old (instead of the current 5-minute TTL or unconditional background refetch).

The goal: flawless offline/low-latency UX while keeping data reasonably fresh.

---

## Repository Context
*Frontend stack*: React 18, **Relay 14+** (react-relay & relay-runtime), Vite.  Cache persistence via IndexedDB using a selective-filter approach.

### Key Files (relative to repo root)
| Path | Notes |
|------|-------|
| `ui/src/relay/useSWRQuery.ts` | Custom hook that wraps `useLazyLoadQuery`.  Contains TTL logic (5 min) and extensive debug logging. |
| `ui/src/relay/createPersistedStore.ts` | IndexedDB persistence: serialises `RecordSource` with record-level filter.  Currently **trims connection edges/pageInfo** for size. |
| `ui/src/relay/initRelayEnvironment.ts` | Async environment factory. |
| `ui/src/components/Layout/RelayEnvironmentLayout/RelayEnvironmentLayout.tsx` | Renders the shell; currently calls `useLazyLoadQuery` directly for `RelayEnvironmentLayoutQuery`. |
| `ui/src/container/TimesheetRoster/TimesheetRoster.tsx` | Page that uses `useSWRQuery` for `TimesheetRosterQuery`. |
| `ui/src/components/TimesheetRoster/TimesheetRosterTable/TimesheetRosterTable.tsx` | Uses `usePaginationFragment`; triggers **`TimesheetRosterRefetchQuery`** on scroll. |

---

## 1  Persist Pagination Slices

Problem: `TimesheetRosterRefetchQuery` is always network-fetched because connection edges (e.g. `TimeSheetEdge`, `PageInfo`) for pages 2-N are not persisted.

### Tasks
1. Audit `createPersistedStore.ts` filter logic – find where connection edges are dropped (look for typename checks like `*Edge`, `*Connection`).
2. Allow persistence of connection edges **belonging to whitelisted entity types** (TimeSheet, Employer, etc.) and their `pageInfo`.
3. Ensure size guards still apply (e.g. keep max 100 edges per connection slice).
4. On load, hydrated store should contain previously fetched pagination slices.
5. Validate with `environment.check()` for a refetch operation that the slice is now `status: 'available'`.

---

## 2  Wrap `RelayEnvironmentLayoutQuery` with `useSWRQuery`

### Tasks
1. In `RelayEnvironmentLayout.tsx`, replace
   ```tsx
   const data = useLazyLoadQuery(LayoutQuery, vars);
   ```
   with our hook:
   ```tsx
   const data = useSWRQuery<RelayEnvironmentLayoutQuery>(LayoutQuery, vars);
   ```
2. Address Suspense / fallback differences if any.
3. Verify that on fresh load, no network request is fired for this query when cache is warm.

---

## 3  True SWR in `useSWRQuery`

Current behaviour: if slice is fresh (≤5 min), hook sets `fetchPolicy: 'store-only'` **and prevents any background refetch**.

Desired behaviour:
* Serve cache immediately (`fetchPolicy: 'store-only'`, `UNSTABLE_renderPolicy:'full'`).
* **If** cached slice is older than 1 min (configurable) **then** trigger a background `store-and-network` request *after* first render.  Use `useEffect` to kick off `fetchQuery` with `networkCacheConfig: {force:true}` (or similar).
* Do **not** revalidate again until another minute has passed (debounced per query key + variables).

### Tasks
1. Introduce constant `REVALIDATE_MS = 60_000`.
2. Store per-operation last-validated timestamp in a WeakMap or on `environment` via metadata.
3. After serving cached data, schedule background revalidate respecting the cooldown.
4. Preserve existing TTL cache-skip logic (5 min heuristic) for first navigation.
5. Update debug logs accordingly.

---

## Validation Checklist
1. Cold load (no IndexedDB): normal network traffic; cache persists pages 1-N.
2. Hot reload within 1 min: **zero** network requests for `RelayEnvironmentLayoutQuery`, `TimesheetRosterQuery`, **and** first pagination slice.
3. Wait >1 min, reload: first paint uses cache, but background requests fire and UI updates with fresh data.
4. Offline mode: user can scroll previously visited pages without network; new pages still display loading.
5. Run `npm run test:persistence` (if exists) to ensure store serialises > 90 % of records.

---

### Deliverables
* Code changes with inline comments.
* Updated unit/integration tests if needed.
* A short `CHANGELOG.md` entry summarising new behaviour.

Good luck — we’re counting on you! :)
