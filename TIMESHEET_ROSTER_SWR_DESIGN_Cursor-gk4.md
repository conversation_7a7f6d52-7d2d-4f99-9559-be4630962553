# Investigation Report: Timesheet Roster SWR Issues

**Report Generated By:** Cursor AI Assistant (Grok 4)
**Date:** [Current Date]
**Scope:** Research into why background revalidation triggers on page reload despite fresh cache, and why paginated data is lost on refetch. Based on code review of `useSWRQuery.ts`, `createPersistedStore.ts`, provided logs, and previous conversation context. No code changes made.

This is a research-only report. Findings are derived from semantic searches, file reads, and log analysis. The investigation reveals potential bugs in timestamp persistence and pagination handling.

## Summary of Findings
- **Refetch on Page Reload:** The code intends to use per-operation timestamps (`window.__RELAY_OP_SAVED_AT__`) to check cache freshness. However, these timestamps may not be properly persisted or loaded across page reloads, leading to `savedAt` being undefined, which forces revalidation. Persistence relies on IndexedDB, but issues like quota errors or incomplete saves could cause this.
- **Pagination Data Loss:** During revalidation, if the payload hash differs (even slightly), the code replaces all edges instead of merging, discarding previously paginated pages. Logs show added records but edgeDelta=0, suggesting replacement occurred, or merge logic failed to preserve slices. The cursor alignment in revalidation may also misalignment, fetching only initial pages.
- **Overall Assessment:** Behavior matches code logic but exposes edge cases in persistence and hash sensitivity. Debug logs indicate cache is detected as available, but revalidation still triggers, pointing to timestamp issues.
- **Recommendations:** (Research-only; no changes) Add more logging for timestamp load/save, test IndexedDB in incognito to simulate failures, consider less sensitive hash (e.g., ignore timestamps in payload).

## Detailed Investigation

### 1. Refetch Always Triggers on Page Reload
**Observed Behavior (from Logs):**
- Cache is available: "✅ Relay pre-check: 'TimesheetRosterQuery' is AVAILABLE from cache" (repeated).
- Cache hit logged: "⚡ Cache Hit: TimesheetRosterQuery loaded in 316ms".
- Yet, background revalidation starts immediately: "🔄 Starting background revalidation for TimesheetRosterQuery".
- This happens even if last refetch <30s ago, as per user report.

**Code Analysis (`useSWRQuery.ts`):**
- Fetch policy decision (lines 563-591): Uses `environment.check()` to set `fetchPolicy: 'store-only'` if status is 'available' or 'stale'.
- Revalidation trigger (lines 584-586):
  ```
  if (!savedAt || isStaleButExpired || (savedAt && Date.now() - savedAt > REVALIDATE_MS)) {
      shouldTriggerBackgroundRevalidation = true;
  }
  ```
  - Key: If `savedAt` (from `window.__RELAY_OP_SAVED_AT__[cacheKey]`) is undefined (`!savedAt`), it always triggers.
- In `triggerBackgroundRevalidation` (line 469): Marks as revalidated and updates timestamp only after successful network fetch (line 472-489).
- UseEffect (lines 733-751): Triggers revalidation if `shouldTriggerBackgroundRevalidation` and `fetchPolicy === 'store-only'`.

**Persistence Analysis (`createPersistedStore.ts`):**
- Timestamps stored in `globalOpSavedAt` (line 165), exposed to `window.__RELAY_OP_SAVED_AT__` (line 608).
- On load (line 204): `globalOpSavedAt = blobV2.opSavedAt ?? {};` – loads from IndexedDB blob.
- On persist (line 371): Includes `opSavedAt: globalOpSavedAt` in blob.
- Scheduled via `notifyStoreUpdated()` (line 524), which calls `schedule()` to debounce persist.
- Beforeunload handler (lines 674-689): Attempts synchronous persist on tab close.
- Potential Issues:
  - If persist fails (e.g., quota exceeded, lines 396-505), timestamps aren't saved.
  - On reload, if blob load fails or lacks `opSavedAt`, `savedAt` is undefined, forcing revalidation.
  - Logs don't show persistence errors, but user behavior suggests timestamps aren't surviving reloads.
  - Previous conversation: Indicates ongoing issues with updater and fetch policies, suggesting code evolved but persistence bugs persist.

**Why It Happens:**
- On page reload, if timestamps aren't loaded (e.g., IndexedDB issue, version mismatch clearing cache – line 185), `savedAt` is undefined, triggering revalidation regardless of cache freshness.
- Logs show revalidation starts right after cache hit, aligning with `!savedAt` condition.

### 2. Paginated Data Lost on Refetch
**Observed Behavior (from Logs):**
- Store diff after revalidation: "addedRecords: 20, removedRecords: 0, changedRecords: 2, preEdgeCount: 48, postEdgeCount: 48, edgeDelta: 0".
- User reports: "cache of paginated data is lost every time a refetch is made".
- Debug logs insufficient, as noted.

**Code Analysis (`useSWRQuery.ts`):**
- In `triggerBackgroundRevalidation` (lines 353-409): After fetch, uses `commitUpdate` to:
  - Get connection (e.g., 'timesheets').
  - Compute `incomingHash` on new edges (serializes id/cursor).
  - If `prevHash !== incomingHash`: Replace edges with new ones (line 380) – this discards old paginated pages.
  - If same: Merge via `mergeConnectionEdges` (line 391) – should preserve pages.
- Cursor alignment (lines 280-340): Attempts to set `after` to current endCursor to fetch "same window + new data", but if no cursor or failure, uses original variables (likely first page only).
- Store diff logs edgeDelta=0, but addedRecords=20 – suggests merge happened, but perhaps edges weren't expanded, or UI re-renders with only new data.

**Merge Logic (`cacheFilters.ts`, referenced in useSWRQuery):**
- `mergeConnectionEdges` (lines 423-535): Merges new edges, dedupes by cursor/id, preserves initial/recent slices if over limit.
- If replacement occurs (hash differ), all old edges discarded – likely cause of loss if any data change (e.g., timestamp update) alters hash.

**Why It Happens:**
- **Hash Sensitivity:** Even minor changes (e.g., updated timestamps in payload) change hash, triggering replacement and losing extra pages.
- **Cursor Misalignment:** If alignment fails, refetch might only get initial page, replacing extended pagination.
- **Logs Insight:** edgeDelta=0 but addedRecords=20 suggests some merge, but perhaps not for the paginated connection, or UI doesn't reflect it.
- Previous conversation: Mentions failed updater attempts, aligning with current post-publish approach, which may not fully prevent loss.

## Potential Root Causes
- **Timestamp Persistence:** Incomplete save on unload or load failures make `savedAt` undefined.
- **Hash Over-Sensitivity:** Include only stable fields in hash to avoid unnecessary replacements.
- **Connection Lookup:** Field patterns ('timesheets', etc.) might not match all cases, skipping merge.
- **Relay Behavior:** `network-only` publishes full payload before commitUpdate, potentially causing interim UI updates with partial data.

## Recommendations (Research-Only)
- Enhance logging: Add traces for `savedAt` value, persistence success, and exact hash components.
- Test Scenarios: Simulate reloads, check IndexedDB contents pre/post.
- Alternatives: Use Approach B from previous doc (network layer) to avoid publish if unchanged.
- Debug Commands: Use `__clearPerOpTimestamps()` to force revalidation and test.

**End of Report**
