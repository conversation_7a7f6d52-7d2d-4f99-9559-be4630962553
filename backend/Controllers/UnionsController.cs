using backend.Data.HttpQueries;
using backend.Data.Models;
using backend.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace backend.Controllers;

[Route("api/[controller]")]
[ApiController]
public class UnionsController(
    ILogger<AgreementsController> logger,
    EPRLiveDBContext db,
    IWebHostEnvironment env
) : ControllerBase
{
    private readonly ILogger<AgreementsController> _logger = logger;
    private readonly EPRLiveDBContext _db = db;
    private readonly IWebHostEnvironment _env = env;

    [HttpGet]
    [Authorize]
    public IEnumerable<FullUnion> GetUnions([FromQuery] OptionalGuidQuery query)
    {
        int ChapterID = AuthUtils.GetCurrentChapterId(query, Request, _db, _env);
        return _db
            .Relationships.Where((r) => r.DrelationshipTypeId == 3 && r.LeftPartyId == ChapterID)
            .Join(
                _db.Organizations,
                r => r.RightPartyId,
                o => o.Id,
                (r, o) => new { ID = o.Id, Name = o.Name }
            )
            .Join(
                _db.Unions,
                o => o.ID,
                u => u.Id,
                (o, u) =>
                    new FullUnion
                    {
                        ID = o.ID,
                        Name = o.Name,
                        DefaultDelinquentDay = u.DefaultDelinquentDay
                    }
            )
            .ToList();
    }

    public class FullUnion
    {
        public int ID { get; set; }
        public required string Name { get; set; }
        public int DefaultDelinquentDay { get; set; }
    }
}
