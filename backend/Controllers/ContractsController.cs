using backend.Data.HttpQueries;
using backend.Data.Models;
using backend.Extensions;
using backend.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace backend.Controllers;

[Route("api/[controller]")]
[ApiController]
public class ContractsController(
    ILogger<ContractsController> logger,
    EPRLiveDBContext db,
    IWebHostEnvironment env
) : ControllerBase
{
    private readonly ILogger<ContractsController> _logger = logger;
    private readonly EPRLiveDBContext _db = db;
    private readonly IWebHostEnvironment _env = env;

    [HttpGet]
    [Authorize]
    public IEnumerable<ContractFull> GetContracts([FromQuery] OptionalGuidQuery query)
    {
        int ChapterID = AuthUtils.GetCurrentChapterId(query, Request, _db, _env);
        return _db
            .Contracts.Where((c) => c.ChapterId == ChapterID || c.ChapterId == null)
            .LeftJoin(
                _db.Benefits,
                c => c.NecabenefitId,
                b => b.Id,
                (c, b) =>
                    new
                    {
                        c.Id,
                        c.Name,
                        c.ChapterId,
                        NECABenefit = b.Name,
                        c.NecabenefitId,
                        c.AmfbenefitId
                    }
            )
            .LeftJoin(
                _db.Benefits,
                c => c.AmfbenefitId,
                b => b.Id,
                (c, b) =>
                    new ContractFull
                    {
                        ID = c.Id,
                        Name = c.Name,
                        ChapterID = c.ChapterId,
                        NECABenefit = c.NECABenefit,
                        NECABenefitID = c.NecabenefitId,
                        AMFBenefit = b.Name,
                        AMFBenefitID = c.AmfbenefitId
                    }
            )
            .ToList();
    }

    [HttpGet("agreements-to-contracts")]
    [Authorize]
    public IEnumerable<AgreementToContractFull> GetAgreementsToContracts(
        [FromQuery] OptionalGuidQuery query
    )
    {
        int ChapterID = AuthUtils.GetCurrentChapterId(query, Request, _db, _env);
        Guid ChapterGUID = _db.Roots.Where(r => r.Id == ChapterID).First().Guid!.Value;
        return _db.AgreementsToContractsFull.FromSql(
            $"Core.GetAgreementsToCategorires {ChapterGUID}"
        );
    }

    [HttpPost("update")]
    [Authorize]
    public IActionResult UpdateContracts([FromBody] UpdateContractsModel model)
    {
        foreach (ContractFull row in model.Contracts)
        {
            Contract? contract = _db.Contracts.Where((c) => c.Id == row.ID).FirstOrDefault();
            if (contract == null)
            {
                contract = new Contract
                {
                    Id = row.ID,
                    Name = row.Name,
                    ChapterId = row.ChapterID,
                    NecabenefitId = row.NECABenefitID,
                    AmfbenefitId = row.AMFBenefitID
                };
                _db.Contracts.Add(contract);
            }
            else
            {
                contract.Name = row.Name;
                contract.NecabenefitId = row.NECABenefitID;
                contract.AmfbenefitId = row.AMFBenefitID;
            }
        }
        _db.SaveChanges();
        return Ok();
    }

    [HttpPost]
    [Authorize]
    public IActionResult SaveContract([FromBody] ContractModel model)
    {
        int ChapterID = AuthUtils.GetCurrentChapterId(model, Request, _db, _env);
        Contract contract = new Contract
        {
            Name = model.Name,
            ChapterId = ChapterID,
            NecabenefitId = model.NECABenefitID,
            AmfbenefitId = model.AMFBenefitID
        };
        _db.Contracts.Add(contract);
        _db.SaveChanges();
        return Ok();
    }

    [HttpPost("agreements-to-contracts")]
    [Authorize]
    public IActionResult SaveAgreementsToContracts(
        [FromBody] AgreementsToContractsModel model
    )
    {
        foreach (AgreementToContractFull row in model.AgreementsToContracts)
        {
            AgreementsToContract? agreementToContract = _db
                .AgreementsToContracts.Where((a) => a.AgreementId == row.AgreementId)
                .FirstOrDefault();
            if (agreementToContract == null)
            {
                agreementToContract = new AgreementsToContract
                {
                    AgreementId = row.AgreementId,
                    ContractId = row.ContractId
                };
                _db.AgreementsToContracts.Add(agreementToContract);
            }
            else
            {
                agreementToContract.ContractId = row.ContractId;
            }
        }
        _db.SaveChanges();
        return Ok();
    }

    [HttpDelete]
    [Authorize]
    public IActionResult DeleteContract([FromQuery] IdQuery query)
    {
        Contract? ContractToDelete = _db.Contracts.Where((c) => c.Id == query.Id).FirstOrDefault();
        if (ContractToDelete == null)
        {
            return NotFound();
        }
        _db.AgreementsToContracts.RemoveRange(
            _db.AgreementsToContracts.Where((a) => a.ContractId == query.Id)
        );
        _db.Contracts.Remove(ContractToDelete);
        _db.SaveChanges();
        return Ok();
    }

    public class ContractFull
    {
        public int ID { get; set; }
        public string? Name { get; set; }
        public int? ChapterID { get; set; }
        public string? NECABenefit { get; set; }
        public int? NECABenefitID { get; set; }
        public string? AMFBenefit { get; set; }
        public int? AMFBenefitID { get; set; }
    }

    public class ContractModel : OptionalGuidQuery
    {
        public required string Name { get; set; }
        public int? NECABenefitID { get; set; }
        public int? AMFBenefitID { get; set; }
    }

    public class UpdateContractsModel
    {
        public required List<ContractFull> Contracts { get; set; }
    }

    public class AgreementsToContractsModel
    {
        public required List<AgreementToContractFull> AgreementsToContracts { get; set; }
    }
}
