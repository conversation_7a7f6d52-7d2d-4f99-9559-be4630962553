using System.Data;
using System.Reflection;
using backend.Data.DTOs;
using backend.Data.HttpQueries;
using backend.Data.Models;
using backend.Services;
using backend.Types.Outputs;
using backend.Types.Queries;
using backend.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Telerik.Windows.Documents.Spreadsheet.FormatProviders;
using static backend.Utils.Constants;

namespace backend.Controllers;

[Route("api/[controller]")]
[ApiController]
public class EmployersController(
    ILogger<EmployersController> logger,
    EPRLiveDBContext db,
    IWebHostEnvironment env,
    IChapterAuthorizationService authService,
    IHttpContextAccessor httpContextAccessor
) : ControllerBase
{
    private const int PRIMARY_CONTACT_RELATIONSHIP_TYPE_ID = 31;
    private const int PAYROLL_CONTACT_RELATIONSHIP_TYPE_ID = 32;
    private readonly ILogger<EmployersController> _logger = logger;
    private readonly EPRLiveDBContext _db = db;
    private readonly IWebHostEnvironment _env = env;
    private readonly IChapterAuthorizationService _authService = authService;
    private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;

    // Constants for payroll contact property names using nameof for maintainability
    private const string PAYROLL_CONTACT_FIRST_NAME = nameof(EmployerRosterView.PayrollContactFirstName);
    private const string PAYROLL_CONTACT_LAST_NAME = nameof(EmployerRosterView.PayrollContactLastName);
    private const string PAYROLL_CONTACT_COLUMN = "payrollContact";

    /// <summary>
    /// Gets the appropriate column name for display, using metadata display name if available
    /// </summary>
    /// <param name="column">The original column name</param>
    /// <param name="metadataFields">Dictionary of metadata fields</param>
    /// <returns>Display name if available in metadata, otherwise the original column name</returns>
    private static string GetColumnName(string column, Dictionary<string, KeyValuePair<string, Metadata>> metadataFields)
    {
        return metadataFields.TryGetValue(column, out var metadata) 
            ? metadata.Value.DisplayName 
            : column;
    }

    [HttpGet("contacts")]
    [Authorize]
    public EmployerContactInfo GetEmployerContactInfo([FromQuery] GuidQuery query)
    {
        int employerID = _db.Roots.Where((r) => r.Guid == query.GUID).First().Id;
        TfEmail? employerEmail = _db
            .TfEmails.FromSql(
                $"select * from Core.tfEmailAddresses() where PartyGUID = {query.GUID}"
            )
            .AsEnumerable()
            .FirstOrDefault();
        Relationship? primaryContact = _db
            .Relationships.Where(
                (r) =>
                    r.LeftPartyId == employerID
                    && r.DrelationshipTypeId == PRIMARY_CONTACT_RELATIONSHIP_TYPE_ID
            )
            .FirstOrDefault();
        TfEmail? primaryContactEmail = null;
        if (primaryContact != null)
        {
            primaryContactEmail = _db
                .TfEmails.FromSql(
                    $"select * from Core.tfEmailAddresses() where PartyID = {primaryContact.RightPartyId}"
                )
                .AsEnumerable()
                .FirstOrDefault();
        }
        Relationship? payrollContact = _db
            .Relationships.Where(
                (r) =>
                    r.LeftPartyId == employerID
                    && r.DrelationshipTypeId == PAYROLL_CONTACT_RELATIONSHIP_TYPE_ID
            )
            .FirstOrDefault();
        TfEmail? payrollContactEmail = null;
        if (payrollContact != null)
        {
            payrollContactEmail = _db
                .TfEmails.FromSql(
                    $"select * from Core.tfEmailAddresses() where PartyID = {payrollContact.RightPartyId}"
                )
                .AsEnumerable()
                .FirstOrDefault();
        }

        return new EmployerContactInfo
        {
            EmployerEmail = employerEmail?.EmailAddress,
            PrimaryContactEmail = primaryContactEmail?.EmailAddress,
            PayrollContactEmail = payrollContactEmail?.EmailAddress,
        };
    }

    [HttpGet("by-union")]
    [Authorize]
    public IEnumerable<EmployerSimpleId> GetEmployersByUnion([FromQuery] EmployerByUnionQuery query)
    {
        int chapterId = AuthUtils.GetCurrentChapterId(query, Request, _db, _env);
        return _db.EmployersSimpleId.FromSqlRaw(
            "exec [Core].[EmployersByUnionSelect] @employerRequest, @chapterID",
            new SqlParameter("@employerRequest", query.EmployerXml),
            new SqlParameter("@chapterID", chapterId)
        );
    }

    [HttpGet("export-employer-roster")]
    [Authorize]
    public async Task<IActionResult> ExportEmployerRoster(
        [FromQuery] ExportEmployerRosterQuery query
    )
    {
        var fileType = Enum.Parse<FileTypes>(query.FileFormat, true);
        var data = await EmployerRosterQuery.GetEmployerRosterByChapterId(
            query.ChapterId,
            _db,
            _authService,
            _httpContextAccessor
        );

        var table = CreateDataTable<EmployerRosterView>(data.OrderBy(x => x.Name), query.Columns);

        var provider = new DataTableFormatProvider();
        var workbook = provider.Import(table);
        var fileName = $"EmployerRoster{DateTime.Now.ToString("yyyyMMdd")}";

        return ExportHelper.ExportData(fileName, fileType, workbook);
    }

    [HttpGet("export-benefit-elections")]
    [Authorize]
    public async Task<IActionResult> ExportBenefitElections(
        [FromQuery] ExportEmployerRosterQuery query,
        CancellationToken cancellationToken
    )
    {
        var fileType = Enum.Parse<FileTypes>(query.FileFormat, true);
        var data = await BenefitElectionsRoster.GetBenefitElectionRosterByChapterIdAsync(
            query.ChapterId,
            _db,
            cancellationToken
        );

        var table = CreateDataTable<BenefitElectionsRosterDto>(
            data.OrderBy(x => x.EmployerName).AsQueryable(),
            query.Columns
        );

        var provider = new DataTableFormatProvider();
        var workbook = provider.Import(table);
        var fileName = $"BenefitElections{DateTime.Now.ToString("yyyyMMdd")}";

        return ExportHelper.ExportData(fileName, fileType, workbook);
    }

    [HttpGet("export-timesheets")]
    [Authorize]
    public async Task<IActionResult> ExportTimesheets(
        [FromQuery] ExportTimesheetsQuery query,
        CancellationToken cancellationToken
    )
    {
        var fileType = Enum.Parse<FileTypes>(query.FileFormat, true);
        var data = await TimesheetsQuery.GetTimesheetsByEmployerGuidAsync(
            query.EmployerId,
            _db,
            cancellationToken
        );

        //ToDo: Hardcoded limit will be removed later!
        var table = CreateDataTable<TimeSheet>(data.Take(10000), query.Columns);

        var provider = new DataTableFormatProvider();
        var workbook = provider.Import(table);

        var fileName = $"Timesheets{DateTime.Now.ToString("yyyyMMdd")}";

        return ExportHelper.ExportData(fileName, fileType, workbook);
    }

    private DataTable CreateDataTable<T>(IQueryable<T> data, string[] columns)
    {
        var table = new DataTable();

        // Get all metadata fields using MetadataQuery.GetFieldDefinitions()
        var metadataFields = MetadataQuery
            .GetFieldDefinitions()
            .ToDictionary(m => m.Value.FieldName, StringComparer.OrdinalIgnoreCase);

        // Add columns - use metadata display name if available, otherwise use column name directly
        foreach (var column in columns)
        {
            table.Columns.Add(GetColumnName(column, metadataFields));
        }

        foreach (var item in data)
        {
            var row = table.NewRow();
            foreach (var column in columns)
            {
                // Handle special case for computed payrollContact field
                if (string.Equals(column, PAYROLL_CONTACT_COLUMN, StringComparison.OrdinalIgnoreCase))
                {
                    // Use helper methods and constants for maintainable property access
                    var firstName = ReflectionUtils.GetPropertyValueAsString(item, PAYROLL_CONTACT_FIRST_NAME);
                    var lastName = ReflectionUtils.GetPropertyValueAsString(item, PAYROLL_CONTACT_LAST_NAME);
                    var fullName = FormatUtilities.FormatFullName(firstName, lastName);

                    string columnName = GetColumnName(column, metadataFields);

                    row[columnName] = string.IsNullOrEmpty(fullName) ? DBNull.Value : fullName;
                    continue;
                }

                var property = item?.GetType()
                    ?.GetProperty(
                        column,
                        BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance
                    );

                if (property != null)
                {
                    var value = property.GetValue(item, null);

                    // Find the column name to use in the table
                    string columnName = column;
                    if (metadataFields.TryGetValue(column, out var metadata))
                    {
                        columnName = metadata.Value.DisplayName;

                        // Format the value if there's a display format and the value is not null
                        if (
                            !string.IsNullOrEmpty(metadata.Value.DisplayFormat)
                            && value is DateTime dateValue
                        )
                        {
                            row[columnName] = dateValue.ToString(metadata.Value.DisplayFormat);
                            continue;
                        }
                    }

                    row[columnName] = value ?? DBNull.Value;
                }
                else
                {
                    // Property not found, set to DBNull
                    string columnName = GetColumnName(column, metadataFields);
                    row[columnName] = DBNull.Value;
                }
            }
            table.Rows.Add(row);
        }

        return table;
    }

    public class EmployerContactInfo
    {
        public string? EmployerEmail { get; set; }
        public string? PrimaryContactEmail { get; set; }
        public string? PayrollContactEmail { get; set; }
    }

    public class ExportTimesheetsQuery
    {
        public Guid EmployerId { get; set; }
        public required string[] Columns { get; set; }
        public required string FileFormat { get; set; }
    }

    public class ExportEmployerRosterQuery
    {
        public int ChapterId { get; set; }
        public required string[] Columns { get; set; }
        public required string FileFormat { get; set; }
    }

    public class EmployerByUnionQuery : OptionalGuidQuery
    {
        public required string EmployerXml { get; set; }
    }
}
