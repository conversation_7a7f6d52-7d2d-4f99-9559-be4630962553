using System.Data;
using backend.Data.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;

namespace backend.Controllers;

[Route("api/[controller]")]
[ApiController]
public class OregonPacificCascadeNecaController(
    ILogger<OregonPacificCascadeNecaController> logger,
    EPRLiveDBContext db
) : ControllerBase
{
    private readonly ILogger<OregonPacificCascadeNecaController> _logger = logger;
    private readonly EPRLiveDBContext _db = db;

    [HttpPost]
    [Authorize]
    public IEnumerable<OregonPacificReportRow> GetOregonPacificReportEntries(
        [FromBody] OregonPacificReportBody body
    )
    {
        var startDateParam = new SqlParameter("@Start_date", SqlDbType.Date)
        {
            Value = body.StartDate,
        };
        var endDateParam = new SqlParameter("@End_date", SqlDbType.Date) { Value = body.EndDate };
        var employerIdsParam = new SqlParameter("@employerIds", SqlDbType.VarChar)
        {
            Value = body.EmployerIds,
        };
        var agreementIdsParam = new SqlParameter("@agreementIds", SqlDbType.VarChar)
        {
            Value = body.AgreementIds,
        };
        var classificationIdsParam = new SqlParameter("@classificationIds", SqlDbType.VarChar)
        {
            Value = body.ClassificationIds,
        };
        var benefitIdParam = new SqlParameter("@benefitId", SqlDbType.Int)
        {
            Value = body.BenefitId,
        };

        return _db.OregonPacificReportRows.FromSqlRaw(
            "exec [Custom].[Oregon_Pacific_Cascade_Neca_Status_Report] @Start_date, @End_date, @employerIds, @agreementIds, @classificationIds, @benefitId",
            startDateParam,
            endDateParam,
            employerIdsParam,
            agreementIdsParam,
            classificationIdsParam,
            benefitIdParam
        );
    }

    public class OregonPacificReportBody
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public required string EmployerIds { get; set; }
        public required string AgreementIds { get; set; }
        public required string ClassificationIds { get; set; }
        public int BenefitId { get; set; }
    }
}
