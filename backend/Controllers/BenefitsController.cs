using System.Data;
using backend.Data.HttpQueries;
using backend.Data.Models;
using backend.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;

namespace backend.Controllers;

[Route("api/[controller]")]
[ApiController]
public class BenefitsController(
    ILogger<BenefitsController> logger,
    EPRLiveDBContext db,
    IWebHostEnvironment env
) : ControllerBase
{
    private readonly ILogger<BenefitsController> _logger = logger;
    private readonly EPRLiveDBContext _db = db;
    private readonly IWebHostEnvironment _env = env;

    [HttpGet]
    [Authorize]
    public IEnumerable<Benefit> GetBenefits([FromQuery] OptionalGuidQuery query)
    {
        int ChapterID = AuthUtils.GetCurrentChapterId(query, Request, _db, _env);
        return _db.Benefits.Where((b) => b.ChapterId == ChapterID || b.ChapterId == null);
    }

    [HttpGet("neca-amf-rates")]
    [Authorize]
    public IEnumerable<BenefitToContract> GetBenefitsForNecaAmfRateSchedule(
        [FromQuery] OptionalGuidQuery query
    )
    {
        int ChapterID = AuthUtils.GetCurrentChapterId(query, Request, _db, _env);
        var chapterIdParam = new SqlParameter("@ChapterID", SqlDbType.Int) { Value = ChapterID };

        return _db
            .BenefitToContracts.FromSqlRaw(
                "select Benefits.ID BenefitID, Contracts.ID ContractID, Benefits.Name Benefit, Contracts.Name Contract "
                    + "from Core.Contracts inner join Core.Benefits on Benefits.ID = NECABenefitID OR Benefits.ID = AMFBenefitID "
                    + "where Contracts.ChapterID = @ChapterID",
                chapterIdParam
            )
            .ToList();
    }

    [HttpPost("by-agreement")]
    [Authorize]
    public IEnumerable<BenefitSimpleId> GetBenefitsByAgreement(
        [FromBody] BenefitsByAgreementBody body
    )
    {
        var agreementsParam = new SqlParameter("@agreements", SqlDbType.VarChar)
        {
            Value = body.agreements,
        };

        return _db.BenefitsSimpleId.FromSqlRaw(
            "exec [Core].[BenefitsByAgreementsSelect] @agreements",
            agreementsParam
        );
    }

    public class BenefitsByAgreementBody
    {
        public required string agreements { get; set; }
    }
}
