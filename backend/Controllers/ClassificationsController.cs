using backend.Data.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;

namespace backend.Controllers;

[Route("api/[controller]")]
[ApiController]
public class ClassificationsController : ControllerBase
{
    private readonly ILogger<ClassificationsController> _logger;
    private readonly EPRLiveDBContext _db;

    public ClassificationsController(ILogger<ClassificationsController> logger, EPRLiveDBContext db)
    {
        _logger = logger;
        _db = db;
    }

    [HttpGet("agreement/{agreementId}")]
    [Authorize]
    public IEnumerable<ClassificationName> GetAgreementClassifications(int agreementId)
    {
        return _db
            .AgreementClassifications.Where(a => a.AgreementId == agreementId)
            .Join(
                _db.ClassificationNames,
                agreementClassification => agreementClassification.ClassificationNameId,
                classification => classification.Id,
                (agreementClassification, classification) => classification
            )
            .Distinct()
            .ToList();
    }

    [HttpGet("agreement/{agreementId}/subclassifications/{classificationId}")]
    [Authorize]
    public IEnumerable<SubClassification> GetAgreementSubClassifications(
        int agreementId,
        int classificationId
    )
    {
        return _db
            .AgreementClassifications.Where(a =>
                a.AgreementId == agreementId && a.ClassificationNameId == classificationId
            )
            .Join(
                _db.SubClassifications,
                agreementClassification => agreementClassification.SubClassificationId,
                subClassification => subClassification.Id,
                (agreementClassificaiton, subClassification) => subClassification
            )
            .Distinct()
            .ToList();
    }

    // While it would be more semantically consistent for this to be a GET, it's possible for the number of agreement IDs being sent to be so large that
    // the query string length causes a network error
    [HttpPost("by-agreement")]
    [Authorize]
    public IEnumerable<ClassificationSimpleId> GetClassificationsByAgreement(
        [FromBody] ClassificationsByAgreementBody query
    )
    {
        return _db.ClassificationsSimpleId.FromSqlRaw(
            "exec [Core].[ClassificationsByAgreementsSelect] @agreements",
            new SqlParameter("@agreements", query.agreements)
        );
    }

    public class ClassificationsByAgreementBody
    {
        public required string agreements { get; set; }
    }
}
