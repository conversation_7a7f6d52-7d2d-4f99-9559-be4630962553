﻿using System.Globalization;
using backend.Data.Models;
using backend.Types.Outputs;
using backend.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Telerik.Windows.Documents.Spreadsheet.Model;
using static backend.Utils.Constants;

namespace backend.Controllers;

[Route("api/[controller]")]
[ApiController]
public class FundAdministrationController(
    ILogger<FundAdministrationController> logger,
    EPRLiveDBContext db,
    IWebHostEnvironment env,
    IMemoryCache memoryCache
) : ControllerBase
{
    private readonly ILogger<FundAdministrationController> _logger = logger;
    private readonly EPRLiveDBContext _db = db;
    private readonly IWebHostEnvironment _env = env;
    private readonly IMemoryCache _memoryCache = memoryCache;

    [HttpGet("export-download-contributions")]
    [Authorize]
    public async Task<IActionResult> ExportDownloadContributions(
        [FromQuery] ExportDownloadContributionsQuery query,
        CancellationToken cancellationToken
    )
    {
        try
        {
            // Generate filename based on date range - defined early to use in cache key
            var fileName =
                $"Reported-Contributions-{query.StartDate:yyyy-MM-dd}to{query.EndDate:yyyy-MM-dd}";
            var fileType = Enum.Parse<FileTypes>(query.FileFormat, true);
            query.FundAdministratorID = 59892846; // Temporarily hard code FundAdministratorID to Service Center 683 / ID: 59892846

            // Attempt to get cached results for the same date range and format
            string cacheKey =
                $"ExportContributions_{query.FundAdministratorID}_{query.StartDate}_{query.EndDate}_{query.FileFormat}";
            if (_memoryCache.TryGetValue(cacheKey, out byte[]? cachedData) && cachedData != null)
            {
                _logger.LogInformation("Returning cached result for {CacheKey}", cacheKey);
                return ExportHelper.GetFileContentResult(cachedData, fileName, fileType);
            }

            // Get data from database
            var data = await GetDownloadContributions(
                query.StartDate,
                query.EndDate,
                query.FundAdministratorID,
                cancellationToken
            );

            // Materialize the data immediately to avoid multiple enumeration
            var materializedData = data.ToList();


            // Define columns using FieldDefinitions
            var columns = new Metadata[]
            {
                Fields.Type,
                Fields.Union,
                Fields.Agreement,
                Fields.FEINSSN,
                Fields.Name,
                Fields.WorkMonth,
                Fields.SubmissionDate,
                Fields.FundingDate,
                Fields.PayrollFromDate,
                Fields.PayrollThruDate,
                Fields.STHours,
                Fields.OTHours,
                Fields.DTHours,
                Fields.HoursWorked,
                Fields.Fund,
                Fields.Classification,
                Fields.EmployeeCount,
                Fields.Payment,
                Fields.GrossWages,
                Fields.Rate,
                Fields.Calculation,
            };

            // Create a simplified DataTable without custom formatting
            var table = new System.Data.DataTable();

            // Add columns
            foreach (var column in columns)
            {
                table.Columns.Add(column.DisplayName, typeof(string));
            }

            // Add rows as plain text values with safe null handling
            foreach (var item in materializedData)
            {
                var row = table.NewRow();
                row[Fields.Type.DisplayName] = item.RecordType.ToString();
                row[Fields.Union.DisplayName] = item.Union ?? string.Empty;
                row[Fields.Agreement.DisplayName] = item.Agreement ?? string.Empty;
                row[Fields.FEINSSN.DisplayName] = item.FEINSSN ?? string.Empty;
                row[Fields.Name.DisplayName] = item.Name ?? string.Empty;
                row[Fields.WorkMonth.DisplayName] = item.WorkMonth ?? string.Empty;
                row[Fields.SubmissionDate.DisplayName] = item.SubmissionDate.ToString(
                    Fields.SubmissionDate.DisplayFormat,
                    CultureInfo.InvariantCulture
                );
                row[Fields.FundingDate.DisplayName] = item.FundingDate.ToString(
                    Fields.FundingDate.DisplayFormat,
                    CultureInfo.InvariantCulture
                );
                row[Fields.PayrollFromDate.DisplayName] = item.PayrollFromDate.ToString(
                    Fields.PayrollFromDate.DisplayFormat,
                    CultureInfo.InvariantCulture
                );
                row[Fields.PayrollThruDate.DisplayName] = item.PayrollThruDate.ToString(
                    Fields.PayrollThruDate.DisplayFormat,
                    CultureInfo.InvariantCulture
                );
                row[Fields.STHours.DisplayName] = item.STHours?.ToString() ?? string.Empty;
                row[Fields.OTHours.DisplayName] = item.OTHours?.ToString() ?? string.Empty;
                row[Fields.DTHours.DisplayName] = item.DTHours?.ToString() ?? string.Empty;
                row[Fields.HoursWorked.DisplayName] = item.HoursWorked?.ToString() ?? string.Empty;
                row[Fields.Fund.DisplayName] = item.Fund ?? string.Empty;
                row[Fields.Classification.DisplayName] = item.Classification ?? string.Empty;
                row[Fields.EmployeeCount.DisplayName] =
                    item.EmployeeCount?.ToString() ?? string.Empty;
                row[Fields.Payment.DisplayName] = item.Payment?.ToString() ?? string.Empty;
                row[Fields.GrossWages.DisplayName] = item.GrossWages?.ToString() ?? string.Empty;
                row[Fields.Rate.DisplayName] = item.Rate?.ToString() ?? string.Empty;
                row[Fields.Calculation.DisplayName] = item.Calculation ?? string.Empty;
                table.Rows.Add(row);
            }

            if (fileType == FileTypes.Csv)
            {
                // Export CSV using CsvHelper for better performance

                return ExportHelper.ExportData(fileName, fileType, table, cacheKey, _memoryCache);
            }

            // Create a simplified workbook with direct writing
            var workbook = new Workbook();
            var worksheet = workbook.Worksheets.Add();

            // Write header row
            for (int i = 0; i < columns.Length; i++)
            {
                worksheet.Cells[0, i].SetValue(columns[i].DisplayName);
            }

            // Write data rows
            for (int rowIndex = 0; rowIndex < table.Rows.Count; rowIndex++)
            {
                var dataRow = table.Rows[rowIndex];
                for (int colIndex = 0; colIndex < columns.Length; colIndex++)
                {
                    worksheet
                        .Cells[rowIndex + 1, colIndex]
                        .SetValue(dataRow[colIndex]?.ToString() ?? string.Empty);
                }
            }

            return ExportHelper.ExportData(fileName, fileType, workbook, cacheKey, _memoryCache);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting download contributions");
            throw;
        }
    }

    private async Task<List<DownloadContribution>> GetDownloadContributions(
        DateOnly startDate,
        DateOnly endDate,
        int fundAdministratorID,
        CancellationToken cancellationToken
    )
    {
        // Use the provided dbContext which is already configured
        await _db.Database.OpenConnectionAsync();

        try
        {
            await _db.Database.ExecuteSqlRawAsync("EXEC Core.OpenSymmetricKey");

            var contributions = await _db
                .DownloadContributions.Where(x =>
                    (
                        x.FundAdministratorID == fundAdministratorID
                        || x.CollectingAgentID == fundAdministratorID
                    )
                    && (x.FundingDate >= startDate && x.FundingDate < endDate.AddDays(1))
                    && (x.SubmissionDate >= startDate && x.SubmissionDate < endDate.AddDays(1))
                )
                .OrderBy(x => x.EmployerName)
                .ThenBy(x => x.Agreement)
                .ThenBy(x => x.WorkMonth)
                .ThenBy(x => x.ReportId)
                .ThenBy(x => x.RecordType)
                .ThenBy(x => x.RecordType == 2 ? x.Name : null)
                .ThenBy(x => x.Fund)
                .ToListAsync(cancellationToken);

            await _db.Database.ExecuteSqlRawAsync("EXEC Core.CloseSymmetricKey");

            return contributions;
        }
        finally
        {
            _db.Database.CloseConnection();
        }
    }

    public class ExportDownloadContributionsQuery
    {
        public required DateOnly StartDate { get; set; }
        public required DateOnly EndDate { get; set; }
        public required string FileFormat { get; set; }
        public required int FundAdministratorID { get; set; }
    }

    public class ColumnProperty
    {
        public required string Name { get; set; }

        public string? DataFormat { get; set; }

        /// <summary>
        /// The format to display the value in the exported file, if it differs from the data format like currency.
        /// </summary>
        public string? DisplayFormat { get; set; }
    }
}
