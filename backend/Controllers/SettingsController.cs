using System.Collections.Generic;
using System.Linq;
using backend.Data.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace backend.Controllers;

[Route("api/[controller]")]
[ApiController]
public class SettingsController : ControllerBase
{
    private readonly ILogger<SettingsController> _logger;
    private readonly EPRLiveDBContext _db;

    public SettingsController(ILogger<SettingsController> logger, EPRLiveDBContext db)
    {
        _logger = logger;
        _db = db;
    }

    [HttpGet("employeeDefaults")]
    [Authorize]
    public IEnumerable<Setting>? GetEmployeeDefaults(int? employeeId)
    {
        if (employeeId != null)
        {
            var settings = (
                from s in _db.Settings
                join r in _db.Roots on s.OwnerId equals r.Guid
                where r.Id == employeeId
                select s
            );
            return settings;
        }
        return null;
    }

    /// <summary>
    /// Gets employee default settings for multiple employees in a single request
    /// </summary>
    /// <param name="employeeIds">List of employee IDs to fetch settings for</param>
    /// <returns>Dictionary mapping employee IDs to their settings</returns>
    [HttpPost("employeeDefaultsBatch")]
    [Authorize]
    public ActionResult<Dictionary<int, IEnumerable<Setting>>> GetEmployeeDefaultsBatch(
        [FromBody] List<int> employeeIds
    )
    {
        if (employeeIds == null || !employeeIds.Any())
        {
            _logger.LogDebug("GetEmployeeDefaultsBatch called with null or empty employeeIds list.");
            return Ok(new Dictionary<int, IEnumerable<Setting>>());
        }

        // Input validation for positive IDs
        if (employeeIds.Any(id => id <= 0))
        {
            _logger.LogWarning("GetEmployeeDefaultsBatch called with one or more non-positive employee IDs. Request: {EmployeeIds}", string.Join(", ", employeeIds));
            return BadRequest("All employee IDs must be positive integers.");
        }

        var distinctEmployeeIds = employeeIds.Distinct().ToList();
        var result = new Dictionary<int, IEnumerable<Setting>>();

        // Query all settings for the requested employee IDs in a single database query
        var allSettings = (
            from s in _db.Settings
            join r in _db.Roots on s.OwnerId equals r.Guid
            where distinctEmployeeIds.Contains(r.Id) // Use distinct IDs for the query
            select new { EmployeeId = r.Id, Setting = s }
        ).ToList();

        // Identify IDs from the input list that were not found in the database query results
        var foundDbEmployeeIds = allSettings.Select(s => s.EmployeeId).Distinct().ToHashSet();
        var notFoundOrNoSettingsIds = distinctEmployeeIds.Except(foundDbEmployeeIds).ToList();

        if (notFoundOrNoSettingsIds.Any())
        {
            _logger.LogWarning("GetEmployeeDefaultsBatch: The following requested employee IDs were not found in the system or have no associated settings: {NotFoundIds}", string.Join(", ", notFoundOrNoSettingsIds));
        }

        // Group settings by employee ID
        foreach (var employeeId in distinctEmployeeIds) // Iterate over distinct requested IDs
        {
            var employeeSettings = allSettings
                .Where(x => x.EmployeeId == employeeId)
                .Select(x => x.Setting)
                .ToList();

            result[employeeId] = employeeSettings;
        }

        return Ok(result);
    }
}
