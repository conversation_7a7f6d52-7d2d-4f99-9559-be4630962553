using System.Data;
using backend.Data.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;

namespace backend.Controllers;

[Route("api/[controller]")]
[ApiController]
public class CommentsController(ILogger<CommentsController> logger, EPRLiveDBContext db)
    : ControllerBase
{
    private readonly ILogger<CommentsController> _logger = logger;
    private readonly EPRLiveDBContext _db = db;

    [HttpPost]
    [Authorize]
    public IActionResult SaveComment([FromBody] CommentSaveModel model)
    {
        int OrgID = _db.Roots.Where((r) => r.Guid == model.OrganizationID).First().Id;

        var reportIdParam = new SqlParameter("@ReportID", SqlDbType.Int) { Value = model.ReportID };
        var benefitIdParam = new SqlParameter("@BenefitID", SqlDbType.Int)
        {
            Value = model.BenefitID,
        };
        var userNameParam = new SqlParameter("@UserName", SqlDbType.VarChar)
        {
            Value = model.Username ?? (object)DBNull.Value,
        };
        var commentParam = new SqlParameter("@Comment", SqlDbType.VarChar)
        {
            Value = model.Comment ?? (object)DBNull.Value,
        };
        var orgIdParam = new SqlParameter("@OrganizationID", SqlDbType.Int) { Value = OrgID };
        var agreementIdParam = new SqlParameter("@AgreementID", SqlDbType.Int)
        {
            Value = model.AgreementID,
        };
        var employerIdParam = new SqlParameter("@EmployerID", SqlDbType.Int)
        {
            Value = model.EmployerID,
        };
        var workMonthParam = new SqlParameter("@WorkMonth", SqlDbType.DateTime)
        {
            Value = model.WorkMonth,
        };

        _db.Database.ExecuteSqlRaw(
            "exec [Core].[FundingCommentsInsert] @ReportID, @BenefitID, @UserName, @Comment, @OrganizationID, @AgreementID, @EmployerID, @WorkMonth",
            reportIdParam,
            benefitIdParam,
            userNameParam,
            commentParam,
            orgIdParam,
            agreementIdParam,
            employerIdParam,
            workMonthParam
        );

        return Ok();
    }

    public class CommentSaveModel
    {
        public int ReportID { get; set; }
        public int BenefitID { get; set; }
        public required string Username { get; set; }
        public string? Comment { get; set; }
        public Guid OrganizationID { get; set; }
        public int AgreementID { get; set; }
        public int EmployerID { get; set; }
        public DateTime WorkMonth { get; set; }
    }
}
