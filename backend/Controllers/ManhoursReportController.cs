using backend.Data.HttpQueries;
using backend.Data.Models;
using backend.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;

namespace backend.Controllers;

[Route("api/[controller]")]
[ApiController]
public class ManhoursReportController(
    ILogger<ManhoursReportController> logger,
    EPRLiveDBContext db,
    IWebHostEnvironment env
) : ControllerBase
{
    private readonly ILogger<ManhoursReportController> _logger = logger;
    private readonly EPRLiveDBContext _db = db;
    private readonly IWebHostEnvironment _env = env;

    [HttpPost]
    [Authorize]
    public IEnumerable<ManhoursReportRow> GetManhoursReportEntries(
        [FromBody] ManhoursReportBody body
    )
    {
        int chapterId = AuthUtils.GetCurrentChapterId(body, Request, _db, _env);
        return _db.ManhoursReportRows.FromSqlRaw(
            "exec [Custom].[Manhours_Report] @chapterID, @start_date, @end_date, @employerIds, @agreementIds, @classificationIds, @include_paper_reports",
            new SqlParameter("@chapterID", chapterId),
            new SqlParameter("@start_date", body.startDate),
            new SqlParameter("@end_date", body.endDate),
            new SqlParameter("@employerIds", body.employerIds ?? (object)DBNull.Value),
            new SqlParameter("@agreementIds", body.agreementIds ?? (object)DBNull.Value),
            new SqlParameter("@classificationIds", body.classificationIds ?? (object)DBNull.Value),
            new SqlParameter("@include_paper_reports", body.includePaperReports)
        );
    }

    public class ManhoursReportBody : OptionalGuidQuery
    {
        public DateTime startDate { get; set; }
        public DateTime endDate { get; set; }
        public required string employerIds { get; set; }
        public required string agreementIds { get; set; }
        public required string classificationIds { get; set; }
        public bool includePaperReports { get; set; }
    }
}
