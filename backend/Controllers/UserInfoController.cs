using backend.Data.DTOs;
using backend.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace backend.Controllers;

[Route("api/[controller]")]
[ApiController]
public class UserInfoController : ControllerBase
{
    private readonly ILogger<UserInfoController> _logger;
    private readonly IUserInfoService _userInfoService;

    public UserInfoController(
        ILogger<UserInfoController> logger,
        IUserInfoService userInfoService
    )
    {
        _logger = logger;
        _userInfoService = userInfoService;
    }

    [HttpGet]
    [Authorize]
    public async Task<UserInfoDto> GetUserInfo(
        [FromQuery] string? username = null,
        CancellationToken cancellationToken = default
    )
    {
        return await _userInfoService.GetUserInfoAsync(username, Request, cancellationToken);
    }
}
