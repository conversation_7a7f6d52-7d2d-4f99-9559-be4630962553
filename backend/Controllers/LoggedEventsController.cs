using backend.Data.HttpQueries;
using backend.Data.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace backend.Controllers;

[Route("api/[controller]")]
[ApiController]
public class LoggedEventsController(ILogger<LoggedEventsController> logger, EPRLiveDBContext db)
    : ControllerBase
{
    private readonly ILogger<LoggedEventsController> _logger = logger;
    private readonly EPRLiveDBContext _db = db;

    [HttpPost]
    [Authorize]
    public IActionResult SaveLoggedEvent([FromBody] LoggedEventModel model)
    {
        _db.LoggedEvents.Add(
            new LoggedEvent
            {
                EventDateTime = model.EventDateTime,
                DeventTypeId = model.DEventTypeID,
                DeventSubTypeId = model.DEventSubTypeID,
                UserName = model.UserName,
                Target = model.Target,
                Notes = model.Notes,
                SessionId = model.SessionID,
            }
        );
        _db.SaveChanges();
        return Ok();
    }
}
