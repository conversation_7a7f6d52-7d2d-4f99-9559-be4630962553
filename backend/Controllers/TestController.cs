using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace backend.Controllers
{
    /// <summary>
    /// Test controller for debugging and verifying authentication/authorization flow
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class TestController : ControllerBase
    {
        private readonly ILogger<TestController> _logger;

        public TestController(ILogger<TestController> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Endpoint to verify claims are properly flowing from introspection to HttpContext
        /// This helps debug the OpenIddict introspection and claims-based authorization
        /// </summary>
        [HttpGet("claims")]
        public IActionResult GetClaims()
        {
            try
            {
                var claims = User.Claims.Select(c => new 
                { 
                    Type = c.Type, 
                    Value = c.Value 
                }).ToList();
                
                var orgIdClaim = User.FindFirst("org_id");
                var orgGuidClaim = User.FindFirst("org_guid");
                var permissionClaim = User.FindFirst("permission");
                
                var result = new 
                {
                    IsAuthenticated = User.Identity?.IsAuthenticated ?? false,
                    AuthenticationType = User.Identity?.AuthenticationType,
                    Name = User.Identity?.Name,
                    Claims = claims,
                    ClaimCount = claims.Count,
                    
                    // Custom EPR Live claims
                    HasOrgId = orgIdClaim != null,
                    OrgId = orgIdClaim?.Value,
                    HasOrgGuid = orgGuidClaim != null,
                    OrgGuid = orgGuidClaim?.Value,
                    HasPermission = permissionClaim != null,
                    Permission = permissionClaim?.Value,
                    
                    // Standard claims
                    Subject = User.FindFirst("sub")?.Value,
                    ClientId = User.FindFirst("client_id")?.Value,
                    Scope = User.FindFirst("scope")?.Value,
                    Expiration = User.FindFirst("exp")?.Value,
                    
                    // Role information
                    Roles = User.Claims.Where(c => c.Type == "role").Select(c => c.Value).ToList(),
                    
                    // Timestamp for debugging
                    Timestamp = DateTime.UtcNow,
                    
                    // Request information
                    RequestHeaders = new
                    {
                        Authorization = Request.Headers.Authorization.ToString(),
                        UserAgent = Request.Headers.UserAgent.ToString()
                    }
                };
                
                _logger.LogInformation("Claims test endpoint called - OrgId: {OrgId}, ClaimCount: {ClaimCount}", 
                    result.OrgId, result.ClaimCount);
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving claims information");
                return StatusCode(500, new { Error = "Failed to retrieve claims", Message = ex.Message });
            }
        }

        /// <summary>
        /// Simple health check endpoint that doesn't require authentication
        /// </summary>
        [HttpGet("health")]
        [AllowAnonymous]
        public IActionResult Health()
        {
            return Ok(new 
            { 
                Status = "OK", 
                Timestamp = DateTime.UtcNow,
                Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown"
            });
        }

        /// <summary>
        /// Endpoint to test authorization for a specific chapter/organization
        /// </summary>
        [HttpGet("authorization/{chapterId:int}")]
        public IActionResult TestAuthorization(int chapterId)
        {
            try
            {
                var orgIdClaim = User.FindFirst("org_id");
                var isSystemAdmin = User.IsInRole("System Administrator");
                
                // Check all role-related claims
                var roleClaims = User.Claims.Where(c => c.Type.Contains("role")).ToList();
                var allRoles = User.Claims.Where(c => c.Type == "role" || c.Type == "http://schemas.microsoft.com/ws/2008/06/identity/claims/role").Select(c => c.Value).ToList();
                
                var result = new
                {
                    ChapterId = chapterId,
                    UserOrgId = orgIdClaim?.Value,
                    IsSystemAdmin = isSystemAdmin,
                    IsAuthorized = false,
                    AuthorizationMethod = "Unknown",
                    
                    // Debug information
                    AllRoleClaims = roleClaims.Select(c => new { Type = c.Type, Value = c.Value }).ToList(),
                    AllRoles = allRoles,
                    TokenId = User.FindFirst("jti")?.Value,
                    TokenIssued = User.FindFirst("iat")?.Value
                };

                if (isSystemAdmin)
                {
                    result = result with { IsAuthorized = true, AuthorizationMethod = "System Administrator Role" };
                }
                else if (orgIdClaim != null && int.TryParse(orgIdClaim.Value, out var userOrgId))
                {
                    var isAuthorized = userOrgId == chapterId;
                    result = result with { IsAuthorized = isAuthorized, AuthorizationMethod = "Organization ID Match" };
                }
                else
                {
                    result = result with { AuthorizationMethod = "No valid organization claim" };
                }
                
                _logger.LogInformation("Authorization test - Chapter: {ChapterId}, UserOrg: {UserOrg}, Authorized: {Authorized}, Roles: {Roles}", 
                    chapterId, result.UserOrgId, result.IsAuthorized, string.Join(", ", allRoles));
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing authorization for chapter {ChapterId}", chapterId);
                return StatusCode(500, new { Error = "Authorization test failed", Message = ex.Message });
            }
        }

        /// <summary>
        /// Endpoint to verify the introspection configuration is working
        /// </summary>
        [HttpGet("introspection-info")]
        public IActionResult GetIntrospectionInfo()
        {
            try
            {
                var result = new
                {
                    // Token information
                    HasToken = Request.Headers.Authorization.Any(),
                    AuthorizationHeader = Request.Headers.Authorization.FirstOrDefault()?.StartsWith("Bearer ") == true,
                    
                    // Claims summary
                    ClaimsPresent = User.Claims.Any(),
                    CustomClaimsPresent = User.FindFirst("org_id") != null || User.FindFirst("org_guid") != null,
                    
                    // EPR Live specific checks
                    Phase1Verification = new
                    {
                        OrgIdPresent = User.FindFirst("org_id") != null,
                        OrgGuidPresent = User.FindFirst("org_guid") != null,
                        PermissionPresent = User.FindFirst("permission") != null,
                        StandardClaimsPresent = User.FindFirst("sub") != null
                    },
                    
                    // Recommendations
                    NextSteps = GetNextStepsRecommendations()
                };
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting introspection info");
                return StatusCode(500, new { Error = "Failed to get introspection info", Message = ex.Message });
            }
        }

        private string[] GetNextStepsRecommendations()
        {
            var recommendations = new List<string>();
            
            if (User.FindFirst("org_id") == null)
            {
                recommendations.Add("org_id claim missing - check token creation and destinations configuration");
            }
            
            if (User.FindFirst("sub") == null)
            {
                recommendations.Add("Standard claims missing - check introspection configuration");
            }
            
            if (!User.Claims.Any())
            {
                recommendations.Add("No claims present - check authentication middleware configuration");
            }
            
            if (recommendations.Count == 0)
            {
                recommendations.Add("All checks passed - ready for Phase 3 implementation");
            }
            
            return recommendations.ToArray();
        }
    }
}