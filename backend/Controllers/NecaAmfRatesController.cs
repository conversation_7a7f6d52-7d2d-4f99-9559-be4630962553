using backend.Data.HttpQueries;
using backend.Data.Models;
using backend.Extensions;
using backend.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace backend.Controllers;

[Route("api/[controller]")]
[ApiController]
public class NecaAmfRatesController(
    ILogger<NecaAmfRatesController> logger,
    EPRLiveDBContext db,
    IWebHostEnvironment env
) : ControllerBase
{
    private readonly ILogger<NecaAmfRatesController> _logger = logger;
    private readonly EPRLiveDBContext _db = db;
    private readonly IWebHostEnvironment _env = env;

    [HttpGet]
    [Authorize]
    public IEnumerable<NecaAmfRateSchedule> GetNecaAmfRateSchedules(
        [FromQuery] OptionalGuidQuery query
    )
    {
        int ChapterID = AuthUtils.GetCurrentChapterId(query, Request, _db, _env);
        return _db
            .NecaAmfRateSchedules.Where((n) => n.ChapterId == ChapterID)
            .OrderBy((n) => n.EffectiveStartDate)
            .ToList();
    }

    [HttpGet("rates")]
    [Authorize]
    public IEnumerable<NecaRateAndStep> GetNecaAmfRates([FromQuery] NecaRateQuery query)
    {
        return _db
            .NecaAmfRates.Where((r) => r.RateScheduleId == query.RateScheduleID)
            .LeftJoin(
                _db.NecaAmfSteps,
                r => r.Id,
                s => s.RateId,
                (r, s) =>
                    new NecaRateAndStep
                    {
                        RateID = r.Id,
                        RateScheduleID = r.RateScheduleId,
                        StepID = s.Id,
                        BenefitID = r.BenefitId,
                        CategoryID = r.CategoryId,
                        CalculationID = r.CalculationId,
                        Value = s.Value,
                        Min = s.Minimum,
                        Max = s.Maximum
                    }
            )
            .ToList();
    }

    [HttpPost]
    [Authorize]
    public ActionResult<IEnumerable<NecaAmfRateSchedule>> SaveRateSchedule(
        [FromBody] NecaRateScheduleSaveModel model
    )
    {
        int ChapterID = AuthUtils.GetCurrentChapterId(model, Request, _db, _env);
        NecaAmfRateSchedule? rateSchedule;
        if (model.RateScheduleID.HasValue)
        {
            int RateScheduleID = model.RateScheduleID.Value;
            rateSchedule = _db
                .NecaAmfRateSchedules.Where((r) => r.Id == model.RateScheduleID)
                .First();
        }
        else
        {
            rateSchedule = new NecaAmfRateSchedule { ChapterId = ChapterID };
            _db.NecaAmfRateSchedules.Add(rateSchedule);
        }
        rateSchedule.EffectiveStartDate = new DateTime(
            model.StartDate.Year,
            model.StartDate.Month,
            model.StartDate.Day,
            0,
            0,
            0
        );
        rateSchedule.EffectiveEndDate = model.EndDate.HasValue
            ? new DateTime(
                model.EndDate.Value.Year,
                model.EndDate.Value.Month,
                model.EndDate.Value.Day,
                0,
                0,
                0
            )
            : null;
        _db.SaveChanges();
        if (model.Rates != null)
        {
            foreach (NecaRateSaveModel row in model.Rates)
            {
                NecaAmfRate? rate = model.RateScheduleID.HasValue
                    ? _db
                        .NecaAmfRates.Where(
                            (r) =>
                                r.RateScheduleId == rateSchedule.Id
                                && r.BenefitId == row.BenefitId
                                && r.CategoryId == row.CategoryId
                        )
                        .FirstOrDefault()
                    : null;
                if (rate == null)
                {
                    rate = new NecaAmfRate
                    {
                        RateScheduleId = rateSchedule.Id,
                        BenefitId = row.BenefitId,
                        CategoryId = row.CategoryId
                    };
                    _db.NecaAmfRates.Add(rate);
                }
                rate.CalculationId = row.CalculationId;
            }
            _db.SaveChanges();
            foreach (NecaRateSaveModel row in model.Rates)
            {
                if (row.Steps == null)
                    continue;
                NecaAmfRate? rate = _db
                    .NecaAmfRates.Where(
                        (r) =>
                            r.RateScheduleId == rateSchedule.Id
                            && r.BenefitId == row.BenefitId
                            && r.CategoryId == row.CategoryId
                    )
                    .FirstOrDefault();
                foreach (NecaStepSaveModel uploadedStep in row.Steps)
                {
                    NecaAmfStep? step = null;
                    if (uploadedStep.Id.HasValue)
                    {
                        step = model.RateScheduleID.HasValue
                            ? _db
                                .NecaAmfSteps.Where((s) => s.Id == uploadedStep.Id)
                                .FirstOrDefault()
                            : null;
                    }
                    if (step == null)
                    {
                        step = new NecaAmfStep();
                        _db.NecaAmfSteps.Add(step);
                    }
                    step.RateId = rate!.Id;
                    step.Value = uploadedStep.Value;
                    step.Minimum = uploadedStep.Min;
                    step.Maximum = uploadedStep.Max;
                }
            }
        }
        if (model.StepsToDelete != null)
        {
            foreach (NecaAmfStep stepToDelete in model.StepsToDelete)
            {
                NecaAmfStep? Step = _db
                    .NecaAmfSteps.Where((s) => s.Id == stepToDelete.Id)
                    .FirstOrDefault();
                if (Step != null)
                {
                    _db.NecaAmfSteps.Remove(Step);
                }
            }
        }
        _db.SaveChanges();
        return _db.NecaAmfRateSchedules.Where((r) => r.ChapterId == ChapterID).ToList();
    }

    [HttpPost("rate-schedule-dates")]
    [Authorize]
    public IActionResult UpdateRateScheduleDates(
        [FromBody] NecaRateScheduleDatesSaveModel model
    )
    {
        foreach (NecaRateScheduleSaveModel rateScheduleModel in model.RateSchedules)
        {
            NecaAmfRateSchedule rateSchedule = _db
                .NecaAmfRateSchedules.Where((r) => r.Id == rateScheduleModel.RateScheduleID)
                .First();
            rateSchedule.EffectiveStartDate = rateScheduleModel.StartDate;
            rateSchedule.EffectiveEndDate = rateScheduleModel.EndDate;
        }
        _db.SaveChanges();
        return Ok();
    }

    [HttpDelete]
    [Authorize]
    public IActionResult DeleteRateSchedule([FromQuery] NecaRateQuery query)
    {
        NecaAmfRateSchedule? rateScheduleToDelete = _db
            .NecaAmfRateSchedules.Where((r) => r.Id == query.RateScheduleID)
            .FirstOrDefault();
        if (rateScheduleToDelete == null)
        {
            return NotFound();
        }
        List<int> RateIDs = _db
            .NecaAmfRates.Where((r) => r.RateScheduleId == rateScheduleToDelete.Id)
            .Select((r) => r.Id)
            .ToList();
        _db.NecaAmfSteps.RemoveRange(_db.NecaAmfSteps.Where((s) => RateIDs.Contains(s.RateId)));
        _db.NecaAmfRates.RemoveRange(
            _db.NecaAmfRates.Where((r) => r.RateScheduleId == rateScheduleToDelete.Id)
        );
        _db.NecaAmfRateSchedules.Remove(rateScheduleToDelete);
        _db.SaveChanges();
        return Ok();
    }

    public class NecaRateAndStep
    {
        public int RateID { get; set; }
        public int RateScheduleID { get; set; }
        public int? StepID { get; set; }
        public int BenefitID { get; set; }
        public int CategoryID { get; set; }
        public int CalculationID { get; set; }
        public decimal? Value { get; set; }
        public decimal? Min { get; set; }
        public decimal? Max { get; set; }
    }

    public class NecaRateQuery
    {
        public int RateScheduleID { get; set; }
    }

    public class NecaRateScheduleSaveModel : OptionalGuidQuery
    {
        public int? RateScheduleID { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public List<NecaRateSaveModel>? Rates { get; set; }
        public List<NecaAmfStep>? StepsToDelete { get; set; }
    }

    public class NecaRateSaveModel
    {
        public int Id { get; set; }
        public int BenefitId { get; set; }
        public int CategoryId { get; set; }
        public int CalculationId { get; set; }
        public List<NecaStepSaveModel>? Steps { get; set; }
    }

    public class NecaStepSaveModel
    {
        public int? Id { get; set; }
        public decimal Value { get; set; }
        public decimal Min { get; set; }
        public decimal? Max { get; set; }
    }

    public class NecaRateScheduleDatesSaveModel
    {
        public required List<NecaRateScheduleSaveModel> RateSchedules { get; set; }
    }
}
