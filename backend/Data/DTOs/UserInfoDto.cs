namespace backend.Data.DTOs;

public class UserInfoDto
{
    public List<string> Role { get; set; } = [];
    public List<string> Permission { get; set; } = [];
    public List<string> RoleGroups { get; set; } = [];
    public bool ShowMyReportsMenu { get; set; }
    public bool ShowTimesheetsMenu { get; set; }
    public required string Username { get; set; }
    public required string FirstName { get; set; }
    public required string LastName { get; set; }
    public required string Email { get; set; }
    public Guid OrgGUID { get; set; }
    public string? OrgName { get; set; }
    public int ChapterId { get; set; }
    public string? PhoneNumber { get; set; }
    public int? PhoneTypeId { get; set; }
    public string? PreferredMfamethod { get; set; }
    public string? PhoneNumberExtension { get; set; }
}
