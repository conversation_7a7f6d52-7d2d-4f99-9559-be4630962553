﻿using System;
using System.Collections.Generic;

namespace backend.Data.Models;

public partial class SubClassification
{
    public int Id { get; set; }

    public int? ChapterId { get; set; }

    public string Name { get; set; } = null!;

    public int DstatusId { get; set; }

    public string? Description { get; set; }

    public virtual Chapter? Chapter { get; set; }

    public virtual Dstatus Dstatus { get; set; } = null!;

    public virtual Root IdNavigation { get; set; } = null!;

    public virtual ICollection<ReportLineItem> ReportLineItems { get; set; } = new List<ReportLineItem>();
}
