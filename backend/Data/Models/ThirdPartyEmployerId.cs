﻿using System;
using System.Collections.Generic;
using backend.Data.Models;

namespace backend.Data.Models;

public partial class ThirdPartyEmployerId
{
    public int Id { get; set; }

    public int EmployerId { get; set; }

    public int ThirdPartyId { get; set; }

    public string? ThirdPartyEmployerId1 { get; set; }

    public DateTime CreationDate { get; set; }

    public string CreatedBy { get; set; } = null!;

    public DateTime LastModifiedDate { get; set; }

    public string LastModifiedBy { get; set; } = null!;

    public virtual Employer Employer { get; set; } = null!;

    public virtual Root IdNavigation { get; set; } = null!;

    public virtual Organization ThirdParty { get; set; } = null!;
}
