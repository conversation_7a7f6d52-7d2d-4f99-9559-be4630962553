﻿using System;
using System.Collections.Generic;

namespace backend.Data.Models;

public partial class ReportedBenefitReleaseAuthorization
{
    public int Id { get; set; }

    public int ReportId { get; set; }

    public int BenefitId { get; set; }

    public virtual Benefit Benefit { get; set; } = null!;

    public virtual Root IdNavigation { get; set; } = null!;

    public virtual Report Report { get; set; } = null!;
}
