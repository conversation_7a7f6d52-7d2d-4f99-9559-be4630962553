using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace backend.Data.Models
{
    [Keyless]
    public class TimeSheetUnionEmployerPaidBenefit
    {
        public int ReportLineItemID { get; set; }
        public int? ChapterID { get; set; }
        public required string Name { get; set; }
        public decimal? Amount { get; set; }
    }
} 