using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace backend.Data.Models
{
    [Keyless]
    public class TimeSheetExportLineItem
    {
        public Guid ID { get; set; }

        public int? ReportLineItemID { get; set; }
        public required string SSN { get; set; }
        public string? Name { get; set; }
        public string? UnionClassificationCode { get; set; }
        public DateTime Date { get; set; }
        public string? JobCode { get; set; }
        public string? CostCenter { get; set; }
        public Single? HourlyRate { get; set; }
        public Single? STHours { get; set; }
        public Single? OTHours { get; set; }
        public Single? DTHours { get; set; }
        public Single? Expenses { get; set; }
        public Single? Bonus { get; set; }
    }
}
