﻿using System;
using System.Collections.Generic;

namespace backend.Data.Models;

public partial class Rate
{
    public int RateScheduleId { get; set; }

    public int BenefitId { get; set; }

    public int ClassificationNameId { get; set; }

    public int? SubClassificationId { get; set; }

    /// <summary>
    /// Value can be null if the calculation method is &quot;Not Applicable&quot;
    /// </summary>
    public decimal? Value { get; set; }

    public int DcalculationMethodId { get; set; }

    public string? CustomCalculation { get; set; }

    public int DcalculationModifierId { get; set; }

    public decimal? MinimumBound { get; set; }

    public decimal? MaximumBound { get; set; }

    public virtual Benefit Benefit { get; set; } = null!;

    public virtual ClassificationName ClassificationName { get; set; } = null!;

    public virtual DcalculationMethod DcalculationMethod { get; set; } = null!;

    public virtual DcalculationModifier DcalculationModifier { get; set; } = null!;

    public virtual RateSchedule RateSchedule { get; set; } = null!;

    public virtual SubClassification? SubClassification { get; set; }
}
