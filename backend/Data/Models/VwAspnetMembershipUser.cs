﻿using System;
using System.Collections.Generic;

namespace backend.Data.Models;

public partial class VwAspnetMembershipUser
{
    public Guid UserId { get; set; }

    public int PasswordFormat { get; set; }

    public string? MobilePin { get; set; }

    public string? Email { get; set; }

    public string? LoweredEmail { get; set; }

    public string? PasswordQuestion { get; set; }

    public string? PasswordAnswer { get; set; }

    public bool IsApproved { get; set; }

    public bool IsLockedOut { get; set; }

    public DateTime CreateDate { get; set; }

    public DateTime LastLoginDate { get; set; }

    public DateTime LastPasswordChangedDate { get; set; }

    public DateTime LastLockoutDate { get; set; }

    public int FailedPasswordAttemptCount { get; set; }

    public DateTime FailedPasswordAttemptWindowStart { get; set; }

    public int FailedPasswordAnswerAttemptCount { get; set; }

    public DateTime FailedPasswordAnswerAttemptWindowStart { get; set; }

    public string? Comment { get; set; }

    public Guid ApplicationId { get; set; }

    public string UserName { get; set; } = null!;

    public string? MobileAlias { get; set; }

    public bool IsAnonymous { get; set; }

    public DateTime LastActivityDate { get; set; }
}
