﻿using System;
using System.Collections.Generic;

namespace backend.Data.Models;

public partial class ReportLineItemDetail
{
    public int ReportLineItemId { get; set; }

    public int BenefitId { get; set; }

    public decimal? Amount { get; set; }

    public object? VariantValue { get; set; }

    public virtual Benefit Benefit { get; set; } = null!;

    public virtual ReportLineItem ReportLineItem { get; set; } = null!;
}
