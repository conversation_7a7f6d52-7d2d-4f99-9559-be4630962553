﻿using System;
using System.Collections.Generic;

namespace backend.Data.Models;

public partial class VEffectiveUserRole
{
    public Guid RoleId { get; set; }

    public string RoleName { get; set; } = null!;

    public string? LoweredRoleName { get; set; }

    public Guid UserId { get; set; }

    public string UserName { get; set; } = null!;

    public string? LoweredUserName { get; set; }

    public int RoleGroupId { get; set; }

    public string RoleGroupName { get; set; } = null!;

    public string? LoweredRoleGroupName { get; set; }
}
