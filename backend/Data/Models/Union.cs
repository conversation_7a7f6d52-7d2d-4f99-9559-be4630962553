﻿using System;
using System.Collections.Generic;

namespace backend.Data.Models;

public partial class Union
{
    public int Id { get; set; }

    /// <summary>
    /// Determines the default day of the month that payroll reports are due on. If a payroll report is submitted after this date, it is delinquent.
    /// </summary>
    public int DefaultDelinquentDay { get; set; }

    public virtual ICollection<Agreement> Agreements { get; set; } = new List<Agreement>();

    public virtual Organization IdNavigation { get; set; } = null!;
}
