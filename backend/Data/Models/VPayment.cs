﻿using System;
using System.Collections.Generic;

namespace backend.Data.Models;

public partial class VPayment
{
    public string Employer { get; set; } = null!;

    public DateTime WorkMonth { get; set; }

    public string Chapter { get; set; } = null!;

    public string Agreement { get; set; } = null!;

    public DateTime CreationDate { get; set; }

    public string LastModifiedBy { get; set; } = null!;

    public decimal Payment { get; set; }

    public string Benefit { get; set; } = null!;

    public decimal BenefitAmount { get; set; }

    public int PaymentId { get; set; }

    public int BenefitId { get; set; }

    public int ReportId { get; set; }
}
