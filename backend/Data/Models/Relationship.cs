﻿using System;
using System.Collections.Generic;

namespace backend.Data.Models;

public partial class Relationship
{
    public int Id { get; set; }

    public int LeftPartyId { get; set; }

    /// <summary>
    /// The RightPartyID should only be null for one-to-one type relationships.
    /// </summary>
    public int? RightPartyId { get; set; }

    public int DrelationshipTypeId { get; set; }

    public int? DrelationshipSubTypeId { get; set; }

    public virtual ChapterToEmployerRelationship? ChapterToEmployerRelationship { get; set; }

    public virtual DrelationshipSubType? Drelationship { get; set; }

    public virtual DrelationshipType DrelationshipType { get; set; } = null!;

    public virtual Root IdNavigation { get; set; } = null!;

    public virtual Party LeftParty { get; set; } = null!;

    public virtual ICollection<RelationshipStatus> RelationshipStatuses { get; set; } = new List<RelationshipStatus>();

    public virtual Party? RightParty { get; set; }
}
