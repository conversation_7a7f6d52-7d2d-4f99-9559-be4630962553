﻿using System;
using System.Collections.Generic;

namespace backend.Data.Models;

public partial class VClassificationXref
{
    public string UnionName { get; set; } = null!;

    public string Agreement { get; set; } = null!;

    public DateTime? AgreementEndDate { get; set; }

    public string Classification { get; set; } = null!;

    public string? SubClassification { get; set; }

    public string Benefit { get; set; } = null!;

    public string? Xrefvalue { get; set; }

    public int? XrefId { get; set; }

    public int UnionId { get; set; }

    public int AgreementId { get; set; }

    public int ClassificationNameId { get; set; }

    public int? SubClassificationId { get; set; }

    public int BenefitId { get; set; }

    public int? FundAdministratorId { get; set; }

    public string Id { get; set; } = null!;
}
