﻿using System;
using System.Collections.Generic;

namespace backend.Data.Models;

public partial class RoleGroup
{
    public int Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public virtual ICollection<NewsItem> NewsItems { get; set; } = new List<NewsItem>();

    public virtual ICollection<AspnetRole> AspnetRoles { get; set; } = new List<AspnetRole>();

    public virtual ICollection<AspnetUser> AspnetUsers { get; set; } = new List<AspnetUser>();
}
