using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace backend.Data.Models
{
    [Keyless]
    public class TimeSheetEmployeeDeductibleBenefit
    {
        public int ReportLineItemID { get; set; }

        public string Name { get; set; }

        [Column(TypeName = "decimal(18, 2)")]
        public decimal? Amount { get; set; }
        public TimeSheetEmployeeDeductibleBenefit(int reportLineItemID, string name, decimal? amount)
        {
            ReportLineItemID = reportLineItemID;
            Name = name;
            Amount = amount;
        }
    }
} 