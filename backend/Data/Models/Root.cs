﻿using System;
using System.Collections.Generic;

namespace backend.Data.Models;

public partial class Root
{
    public int Id { get; set; }

    public Guid? Guid { get; set; }

    public DateTime CreationDate { get; set; }

    public string CreatedBy { get; set; } = null!;

    public DateTime LastModificationDate { get; set; }

    public string LastModifiedBy { get; set; } = null!;

    public virtual Agreement? Agreement { get; set; }

    public virtual Benefit? Benefit { get; set; }

    public virtual BenefitOverridesOrig? BenefitOverridesOrig { get; set; }

    public virtual ClassificationName? ClassificationName { get; set; }

    public virtual ContactMechanism? ContactMechanism { get; set; }

    public virtual ElectronicBatch? ElectronicBatch { get; set; }

    public virtual ElectronicPayment? ElectronicPayment { get; set; }

    public virtual ElectronicPaymentConfiguration? ElectronicPaymentConfiguration { get; set; }

    public virtual EmployersToAgreement? EmployersToAgreement { get; set; }

    public virtual Image? Image { get; set; }

    public virtual ICollection<Note> NoteIdNavigations { get; set; } = new List<Note>();

    public virtual ICollection<Note> NoteRoots { get; set; } = new List<Note>();

    public virtual PartiesToContactMechanism? PartiesToContactMechanism { get; set; }

    public virtual Party? Party { get; set; }

    public virtual Payment? Payment { get; set; }

    public virtual ICollection<PaymentMethod> PaymentMethods { get; set; } = new List<PaymentMethod>();

    public virtual RateSchedule? RateSchedule { get; set; }

    public virtual Relationship? Relationship { get; set; }

    public virtual RelationshipStatus? RelationshipStatus { get; set; }

    public virtual Report? Report { get; set; }

    public virtual ReportLineItem? ReportLineItem { get; set; }

    public virtual ReportSuppression? ReportSuppression { get; set; }

    public virtual ReportedBenefitReleaseAuthorization? ReportedBenefitReleaseAuthorization { get; set; }

    public virtual SubClassification? SubClassification { get; set; }

    public virtual TimelinesOrig? TimelinesOrig { get; set; }
}
