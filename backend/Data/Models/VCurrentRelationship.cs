﻿using System;
using System.Collections.Generic;

namespace backend.Data.Models;

public partial class VCurrentRelationship
{
    public int Id { get; set; }

    public string RelationshipTypeName { get; set; } = null!;

    public int LeftPartyId { get; set; }

    public string? LeftPartyName { get; set; }

    public int? RightPartyId { get; set; }

    public string? RightPartyName { get; set; }

    public bool? Active { get; set; }
}
