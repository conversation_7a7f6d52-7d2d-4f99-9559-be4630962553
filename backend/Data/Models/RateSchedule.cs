﻿using System;
using System.Collections.Generic;

namespace backend.Data.Models;

public partial class RateSchedule
{
    public int Id { get; set; }

    public int AgreementId { get; set; }

    public DateTime EffectiveStartDate { get; set; }

    public DateTime? EffectiveEndDate { get; set; }

    public virtual Agreement Agreement { get; set; } = null!;

    public virtual Root IdNavigation { get; set; } = null!;

    public virtual ICollection<Report> Reports { get; set; } = new List<Report>();
}
