﻿using System;
using System.Collections.Generic;

namespace backend.Data.Models;

public partial class ReportLineItem
{
    public int Id { get; set; }

    public int ReportId { get; set; }

    public int? EmployeeId { get; set; }

    public int? ClassificationNameId { get; set; }

    public int? SubClassificationId { get; set; }

    public int? DamendmentActionId { get; set; }

    public int? AmendedLineItemId { get; set; }

    public virtual ClassificationName? ClassificationName { get; set; }

    public virtual DamendmentAction? DamendmentAction { get; set; }

    public virtual Employee? Employee { get; set; }

    public virtual Root IdNavigation { get; set; } = null!;

    public virtual ICollection<PayStubDetail> PayStubDetails { get; set; } = new List<PayStubDetail>();

    public virtual Report Report { get; set; } = null!;

    public virtual ICollection<ReportLineItemDetail> ReportLineItemDetails { get; set; } = new List<ReportLineItemDetail>();

    public virtual SubClassification? SubClassification { get; set; }
}
