﻿using System;
using System.Collections.Generic;

namespace backend.Data.Models;

public partial class RelationshipStatus
{
    public int Id { get; set; }

    public int RelationshipId { get; set; }

    public int DrelationshipStatusId { get; set; }

    public virtual DrelationshipStatus DrelationshipStatus { get; set; } = null!;

    public virtual Root IdNavigation { get; set; } = null!;

    public virtual Relationship Relationship { get; set; } = null!;
}
