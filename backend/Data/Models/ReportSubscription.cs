﻿using System;
using System.Collections.Generic;

namespace backend.Data.Models;

public partial class ReportSubscription
{
    public int OrganizationId { get; set; }

    public int CustomReportId { get; set; }

    public int ReportSubscriptionTypeId { get; set; }

    public int Id { get; set; }

    public int? OptimisticLockField { get; set; }

    public virtual DreportSubscriptionType ReportSubscriptionType { get; set; } = null!;
}
