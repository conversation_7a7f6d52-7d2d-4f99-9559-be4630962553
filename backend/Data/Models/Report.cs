﻿using System;
using System.Collections.Generic;
using backend.Data.Models;

namespace backend.Data.Models;

public partial class Report
{
    public int Id { get; set; }

    public int AgreementId { get; set; }

    public int? RateScheduleId { get; set; }

    public int EmployerId { get; set; }

    public DateTime PeriodStartDate { get; set; }

    public DateTime PeriodEndDate { get; set; }

    /// <summary>
    /// This should always be the first of the month with no time elements (e.g., 1/1/2008 00:00:00.000)
    /// </summary>
    public DateTime WorkMonth { get; set; }

    public int DreportStatusId { get; set; }

    public int? AmendedReportId { get; set; }

    public bool AggregatesOnly { get; set; }

    public string? Notes { get; set; }

    public int? AggregateEmployeeCount { get; set; }

    public bool ZeroHour { get; set; }

    public bool? FullyPaid { get; set; }

    public DateTime? SubmissionDate { get; set; }

    public virtual Agreement Agreement { get; set; } = null!;

    public virtual Report? AmendedReport { get; set; }

    public virtual DreportStatus DreportStatus { get; set; } = null!;

    public virtual ICollection<ElectronicPayment> ElectronicPayments { get; set; } = new List<ElectronicPayment>();

    public virtual Employer Employer { get; set; } = null!;

    public virtual ICollection<FundingComment> FundingComments { get; set; } = new List<FundingComment>();

    public virtual Root IdNavigation { get; set; } = null!;

    public virtual ICollection<Report> InverseAmendedReport { get; set; } = new List<Report>();

    public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();

    public virtual RateSchedule? RateSchedule { get; set; }

    public virtual ICollection<ReportLineItem> ReportLineItems { get; set; } = new List<ReportLineItem>();

    public virtual ICollection<ReportSuppression> ReportSuppressions { get; set; } = new List<ReportSuppression>();

    public virtual ICollection<ReportedBenefitReleaseAuthorization> ReportedBenefitReleaseAuthorizations { get; set; } = new List<ReportedBenefitReleaseAuthorization>();
}
