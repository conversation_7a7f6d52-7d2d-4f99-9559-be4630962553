using System.Reflection;

namespace backend.Utils
{
    /// <summary>
    /// Utility class for common reflection operations
    /// </summary>
    public static class ReflectionUtils
    {
        /// <summary>
        /// Safely gets a property value from an object as a string using reflection
        /// </summary>
        /// <param name="item">The object to get the property from</param>
        /// <param name="propertyName">The name of the property</param>
        /// <returns>The property value as string, or empty string if not found or null</returns>
        public static string GetPropertyValueAsString(object? item, string propertyName)
        {
            // Validate input parameter
            if (string.IsNullOrWhiteSpace(propertyName) || item == null)
            {
                return "";
            }

            try
            {
                var property = item.GetType()
                    ?.GetProperty(propertyName, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                
                return property?.GetValue(item, null) as string ?? "";
            }
            catch (Exception)
            {
                return "";
            }
        }
    }
} 