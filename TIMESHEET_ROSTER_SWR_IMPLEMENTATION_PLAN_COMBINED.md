# Timesheet Roster SWR — Unified Implementation Plan

_Last updated: **2025-08-05**_

This document consolidates **Windsurf** and **Cursor-gk4** analyses into a single, step-by-step plan.  It is written for an engineer who is **new to the code-base** and needs both context **and** concrete instructions.

---

## 0 Quick Glossary
| Term | Meaning |
|------|---------|
| `TTL_MS` (`REVALIDATE_MS`) | 30 000 ms – data considered *fresh* for 30 s |
| `cacheKey` | `"<QueryName>:" + stableStringify(variables)` |
| **Persisted blob** | JSON written to IndexedDB by `ui/src/relay/createPersistedStore.ts` |
| **Op timestamp** | `globalOpSavedAt[cacheKey]` → exposed on `window.__RELAY_OP_SAVED_AT__` |

---

## 1 Problem Statement (re-cap)
1. **Background revalidation always fires** after a hard reload because the per-operation timestamp is often **missing**.
2. **Paginated pages disappear** after each revalidation because Merge logic executes **after** Relay has already replaced `edges[]`.
3. Minor issues: hash over-sensitivity, limited connection-field pattern list, confusing debug label.

---

## 2 Goals
| # | Goal | Success Metric |
|---|------|---------------|
| G1 | Honour 30-second TTL across page reloads | Hard-reloading within 30 s triggers **zero** network calls (DevTools) |
| G2 | Preserve previously-fetched pagination slices after revalidation | `edgeDelta ≥ 0` and UI still shows older pages |
| G3 | Avoid useless store publishes | Identical payload ⇒ **no** additional publish/render |
| G4 | DX improvements | Clear logs, maintainable code & tests |

---

## 3 High-Level Approach
1. **Make op-timestamp persistence rock-solid** (fix G1 & partially G3).
2. **Capture and merge edges _before_ the network publish** (fix G2).
3. **Harden the hash/connection logic** (reduce false replacements).
4. Extend logging & tests.

---

## 4 Change Log (Files & Tasks)
> Paths are *relative to repo root* (`/home/<USER>/repos/eprlive24/subscriptions`).

### 4.1 Timestamp Persistence
| File | Task |
|------|------|
| `ui/src/relay/useSWRQuery.ts` | 1. Replace `shouldTriggerBackgroundRevalidation` condition with the simplified TTL check shown in §5.1.<br>2. Call `notifyStoreUpdated()` **synchronously** right after setting `window.__RELAY_OP_SAVED_AT__[cacheKey]` (remove the dynamic `import()` / await). |
| `ui/src/relay/createPersistedStore.ts` | 1. Expose a **non-debounced** `flushNow()` that persists immediately.<br>2. Invoke `flushNow()` from a `beforeunload` listener **and** from `notifyStoreUpdated()`.<br>3. Add `navigator.sendBeacon` fallback if `IndexedDB` write fails. |
| `ui/src/relay/__tests__/timestamps.test.ts` *(new)* | Add Jest tests covering: (a) timestamp survives a reload, (b) TTL respected. |

### 4.2 Edge Preservation
| File | Task |
|------|------|
| `ui/src/relay/useSWRQuery.ts` | 1. Inside `triggerBackgroundRevalidation()` **before** calling `fetchQuery`, capture:<br>```ts
const existingEdgesSnapshot = connection?.getLinkedRecords('edges') ?? [];
```<br>2. Pass `existingEdgesSnapshot` to the `commitUpdate` block and merge it **with** `newEdges` **regardless of hash outcome**.<br>3. If the hash differs, still merge (do not replace) unless the user has paginated back to the beginning & requested a reset (edge-case flag TODO). |
| `ui/src/relay/cacheFilters.ts` | 1. Generalise `mergeConnectionEdges` to accept `existingEdgesSnapshot` argument so it doesn’t rely on connection’s *current* state.<br>2. Add optional param `preserveAll=true` to keep *all* unique edges when hash differs. |

### 4.3 Hash & Connection Hardening
| File | Task |
|------|------|
| `ui/src/relay/useSWRQuery.ts` | 1. Change hash input to omit volatile fields (e.g., `node.updatedAt`).<br>2. Extract connection-field detection into `getRosterConnection(record)` utility located in `ui/src/relay/connectionUtils.ts`; fall back to reflection on `__connection` ids so any new list field is caught. |
| `ui/src/relay/connectionUtils.ts` *(new)* | Implements robust connection discovery based on `record.getLinkedRecords()` iteration. |

### 4.4 DX / Logging
| File | Task |
|------|------|
| `ui/src/relay/useSWRQuery.ts` | Rename debug flag `network_first_policy` → `forced_network_due_to_missing_timestamp` for clarity. |
| `ui/src/relay/debug.ts` | Map new label to coloured output. |

### 4.5 Tests & Tooling
| Scope | Task |
|-------|------|
| Unit | Expand cache-edge merge tests (`ui/src/relay/__tests__/edgeMerge.test.ts`). |
| Integration | Add Playwright scenario: paginate to page 3, reload within 30 s, assert data still at page 3 with no network hit. |

---

## 5 Code Snippets (required edits)
### 5.1 TTL Guard (replace block in `useSWRQuery.ts`)
```ts
// NEW TTL logic
const isCacheFresh = savedAt !== undefined && Date.now() - savedAt <= REVALIDATE_MS;
const shouldTriggerBackgroundRevalidation = !isCacheFresh && availability.status !== 'missing';
```

### 5.2 Immediate Flush (add to `createPersistedStore.ts`)
```ts
export function flushNow(): void {
  try {
    persist(source.toJSON());
  } catch (e) {
    console.warn('[createPersistedStore] flushNow failed', e);
  }
}

window.addEventListener('beforeunload', flushNow);
```
And in `notifyStoreUpdated()` replace debounce with direct `flushNow()`.

---

## 6 Design Decisions & Justification
1. **Write-before-close:** Saves op-timestamps before the user navigates away, eliminating the race with the 1.5 s debounce.
2. **Merge-first then publish:** By snapshotting existing edges we ensure data preservation even if Relay’s default handler replaces edges.
3. **Hash loosening:** False positives waste network and UI work; hashing stable IDs/cursors only reduces replacements while still detecting true list diffs.
4. **Generalised connection discovery:** Avoids maintenance overhead as schemas evolve.

---

## 7 Roll-out Plan
1. Implement behind env flag `VITE_SWR_EDGE_PRESERVATION_V2`.
2. Canary in staging — enable for 10 % traffic.
3. Monitor metrics: cache hit ratio, rendered edge count, background fetch frequency.
4. Gradually ramp to 100 % if no regression.

---

## 8 Verification Checklist
- [ ] Jest suite green.
- [ ] Playwright scenario passes.
- [ ] DevTools shows **no** network call on reload < 30 s.
- [ ] UI retains paginated data after background revalidation.
- [ ] IndexedDB blob contains `opSavedAt` with recent timestamp.

---

## 9 Fallback / Rollback
Set `VITE_SWR_EDGE_PRESERVATION_V2="0"` to restore current behaviour instantly – code will skip the new snapshot merge & use old path.

---

© 2025 EPR Live — Engineering
