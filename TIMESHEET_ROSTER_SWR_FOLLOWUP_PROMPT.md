# 🤖 Follow-Up Prompt – Investigate SWR Cache-Miss Logic for `TimesheetRosterQuery`

**Date:** 2025-08-05
**Context owner:** Frontend Infra Team (Relay / SWR)

---

## 1 Background

`TimesheetRosterQuery` should follow a *stale-while-revalidate* (SWR) pattern implemented in
`ui/src/relay/useSWRQuery.ts`.
Despite recent fixes (see commit **a7847b7e** – added stable cache keys & new tests) the query still:

* **Returns data from the cache** (Relay `check()` ⇒ `available`).
* **Marks its own result as a cache-MISS** in debug logs.
* **Never performs a foreground network request** when data is stale; instead it triggers only the background revalidation path.

A detailed browser log is attached below (section 4).  The log shows contradictory diagnostics – simultaneous *cache hit* and *cache miss* signals.

We believe the internal logic that classifies hits/misses in `useSWRQuery.ts` is flawed.  Your task is to pinpoint the problem and propose a concrete code-level fix.

---

## 2 Key Code Files (paths are relative to repo root)

1. `ui/src/relay/useSWRQuery.ts` – SWR hook, background revalidation, cache-hit detection.
2. `ui/src/relay/createPersistedStore.ts` – IndexedDB persistence, per-operation timestamp exposure.
3. `ui/src/relay/cacheFilters.ts` – filtering / trimming records before persistence.
4. `ui/src/container/TimesheetRoster/TimesheetRoster.tsx` – component invoking the query; builds variables.
5. `ui/src/utils/stableStringify.ts` – new helper added in commit a7847b7e for cache-key stability.
6. Tests added by commit a7847b7e:
   * `ui/src/relay/__tests__/createCacheKey.test.ts`
   * `ui/src/relay/__tests__/swrRoster.integration.test.ts`

---

## 3 Recent Changes

Commit **a7847b7e** (message excerpt):

* Added **stable serialisation** for cache keys.
* Normalised variables in roster container.
* Wrote unit + integration tests covering cache-key determinism and SWR behaviour.

Yet the issue persists in the real browser session.

---

## 4 Observed Dev-Console Log (abridged)

```text
…
🚀 Starting GraphQL query: TimesheetRosterQuery (fetchPolicy: 'store-only', willRevalidate: true)
✅ GraphQL query completed … cacheStatus: 'MISS' (loadTime 199ms)
useSWRQuery.ts:100 ✅ Relay pre-check: 'TimesheetRosterQuery' is AVAILABLE from cache
…
🔍 Cache Miss Analysis: TimesheetRosterQuery (reason: incomplete_data)
…
🔄 Starting background revalidation for TimesheetRosterQuery
✅ Background revalidation completed for TimesheetRosterQuery
…
```

Key contradictions:

1. `fetchPolicy` is `store-only`, indicating a *cache hit* path.
2. Completion log marks **cacheStatus:'MISS'**.
3. `environment.check()` pre-check says **AVAILABLE**.

---

## 5 Suspect Areas

* **Cache-hit detection metric** in `useSWRQuery.ts` lines ~360-380.  It infers a miss when `loadTime > 50 ms`. Pagination queries can take longer even when all data is local.
* **Double invocation** of `useLazyLoadQuery`/loggers might skew timing.
* **Connection trimming** results in fewer edges, making `loadTime` longer to hydrate React tree, fooling the heuristic.
* **`fromCache` computation ignores `UNSTABLE_renderPolicy:'full'` which lengthens render time.

---

## 6 Requested Tasks for the AI Assistant

1. Trace the *cache-hit / cache-miss classification* path in `useSWRQuery.ts` and identify logic errors.
2. Validate against real-world characteristics (large connection payloads, lazy evaluation, render time).
3. Propose a deterministic alternative (e.g. rely on Relay `execution` info or compare pre/post store snapshots instead of timing).
4. Provide code-level diff(s) to:
   * Correct the hit/miss heuristic.
   * Add reliable instrumentation for future debugging.
   * Update/extend the Jest integration tests accordingly.
5. Ensure no regression for other queries.

Deliver output as:

* Explanation of root cause.
* Patch (Git diff or `search_replace` spec).
* Updated / new tests.
* Any necessary doc updates.

---

## 7 Resources

* **TIMESHEET_ROSTER_SWR_FIX_PLAN.md** – previous implementation roadmap.
* **RELAY_SWR_DEBUGGING_PROMPT.md** – earlier in-depth investigation prompt.
* `window.__RELAY_DEV__` helpers listed in the log can reproduce conditions.

---

### End of prompt – please investigate and propose a fix.  Thank you!
