# Timesheet Roster SWR – Windsurf Findings

_Last updated: 2025-08-05_

> Objective: explain **why** the app still (a) refetches on every page load even when the last fetch was < 30 s ago and (b) drops previously-paginated data after each refetch, despite the recent "merge/replace" implementation.  _No code changes have been made; this is an analysis only._

---

## 1  Observed Symptoms
1. <PERSON><PERSON><PERSON> shows `Cache Hit` but _immediately_ schedules a **background revalidation** (network-only).
2. Refreshing the page within 30 s triggers the same pattern again – the 30 s TTL appears ignored.
3. After the network response arrives the **connection edges are truncated**; earlier paginated pages are no longer in the Relay store.

---

## 2  Root-Cause Analysis
### 2.1  Refetch always triggers (TTL ignored)
| Step | Explanation |
|------|-------------|
| 1 | `useSWRQuery` decides `fetchPolicy = 'store-only'` _and_ whether to call `triggerBackgroundRevalidation()`.  (file `useSWRQuery.ts:560-587`)
| 2 | It relies on `window.__RELAY_OP_SAVED_AT__[cacheKey]` to know _when_ the operation was last validated.  If the key is **missing**, it assumes the data is stale and calls the background revalidator. |
| 3 | The timestamp is written **after** the revalidation (`useSWRQuery.ts:470-488`) and persisted through `notifyStoreUpdated()` → `createPersistedStore.schedulePersistCallback()` – but this persist is **debounced by 1.5 s** (`DELAY_MS`). |
| 4 | When the user refreshes the page quickly (typical dev workflow) the browser unloads before the debounced write executes.  On the next load the persisted blob therefore **does not contain** the new per-op timestamp, so the hook believes the cache is still unvalidated and fires another network-only request. |
| 5 | Result: every hard refresh behaves like the first load; the 30 s TTL is never honoured unless the page stays open > 1.5 s _after_ the previous validation. |

> **Consequence**: The design document’s “serve cache for 30 s” guarantee is broken in quick-reload scenarios (most noticeable in development but also in prod when users navigate rapidly).

### 2.2  Loss of paginated pages after refetch
| Step | Explanation |
|------|-------------|
| 1 | A network-only fetch publishes its payload via Relay’s **default connection handler**, which **replaces** the `edges` array with only the first page returned by the server.  Previously-cached pages are therefore gone at this point. |
| 2 | The post-publish `environment.commitUpdate()` block (intended to “merge or replace”) now runs **after** the store already holds the truncated list.  It reads `connection.getLinkedRecords('edges')` – which is _exactly_ the freshly-written, truncated edges. |
| 3 | In the _identical-hash_ path the code calls `mergeConnectionEdges(storeProxy, connection, newEdges)`.  Because `existingEdges === newEdges`, the helper performs a no-op merge; lost pages cannot be recovered. |
| 4 | In the _different-hash_ path the code explicitly calls `connection.setLinkedRecords([...newEdges])`, ensuring the truncated list remains. |

> **Consequence**: Regardless of hash outcome, pagination slices accumulated by the user are discarded after each background revalidation – matching the user-reported symptom.

---

## 3  Additional Findings
1. **Predicted Reason: network_first_policy** – The debug analyser flags `network_first_policy` because the hook uses `fetchPolicy: 'network-only'` in the revalidator.  This string may confuse developers; consider renaming the heuristic.
2. **`revalidationTimestamps` Map** (in-memory) prevents _duplicate_ background requests during a single session but offers no persistence – acceptable, but worth noting.
3. **Hash calculation** uses only `{id, cursor}` which is fine, but because edge replacement already happened, equal hashes simply mean “same first page” – they tell us nothing about lost pages.

---

## 4  Recommendations (for future implementation)
* **Persist timestamps synchronously (or with `navigator.sendBeacon` on `beforeunload`)** to guarantee the 30 s TTL survives a fast reload.
* **Preserve existing edges _before_ the network-only publish**.
  * Easiest: read `existingEdges` _inside_ `triggerBackgroundRevalidation()` **before** calling `fetchQuery`, keep a reference, then merge that saved list inside the `commitUpdate`.
  * Alternative: move to **Approach B (hash-check in custom network layer)**, which avoids the double-publish and never discards edges when data is identical.
* **Audit tests** – current Jest suite covers timestamp persistence but does not simulate `beforeunload`; add an integration test using `jsdom` timers.

---

## 5  Summary
* **Why TTL fails**: the per-operation timestamp isn’t written to IndexedDB if the tab closes < 1.5 s after revalidation, so every reload looks stale.
* **Why pagination disappears**: the merge helper operates _after_ Relay already replaced the edges; it therefore cannot recover discarded pages.
* **Next steps**: make timestamp persistence immediate and reorder the merge logic (or adopt a network-layer hash check) to truly preserve pagination slices.

© 2025 EPR Live — Engineering (Windsurf analysis)
