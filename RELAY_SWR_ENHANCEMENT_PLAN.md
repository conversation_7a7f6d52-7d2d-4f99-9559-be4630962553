# Relay `useSWRQuery` – **30-Second Stale-While-Revalidate** Enhancement Plan

> **Audience** – Front-end developers new to this codebase. The document explains **why** each change is needed, **how** it will be implemented, and **how** to validate success.

---

## 1. Problem Statement

Current `useSWRQuery` delivers _fresh-for-5-minutes_ behaviour:

1. **0-5 min** – Cached data is considered _fresh_; query returns instantly (`store-only`).
2. **>5 min** – Hook falls back to `store-or-network`. Users wait for the network before seeing data.
3. **Background revalidation** is supposed to happen but anecdotal reports show the UI blocks instead.

**UX Impact** – Users experience loading spinners if they revisit the page after ~5 minutes.

---

## 2. Desired Behaviour

| Phase | Timeline | What should happen |
|-------|----------|--------------------|
| Fresh | 0-30 sec | Serve cached data instantly. **No** network request. |
| Stale | 30 sec-∞ | Still serve cached data instantly **AND** trigger a background refetch. |
| Update | On response | If data changed, every component that executed the query/fragments should automatically re-render with the fresh data. |

---

## 3. High-Level Design

```
┌──── initial render ───┐
│ useSWRQuery()        │
│   ├─ Check Relay store availability
│   ├─ if data NOT found → fetchPolicy = "store-or-network" (old path)
│   └─ if data found     → fetchPolicy = "store-only"
│          │
│          ├─ cacheAge ≤ 30s → RETURN ✓ (fresh)
│          │
│          └─ cacheAge > 30s →
│                • schedule background revalidation (fetchQuery force:true)
│                • RETURN cached data immediately (stale)
└────────────────────────┘
```

* **Constant names**
  * `FRESH_TTL_MS` → _deprecate_
  * `REVALIDATE_MS` → **30_000** (30 sec)
* Updates flow automatically: Relay publishes the network payload into the store; all subscribed components (including the one running `useSWRQuery`) receive new props and re-render.

---

## 4. Detailed Implementation Steps

### 4.1 Update Constants

1. Open `ui/src/relay/useSWRQuery.ts`.
2. Change `const REVALIDATE_MS = 30_000;`
   _(Remove unused `FRESH_TTL_MS`; relying purely on runtime TTL.)_

### 4.2 Simplify Freshness Logic

```ts
// Before (abridged)
if (!staleFlag && cacheAge < ttlMs) {
    fetchPolicy = 'store-only';            // fresh (<5m)
} else {
    fetchPolicy = 'store-only';            // stale (≥5m) but still UI blocks later 🙁
}
```

```ts
// After
const cacheAge = hydratedAt ? Date.now() - hydratedAt : Infinity;
fetchPolicy = availability.status === 'available' ? 'store-only' : 'store-or-network';
if (availability.status === 'available' && cacheAge > REVALIDATE_MS && shouldRevalidate(cacheKey)) {
    shouldTriggerBackgroundRevalidation = true;
}
```

* **Rationale** – Always serve cached data if it exists; _age_ only gates **background** revalidation, never the user-facing fetchPolicy.

### 4.3 Tighten `shouldRevalidate()`

* Keep the cool-down Map to avoid flooding.
* No change needed; new constant propagates automatically.

### 4.4 Ensure Background Revalidation is Truly “Background”

* Already scheduled via `setTimeout(() => fetchQuery(...), 0)` – non-blocking.
* Verify `fetchQuery` is called with `networkCacheConfig: { force: true }` so it bypasses cache & ensures fresh data writes through the store.
* Confirm promise chain is not awaited by the component render (it isn’t).

### 4.5 Data Propagation

* **Relay Guarantee** – Components that used `useLazyLoadQuery`, `useFragment`, or `useSWRQuery` automatically subscribe to the store. When the background network response commits:
  * Relay publishes new records.
  * React Suspense is _not_ triggered because the data is now satisfied.
  * Components re-render with new props.
* **No extra code** needed, but we will add a regression test (section 6).

### 4.6 Remove Dead Code

* Delete `FRESH_TTL_MS` constant.
* Delete branch that checks `ttlMs` for fetchPolicy – now redundant.

---

## 5. Edge-Case Handling

| Scenario | Expected Outcome | Handling |
|----------|------------------|----------|
| **Cache miss** (first visit) | UI waits for network (cannot show data) | fetchPolicy = `store-or-network` unchanged. |
| **Offline** | Serve cached data; background revalidation fails silently | `triggerBackgroundRevalidation` already catches & logs errors. Cool-down prevents retry storms. |
| **Rapid page churn** (user navigates back repeatedly) | At most **1** revalidation per `cacheKey` every 30 sec | Cool-down Map. |
| **Large queries** | Potential network cost | Same behaviour today; developers can override REVALIDATE_MS per query later if needed. |

---

## 6. Testing Strategy

### 6.1 Automated Jest Tests (Relay MockLoader)

1. **Fresh Cache** (<30 s)
   * Hydrate store.
   * Call `useSWRQuery`.
   * Assert no network fetch occurs.
2. **Stale Cache** (>30 s)
   * Advance timers to 31 s.
   * Call hook.
   * Assert hook returns cached data synchronously.
   * Assert `fetchQuery` scheduled (mocked) **after** render.
3. **UI Re-render**
   * Complete mocked network fetch with modified payload.
   * Expect component to re-render with new data.

### 6.2 Manual Browser Validation

1. Load page → wait >30 s → open Network tab.
2. Navigate away & back.
3. Confirm:
   * Page renders instantly.
   * Immediate console log marks `willRevalidate: true`.
   * Network request fires in background.
   * UI updates when request completes.

---

## 7. Roll-out & Monitoring

| Step | Action |
|------|--------|
| 1 | Ship to **staging** behind `VITE_SWR_REDUCED_TTL=30` env variable (opt-in). |
| 2 | Monitor error logs & performance metrics (`relayObservability.trackCacheHit` / `trackCacheMiss`). |
| 3 | Compare spinner counts and average TTI before & after. |
| 4 | If stable, enable by default in production. |

---

## 8. Timeline & Owners

| Task | Owner | ETA |
|------|-------|-----|
| Code updates (`useSWRQuery.ts`) | @FrontendDev | Day 1 |
| Jest tests | @QA | Day 2 |
| Staging validation | QA + Dev | Day 3 |
| Prod rollout | DevOps | Day 4 |

---

## 9. References

* [`useSWRQuery.ts`](./ui/src/relay/useSWRQuery.ts)
* [`createPersistedStore.ts`](./ui/src/relay/createPersistedStore.ts)
* [Relay Docs – Data subscriptions](https://relay.dev/docs/guides/subscriptions/)

---

**End of plan** – feel free to reach out to the original implementer (`@cheema`) for clarifications.
