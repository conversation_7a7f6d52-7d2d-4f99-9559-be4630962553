# Relay SWR Debugging Prompt – Validation Report

**Date:** 2025-08-04

This report validates the assumptions and recommendations made in `RELAY_SWR_DEBUGGING_PROMPT.md` by inspecting the current source code.

---

## 1. Executive Summary

| ID | Statement from Prompt | Status | Evidence |
|----|-----------------------|--------|----------|
| G2 | *“RelayEnvironmentLayoutQuery is still using `useLazyLoadQuery`, so the query is never cached”* | **Confirmed** | `RelayEnvironmentLayout.tsx` still imports and calls `useLazyLoadQuery` (see 26-32). |
| G3-A | *“`useSWRQuery` serves stale data **only** when &lt;5 min old; otherwise it blocks on the network.”* | **Refuted** | `useSWRQuery` always sets `fetchPolicy = 'store-only'` for **both** fresh **and** stale cache (287-315). |
| G3-B | *“No background revalidation is triggered.”* | **Refuted** | Background revalidation is scheduled in an effect when `fetchPolicy === 'store-only'` and the cache is stale (451-466). |
| Other | *Pagination slice persistence & selective persistence* | **Not part of this validation** | Out-of-scope for this report. |

---

## 2. Detailed Findings

### 2.1 Layout Query Hook

```24:32:ui/src/components/Layout/RelayEnvironmentLayout/RelayEnvironmentLayout.tsx
// … inside MetadataLoader …
const data = useLazyLoadQuery<FieldMetadataContextQueryType>(
    graphql`
        query RelayEnvironmentLayoutQuery {
            fieldDefinitions {
```

* `useLazyLoadQuery` is still used. No references to `useSWRQuery<RelayEnvironmentLayoutQuery>` exist in the codebase (grep searches returned zero matches).
* Therefore the prompt’s recommendation to migrate this component to `useSWRQuery` is **valid**.

### 2.2 Stale-While-Revalidate Logic

#### 2.2.1 Fetch-Policy Selection

```287:315:ui/src/relay/useSWRQuery.ts
let fetchPolicy: FetchPolicy = 'store-or-network';
…
if (availability.status === 'available') {
    const cacheAge = hydratedAt ? Date.now() - hydratedAt : 0;
    if (!staleFlag && cacheAge < ttlMs) {
        fetchPolicy = 'store-only';            // fresh → cache
        if (cacheAge > REVALIDATE_MS && shouldRevalidate(cacheKey)) {
            shouldTriggerBackgroundRevalidation = true;
        }
    } else {
        fetchPolicy = 'store-only';            // stale → still cache 🔥
        shouldTriggerBackgroundRevalidation = true; // SWR path
    }
}
```

* **Observation**: The hook **never** switches to `'store-or-network'` when cached data exists; it always serves from cache first.
* This aligns with true SWR semantics, contradicting the prompt’s assumption.

#### 2.2.2 Background Revalidation Trigger

```451:466:ui/src/relay/useSWRQuery.ts
if (shouldTriggerBackgroundRevalidation &&
    delayComplete &&
    !backgroundRevalidationTriggered.current &&
    fetchPolicy === 'store-only') {
    backgroundRevalidationTriggered.current = true;
    setTimeout(() => {
        triggerBackgroundRevalidation(query, variables, queryName, cacheKey)
            .catch(/* log */);
    }, 0);
}
```

* The effect schedules a background `fetchQuery` with `force: true`, meeting the SWR requirement.
* Cool-down logic (`shouldRevalidate`) prevents excessive revalidations.

### 2.3 TTL & Freshness Configuration

* `createPersistedStore.ts` sets
  * `window.__RELAY_CACHE_TTL__` to `5 min` by default (513-517).
* `useSWRQuery` uses this runtime value instead of hard-coded `FRESH_TTL_MS`. Constant `FRESH_TTL_MS` (11) is **dead code**.

### 2.4 Minor Observations

1. **Unused Constant** – `FRESH_TTL_MS` in `useSWRQuery.ts` is never referenced after definition.
2. **Debug Build Dependence** – Several debug paths are gated by `process.env.NODE_ENV === 'development'`; make sure production builds retain SWR behaviour (they do – the critical SWR logic is not in dev-only blocks).

---

## 3. Recommendations

1. **Migrate Layout Component** – Replace `useLazyLoadQuery` with `useSWRQuery` in `RelayEnvironmentLayout.tsx` to enable caching and SWR for the layout data.
2. **Remove Dead Code** – Delete or repurpose `FRESH_TTL_MS` to avoid confusion.
3. **Optional** – Add automated tests covering:
   * Cache-hit warm load (<1 min).
   * Stale load (1-5 min) with background revalidation.
   * Very stale (>5 min) ensuring UI still renders from cache.

---

## 4. Conclusion

The codebase already implements true stale-while-revalidate behaviour inside `useSWRQuery`. The only outstanding issue highlighted by the prompt is the **missing adoption** of this hook in `RelayEnvironmentLayout`. All other criticisms in the prompt regarding SWR behaviour are outdated with respect to the current implementation.
