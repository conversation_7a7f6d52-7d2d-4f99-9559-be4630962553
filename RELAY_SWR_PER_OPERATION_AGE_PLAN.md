# Relay SWR – Per-Operation Age Tracking

> **Goal** — Replace the single global `__RELAY_CACHE_SAVED_AT__` timestamp with **per-operation** (per-query) timestamps so that background revalidation decisions are independent for each GraphQL operation.
>
> **Audience** — A developer new to this codebase. The document explains purpose, design options, the chosen solution, step-by-step implementation tasks, and validation strategy.

---

## 1  Problem Recap

* We currently persist the whole store to IndexedDB; every persist writes `savedAt: Date.now()` in the blob.
* On hydration we expose that single timestamp via `window.__RELAY_CACHE_SAVED_AT__`.
* `useSWRQuery` compares **its** query’s age to that global value. If any network request occurs (even for an unrelated query) the next persist resets `savedAt`, making all other queries look “fresh < 30 s”.
* Result: long-lived cache never revalidates unless **its own** data changes, defeating SWR for most screens.

---

## 2  Design Requirements

| ID | Requirement |
|----|-------------|
| R1 | Track freshness **per GraphQL operation** (query name + variables). |
| R2 | Persist the age map so that page reloads keep history. |
| R3 | Keep runtime lookup **O(1)**. |
| R4 | Do **not** bloat IndexedDB significantly. |
| R5 | Maintain backward compatibility; old caches without the new metadata should still load. |

---

## 3  High-Level Solution

### 3.1 Data Model

1. **Operation key** – The existing `createCacheKey(queryName, variables)` already produces a stable string (`"QueryName:{sorted json vars}"`). Use this as the map key.
2. **Age map** – A plain object `{ [cacheKey: string] : number /* savedAt */ }` persisted alongside the records.
3. **Blob shape** – Add a new optional field `opSavedAt` to `PersistedBlob`:
```ts
interface PersistedBlob {
  version: string;
  schemaHash?: string;
  savedAt: number;          // legacy whole-store timestamp (kept for compat)
  opSavedAt?: Record<string, number>; // NEW
  records: RecordMap;
}
```
4. **In-memory cache** – Expose the map at runtime via a global `window.__RELAY_OP_SAVED_AT__` (object) to avoid repeated IndexedDB reads.

### 3.2 Update Paths

| Component | Responsibility |
|-----------|----------------|
| **`createPersistedStore.ts`** | • During hydration, read `opSavedAt` into memory.
• When persisting, merge the runtime map and write back. |
| **`useSWRQuery.ts`** | • Look up `cacheAge` using `window.__RELAY_OP_SAVED_AT__[cacheKey]`.
• After a **successful** background revalidation (`triggerBackgroundRevalidation`), update the map entry to `Date.now()` so subsequent mounts know it is fresh. |
| **`triggerBackgroundRevalidation`** (same file) | After `.toPromise()` resolves, update and schedule persist by calling a small helper. |
| **`persist()`** | Needs an extra argument (new map) or access the global to store `opSavedAt` on every write-back. |

### 3.3 Performance & Size Impact

* 100s of operations × 8-byte timestamp ≈ a few KB. Meets R4.

---

## 4  Implementation Steps

### 4.1 Types & Constants

1. **Add** to `ui/src/relay/types/persistence.ts` (or equivalent):
```ts
export interface PersistedBlobV2 extends PersistedBlob {
  opSavedAt?: Record<string, number>;
}
```
2. Bump `CACHE_VERSION` to `2025-08-xx-op-timestamp`.

### 4.2 Hydration Changes (`createPersistedStore.ts`)

1. When reading the blob (`storedBlob`), extract `storedBlob.opSavedAt ?? {}` into an in-memory `const opSavedAtMap`.
2. Expose globally:
```ts
(window as any).__RELAY_OP_SAVED_AT__ = opSavedAtMap;
```
3. **Do not** overwrite entries; keep the object reference so later updates are persisted.

### 4.3 Persist Changes

1. Inside `persist()` build the blob as:
```ts
const blob: PersistedBlobV2 = {
  ...legacyFields,
  opSavedAt: window.__RELAY_OP_SAVED_AT__
};
```
2. No extra space taken if map is empty (serialises to `{}`).

### 4.4 Hook Logic (`useSWRQuery.ts`)

1. Replace:
```ts
const savedAt = window.__RELAY_CACHE_SAVED_AT__;
```
with
```ts
const savedAt = (window.__RELAY_OP_SAVED_AT__ ?? {})[cacheKey];
```
2. If `savedAt` is `undefined` treat as Infinity (forces immediate revalidation) **or** allow first network.
3. After successful revalidation (`triggerBackgroundRevalidation`):
```ts
(window.__RELAY_OP_SAVED_AT__ ?? {})[cacheKey] = Date.now();
```
4. Schedule a persist to flush the new timestamp (reuse existing debounce `schedule()` via a small `notifyStoreUpdated()` helper imported from `createPersistedStore.ts`).

### 4.5 Backward-Compatibility

* When loading an old blob without `opSavedAt`, the map is `{}` → all queries appear stale and will revalidate once, then populate the map.
* Keep writing the legacy `savedAt` for possible rollback.

---

## 5  Edge-Case Handling

| Scenario | Behaviour |
|----------|-----------|
| **Very large variable objects** | The cache key function already JSON-serialises; unchanged. |
| **Deleted operations** | Their keys remain in the map; can be pruned during persist if no matching records exist. |
| **Quota exceeded** | Existing two-strike policy still applies; map is included in the same blob. |
| **Multi-tab** | Because the map is persisted, revalidation in one tab will freshen timestamps for the next reload of another tab. Real-time cross-tab sync is out of scope. |

---

## 6  Validation & Testing

### 6.1 Unit Tests (Jest + Relay Mock)

1. **Hydration loads `opSavedAt`** – Mock a blob containing `{opSavedAt:{"Q:{}":123}}` and assert global is set.
2. **Cache age calculation** – Age <30 s → no revalidation flag; Age >30 s → flag true.
3. **Map update after revalidation** – Mock `triggerBackgroundRevalidation` success; expect timestamp updated and persisted.

### 6.2 Manual Browser Test

1. Load page → wait >30 s → reload.
   * **Expect** TimesheetRosterQuery triggers network (background) while page still renders stale data.
2. Observe `window.__RELAY_OP_SAVED_AT__` contains an entry for its cache key that updates after response.
3. Validate no other queries’ ages are reset by unrelated network activity.

---

## 7  Roll-out

| Phase | Action |
|-------|--------|
| Dev | Implement & run unit tests. |
| Staging | Enable via feature flag `VITE_SWR_PER_OP_AGE=true`. |
| Prod | Gradual rollout; monitor `relayObservability.trackCacheHit/Miss` ratios. |

---

## 8  Estimated Effort

* **Code changes**: ~300 LOC across 2 files.
* **Tests**: 2–3 Jest cases.
* **QA**: ½ day.
* **Total**: 2 developer-days.

---

## 9  Future Enhancements (out of scope)

* BroadcastChannel to sync `opSavedAt` live across tabs.
* Periodic TTL cleanup of obsolete keys.
* Per-operation configurable revalidation windows.

---

## 10 Manual Testing Guide for Per-Operation SWR

### 10.1 Setup for Testing

1. **Enable Development Mode**: Ensure `NODE_ENV=development` to access debugging tools
2. **Open Browser DevTools**: Console tab for debugging commands
3. **Access a page with multiple GraphQL queries** (e.g. Timesheet dashboard)

### 10.2 Testing Scenarios

#### Scenario 1: Fresh Cache Behavior
```javascript
// 1. Load a page and check initial timestamps
window.__getPerOpTimestamps()

// 2. Reload page within 30 seconds
// Expected: All queries should use cached data (store-only policy)
// Expected: No background revalidation should occur

// 3. Verify timestamps are preserved across reload
window.__getPerOpTimestamps()
```

#### Scenario 2: Stale Cache Revalidation
```javascript
// 1. Clear per-operation timestamps to force revalidation
window.__clearPerOpTimestamps()

// 2. Load page - all queries should trigger background revalidation
// Watch Network tab for GraphQL requests

// 3. Wait for revalidation to complete, check updated timestamps
window.__getPerOpTimestamps()

// 4. Reload immediately - should use cached data
// Network tab should show no new requests
```

#### Scenario 3: Independent Query Aging
```javascript
// 1. Load page with multiple queries (e.g., TimesheetRoster, PayStubList)
// 2. Wait 35+ seconds after load
// 3. Navigate to trigger only one query (e.g., just TimesheetRoster)
// Expected: Only that specific query should revalidate
// Expected: Other query timestamps should remain unchanged

window.__getPerOpTimestamps() // Verify only one timestamp updated
```

#### Scenario 4: Cross-Tab Consistency
```javascript
// 1. Open same page in two tabs
// 2. In Tab 1: Clear timestamps - window.__clearPerOpTimestamps()
// 3. In Tab 1: Reload page (triggers revalidation)
// 4. In Tab 2: Reload page
// Expected: Tab 2 should use fresh cache from Tab 1's revalidation
// Expected: No additional network requests in Tab 2
```

### 10.3 Debug Commands Reference

```javascript
// View all per-operation timestamps with age analysis
window.__getPerOpTimestamps()

// Clear all timestamps (force revalidation for all queries)
window.__clearPerOpTimestamps()

// View background revalidation statistics
window.__getSWRStats()

// Enable 10-second debug delay to test cache vs network clearly
window.__enableCacheDelay()
// Disable delay
window.__disableCacheDelay()

// Full cache inspection
window.__RELAY_DEV__ // If available

// Check what's in IndexedDB for persistence
// (DevTools > Application > Storage > IndexedDB > relay-cache-v1)
```

### 10.4 Validation Checklist

- [ ] **Per-operation independence**: Different queries age independently
- [ ] **Background revalidation**: Stale queries revalidate without blocking UI
- [ ] **Timestamp persistence**: Timestamps survive page reloads
- [ ] **Cache size impact**: Minimal IndexedDB size increase (few KB for 100s of operations)
- [ ] **Backward compatibility**: Works with existing cached data
- [ ] **Cross-tab sync**: Fresh data from one tab available in others after reload
- [ ] **Error handling**: Failed revalidations don't update timestamps
- [ ] **Performance**: No noticeable performance impact on page loads

### 10.5 Expected Behaviors

| Scenario | Expected Behavior |
|----------|------------------|
| Fresh cache (< 30s) | `store-only` policy, no network requests |
| Stale cache (> 30s) | `store-only` + background revalidation |
| Missing timestamp | `store-only` + immediate background revalidation |
| Failed revalidation | Timestamp unchanged, retry on next access |
| Multiple queries | Independent aging and revalidation |
| Page reload | Preserved timestamps from IndexedDB |

### 10.6 Troubleshooting

**Issue**: Queries always revalidate despite recent cache
- **Check**: `window.__getPerOpTimestamps()` - ensure timestamps exist
- **Verify**: Cache key format matches query name and variables

**Issue**: Background revalidation not triggering
- **Check**: Console for any JavaScript errors
- **Verify**: `window.__RELAY_OP_SAVED_AT__` object exists and updates

**Issue**: Cross-tab sync not working
- **Check**: IndexedDB persistence is working (no quota errors)
- **Verify**: Same origin and same cache version

**Issue**: Performance problems
- **Check**: `window.__validateCachePerformance()` for cache efficiency
- **Monitor**: Network requests and cache hit rates

---

**End of plan** – implementing the above will ensure each query decides freshness based on when **it** last fetched, eliminating false "fresh" readings caused by unrelated network traffic.
