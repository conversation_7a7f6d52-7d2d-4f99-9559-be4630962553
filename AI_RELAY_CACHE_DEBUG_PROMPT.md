# Relay Cache – Network Fetch Despite store-only

## Objective
We need an expert analysis (and eventual code fix proposal) for why our Relay-based frontend still issues **network requests** even when:
1. `environment.check(operation)` returns `status: "available"`
2. `useLazyLoadQuery` is invoked with `fetchPolicy: 'store-only'` and (when applicable) `UNSTABLE_renderPolicy: 'full'`
3. The hydrated cache is < 5 min old (TTL window) and contains all expected records.

The current debugging effort (by another AI assistant) has exhausted two hypotheses:
* **Global invalidation epoch** – manually resetting via `store.invalidateStore()` did **not** help.
* **useLazyLoadQuery automatic re-validate** – switching to `loadQuery + usePreloadedQuery` also did **not** help and introduced warnings.

We need fresh eyes on the problem.

---

## Repository context
Frontend uses **React 18**, **Relay 14+** (runtime + react-relay), **Vite**, **IndexedDB selective persistence**.  Cache layer is custom but mostly conventional.

### Key implementation files (paths are project-root relative)
| File | Purpose |
|------|---------|
| `ui/src/relay/useSWRQuery.ts` | Custom SWR-style hook wrapping `useLazyLoadQuery`. Contains TTL & fetchPolicy logic and extensive debug logging. |
| `ui/src/relay/createPersistedStore.ts` | IndexedDB persistence: loads blob into `RecordSource`, writes back on schedule. Injects global `__RELAY_CACHE_HYDRATED_AT__` and `__RELAY_CACHE_TTL__`. |
| `ui/src/relay/initRelayEnvironment.ts` | Async environment factory. (Now identical to Relay defaults; no epoch reset). |
| `ui/src/container/TimesheetRoster/TimesheetRoster.tsx` | Main screen that calls `useSWRQuery` with `TimesheetRosterQuery`. |
| `ui/src/components/TimesheetRoster/TimesheetRosterTable/TimesheetRosterTable.tsx` | Contains fragment & pagination logic but not directly part of first fetch. |

### High-level flow
1. Page boot → `createPersistedStore` hydrates cache; sets timestamp + TTL.
2. `initRelayEnvironment` returns `Environment` with this store.
3. `TimesheetRoster.tsx` calls `useSWRQuery`.
4. In `useSWRQuery`:
   * Pre-check (`environment.check`) logs **AVAILABLE**.
   * Because data is fresh, hook sets `fetchPolicy = 'store-only'`.
   * Yet Relay still fires a network request and logs `cacheStatus: 'MISS'`.

### Representative console log
```text
🚀 Starting GraphQL query: TimesheetRosterQuery {fetchPolicy:'store-only'}
✅ Relay pre-check: 'TimesheetRosterQuery' is AVAILABLE from cache
✅ GraphQL query completed: TimesheetRosterQuery {loadTime:'59ms', fromCache:false, cacheStatus:'MISS'}
🔍 Cache Miss Analysis: TimesheetRosterQuery – Reason: network_first_policy
```

### Previous attempts & outcomes
| Attempt | Files touched | Result |
|---------|---------------|--------|
| **Epoch reset** – `environment.commitUpdate(store => store.invalidateStore())` right after env creation | `ui/src/relay/initRelayEnvironment.ts` | No change; network fetch still fires. |
| **Preload pattern** – replaced `useLazyLoadQuery` with `loadQuery + usePreloadedQuery` | `ui/src/relay/useSWRQuery.ts` | Network fetch still fires **and** React warned: `usePreloadedQuery(): Expected preloadedQuery to not be disposed yet.` Reverted. |

### Additional observations
* Only one mount of `TimesheetRosterQuery` in non-Strict builds ⇒ no duplicate query mounts.
* `availability.isStale` during pre-check is **false**.
* Root record (`client:root`) **is persisted**; store snapshot shows expected business records.

---

## What we need from you
1. Identify the true root cause of the network fetch despite cache availability.
2. Provide a minimally invasive code fix (file & line numbers) that guarantees **zero** network request on first navigation when cache is fresh.
3. Outline a validation plan (manual + optional unit test).
4. (Stretch) Suggest automated guardrails to prevent regression.

Please reference the files above and assume full access to the repository.

---

## Quick links for your convenience
* [`ui/src/relay/useSWRQuery.ts`](ui/src/relay/useSWRQuery.ts)
* [`ui/src/relay/createPersistedStore.ts`](ui/src/relay/createPersistedStore.ts)
* [`ui/src/relay/initRelayEnvironment.ts`](ui/src/relay/initRelayEnvironment.ts)
* [`ui/src/container/TimesheetRoster/TimesheetRoster.tsx`](ui/src/container/TimesheetRoster/TimesheetRoster.tsx)

---

> **Focus**: Why does Relay ignore `store-only` when `check()` returned available and data is not stale?

Thank you!
