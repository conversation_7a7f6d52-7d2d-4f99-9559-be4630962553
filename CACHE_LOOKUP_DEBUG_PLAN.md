# Cache Lookup Debug Plan

**Created**: 2025-08-04  
**Priority**: Critical  
**Status**: Research & Implementation  

---

## Problem Statement

Despite achieving **99.9% cache persistence rate** (2503/2505 records) with the new denylist approach, **all queries are still going to network** (`fromCache: false`, `cacheStatus: 'MISS'`). This indicates a critical disconnect between cache persistence and <PERSON>lay's query lookup mechanism.

### Key Evidence
```
✅ Cache Persistence: 99.9% success (2503 records, 998KB)
❌ Cache Retrieval: 0% success (all queries miss cache)
📊 Store Contents: 2465 records including TimeSheet, Employer, Organization
🔍 Query Analysis: Shows "Expected Records: 0" (broken analysis logic)
🐌 Debug Delay: Not working (shows 'disabled' always)
```

---

## Research Hypothesis

**Primary Theory**: Cache keys generated during persistence don't match cache keys used during Relay query lookup.

**Secondary Theories**:
1. Store hydration failure - IndexedDB data not properly loaded into Relay store
2. Query variable normalization differences between save/lookup
3. Connection/pagination parameters causing cache key mismatches
4. Relay environment initialization timing issues

---

## Investigation Plan

### Phase 1: Cache Key Analysis (Week 1)
**Objective**: Determine if cache keys match between persistence and lookup

#### Task 1.1: Cache Key Comparison Tool
- **Goal**: Create tool to compare persistence keys vs. query lookup keys
- **Deliverable**: Function to log both key types for same query
- **Files**: `ui/src/relay/debug.ts`, `ui/src/relay/devUtils.ts`

#### Task 1.2: Store Hydration Verification  
- **Goal**: Verify IndexedDB data properly loads into Relay store
- **Deliverable**: Startup diagnostic showing before/after store contents
- **Files**: `ui/src/relay/createPersistedStore.ts`, `ui/src/relay/initRelayEnvironment.ts`

#### Task 1.3: Query Variable Analysis
- **Goal**: Compare query variables during persistence vs. lookup
- **Deliverable**: Side-by-side variable comparison logging
- **Files**: `ui/src/relay/debug.ts`, `ui/src/relay/useSWRQuery.ts`

### Phase 2: Relay Integration Deep Dive (Week 1)
**Objective**: Understand how Relay performs cache lookups

#### Task 2.1: Query Execution Tracing
- **Goal**: Trace complete query execution path from request to cache lookup
- **Deliverable**: Step-by-step logging of Relay's cache lookup process
- **Files**: `ui/src/relay/fetchRelay.ts`, `ui/src/relay/debug.ts`

#### Task 2.2: Store Record Mapping
- **Goal**: Map persisted records to Relay store structure
- **Deliverable**: Tool showing how persisted data appears in Relay store
- **Files**: `ui/src/relay/devUtils.ts`

#### Task 2.3: Connection Cache Analysis
- **Goal**: Analyze connection/pagination specific cache behavior
- **Deliverable**: Connection-specific cache lookup diagnostics
- **Files**: `ui/src/relay/debug.ts`

### Phase 3: Debug Tool Repair (Week 1-2)
**Objective**: Fix broken debug tools to enable proper investigation

#### Task 3.1: Query Analysis Repair
- **Goal**: Fix "Expected Records: 0" issue in query analysis
- **Deliverable**: Working query analysis showing actual expected records
- **Files**: `ui/src/relay/debug.ts`, `ui/src/relay/useSWRQuery.ts`

#### Task 3.2: Debug Delay Implementation
- **Goal**: Restore 10-second debug delay functionality
- **Deliverable**: Working `window.__enableCacheDelay()` mechanism
- **Files**: `ui/src/relay/fetchRelay.ts`, `ui/src/relay/debug.ts`

#### Task 3.3: Cache Miss Reason Enhancement
- **Goal**: Provide specific reasons why cache lookups fail
- **Deliverable**: Detailed cache miss analysis with actionable information
- **Files**: `ui/src/relay/debug.ts`, `ui/src/relay/devUtils.ts`

### Phase 4: Root Cause Resolution (Week 2)
**Objective**: Implement fixes based on investigation findings

#### Task 4.1: Cache Key Normalization
- **Goal**: Ensure consistent cache key generation between persistence and lookup
- **Deliverable**: Unified cache key generation mechanism
- **Files**: TBD based on findings

#### Task 4.2: Store Hydration Fix
- **Goal**: Ensure proper store hydration from IndexedDB
- **Deliverable**: Verified store hydration with diagnostic logging
- **Files**: `ui/src/relay/createPersistedStore.ts`

#### Task 4.3: Query Execution Integration
- **Goal**: Fix any integration issues between cache and query execution
- **Deliverable**: Queries hitting cache consistently
- **Files**: TBD based on findings

---

## Implementation Approach

### Diagnostic Tools to Create

#### 1. Cache Key Comparison Tool
```javascript
window.__RELAY_DEV__.compareCacheKeys(queryName, variables) 
// Shows persistence key vs lookup key for same query
```

#### 2. Store Hydration Diagnostics
```javascript
window.__RELAY_DEV__.validateStoreHydration()
// Compares IndexedDB contents vs Relay store contents
```

#### 3. Query Execution Tracer
```javascript
window.__RELAY_DEV__.traceQueryExecution(queryName)
// Step-by-step logging of cache lookup process
```

#### 4. Cache Miss Deep Analysis
```javascript
window.__RELAY_DEV__.analyzeCacheMissDeep(queryName)
// Detailed analysis of why specific query missed cache
```

### Research Methodology

#### Evidence Collection
1. **Before Query**: Log expected cache keys and store state
2. **During Query**: Trace Relay's cache lookup attempts
3. **After Query**: Compare what was found vs. what was expected
4. **Cross-Reference**: Match persistence logs with lookup logs

#### Hypothesis Testing
1. **Test Cache Key Theory**: Compare generated keys at each stage
2. **Test Hydration Theory**: Verify store contents match IndexedDB
3. **Test Variable Theory**: Compare variable serialization
4. **Test Timing Theory**: Check initialization order

---

## Success Criteria

### Primary Success Metrics
- [ ] **Cache Hit Rate >80%**: Queries hitting cache instead of network
- [ ] **Cache Key Matching**: Persistence keys match lookup keys
- [ ] **Store Hydration Verified**: IndexedDB data properly loaded
- [ ] **Debug Tools Working**: All analysis tools provide accurate data

### Diagnostic Success Metrics
- [ ] **Query Analysis Fixed**: Shows actual expected records (not 0)
- [ ] **Debug Delay Working**: 10-second delay enables cache vs network testing
- [ ] **Cache Miss Reasons**: Specific, actionable reasons for cache misses
- [ ] **Store Mapping Clear**: Can trace from persisted data to Relay store

### Performance Success Metrics
- [ ] **Page Load Time**: <2s for cached scenarios
- [ ] **Network Request Reduction**: >60% fewer API calls for repeat visits
- [ ] **Cache Effectiveness**: Measurable improvement in user experience

---

## Risk Assessment

### High Risk Items
1. **Deep Relay Integration**: May require understanding complex Relay internals
2. **Cache Key Complexity**: GraphQL cache key generation is intricate
3. **Timing Dependencies**: Store hydration timing critical

### Mitigation Strategies
1. **Incremental Approach**: Build understanding step by step
2. **Diagnostic First**: Create tools before attempting fixes
3. **Evidence Based**: Only make changes based on concrete findings
4. **Rollback Plan**: Maintain ability to revert changes

---

## Dependencies

### Technical Dependencies
- **Relay Store API**: Understanding of Relay's internal store structure
- **IndexedDB**: Proper IndexedDB persistence and retrieval
- **GraphQL Query Parsing**: Ability to extract cache keys from queries
- **Browser DevTools**: Console logging and debugging capabilities

### Knowledge Dependencies
- **Relay Cache Mechanism**: How Relay performs cache lookups
- **GraphQL Normalization**: How GraphQL responses are normalized
- **Connection Patterns**: How Relay handles connections and pagination
- **Query Variable Serialization**: How variables affect cache keys

---

## Timeline

### Week 1: Investigation & Diagnostics
- **Days 1-2**: Cache key analysis and comparison tools
- **Days 3-4**: Store hydration verification and query tracing
- **Days 5-7**: Debug tool repair and initial findings analysis

### Week 2: Root Cause & Resolution
- **Days 8-10**: Implement fixes based on investigation findings
- **Days 11-12**: Testing and validation of fixes
- **Days 13-14**: Performance verification and documentation

---

## Expected Deliverables

### Research Deliverables
1. **Root Cause Analysis Report**: Detailed findings on why cache lookups fail
2. **Cache Key Analysis**: Comparison of persistence vs lookup key generation
3. **Store Hydration Report**: Analysis of IndexedDB to Relay store loading
4. **Query Execution Flow**: Documentation of Relay's cache lookup process

### Implementation Deliverables
1. **Enhanced Debug Tools**: Working cache analysis and debugging utilities
2. **Cache Lookup Fixes**: Implementation addressing root cause
3. **Performance Validation**: Metrics showing improved cache hit rates
4. **Documentation Updates**: Updated debugging guides and troubleshooting

### Testing Deliverables
1. **Diagnostic Test Suite**: Tests validating cache lookup mechanisms
2. **Performance Benchmarks**: Before/after performance comparisons
3. **Integration Tests**: End-to-end cache behavior validation
4. **Debug Tool Tests**: Validation of all debugging utilities

---

## Notes

- **Evidence-Driven**: All decisions based on concrete diagnostic data
- **Incremental Progress**: Build understanding progressively
- **Tool-First Approach**: Create diagnostic tools before attempting fixes
- **Performance Focus**: Measure impact at each stage
- **Documentation**: Maintain clear record of findings and solutions

This plan prioritizes understanding the problem deeply before attempting solutions, using a systematic diagnostic approach to identify the root cause of the cache lookup failure despite successful persistence.