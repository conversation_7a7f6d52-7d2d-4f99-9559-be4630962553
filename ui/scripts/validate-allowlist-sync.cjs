#!/usr/bin/env node

/**
 * CI Allowlist Synchronization Validator
 * 
 * This script validates that:
 * 1. Allowlists are synchronized with the current GraphQL schema
 * 2. No drift exists between schema.graphql and cacheFilters.ts
 * 3. Generated allowlists meet performance requirements
 * 
 * Exit codes:
 * 0 - Allowlists are up-to-date
 * 1 - Allowlists need regeneration (schema drift detected)
 * 2 - Performance requirements not met
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

let hasErrors = false;

function logError(message) {
    console.error(`❌ ${message}`);
    hasErrors = true;
}

function logSuccess(message) {
    console.log(`✅ ${message}`);
}

function logWarning(message) {
    console.warn(`⚠️  ${message}`);
}

function logInfo(message) {
    console.log(`ℹ️  ${message}`);
}

/**
 * Check if allowlists are up-to-date by running generation and checking for changes
 */
function validateAllowlistSync() {
    logInfo('Validating allowlist synchronization with schema...');
    
    try {
        // Read current cacheFilters.ts content
        const cacheFiltersPath = path.join(process.cwd(), 'src/relay/cacheFilters.ts');
        const originalContent = fs.readFileSync(cacheFiltersPath, 'utf8');
        
        // Run allowlist generation
        logInfo('Running allowlist generation...');
        execSync('pnpm gen:allowlist', { stdio: 'pipe' });
        
        // Check if content changed (ignore timestamp differences)
        const newContent = fs.readFileSync(cacheFiltersPath, 'utf8');
        
        // Remove timestamps for comparison
        const normalizeContent = (content) => {
            return content.replace(/auto-generated on [\d\-T:.Z]+/, 'auto-generated on [TIMESTAMP]');
        };
        
        const normalizedOriginal = normalizeContent(originalContent);
        const normalizedNew = normalizeContent(newContent);
        
        if (normalizedOriginal === normalizedNew) {
            logSuccess('Allowlists are synchronized with schema');
            return true;
        } else {
            logError('Schema drift detected! Allowlists are out of sync with schema.graphql');
            logError('Run `pnpm gen:allowlist` to update allowlists and commit the changes');
            
            // Show a diff summary
            const originalLines = originalContent.split('\n').length;
            const newLines = newContent.split('\n').length;
            logInfo(`Content change detected: ${originalLines} -> ${newLines} lines`);
            
            return false;
        }
        
    } catch (error) {
        logError(`Failed to validate allowlist sync: ${error.message}`);
        return false;
    }
}

/**
 * Run allowlist performance tests to ensure <10% unknown record rate
 */
function validateAllowlistPerformance() {
    logInfo('Validating allowlist performance requirements...');
    
    try {
        // Run the allowlist-specific tests
        const testOutput = execSync(
            'pnpm test --testPathPattern="allowlist.test.ts" --verbose', 
            { encoding: 'utf8', stdio: 'pipe' }
        );
        
        // Check for performance indicators in test output
        if (testOutput.includes('unknown record rate')) {
            const unknownRateMatch = testOutput.match(/unknown record rate:\s*([\d.]+)%/);
            if (unknownRateMatch) {
                const unknownRate = parseFloat(unknownRateMatch[1]);
                if (unknownRate < 10) {
                    logSuccess(`Allowlist performance target met: ${unknownRate.toFixed(1)}% unknown record rate`);
                    return true;
                } else {
                    logError(`Allowlist performance target not met: ${unknownRate.toFixed(1)}% unknown record rate (>10%)`);
                    return false;
                }
            }
        }
        
        logSuccess('Allowlist performance tests passed');
        return true;
        
    } catch (error) {
        logError(`Allowlist performance validation failed: ${error.message}`);
        return false;
    }
}

/**
 * Validate that critical business entities are included
 */
function validateCriticalEntities() {
    logInfo('Validating critical business entity coverage...');
    
    try {
        const cacheFiltersPath = path.join(process.cwd(), 'src/relay/cacheFilters.ts');
        const content = fs.readFileSync(cacheFiltersPath, 'utf8');
        
        const criticalEntities = [
            'TimeSheet',
            'PayStub', 
            'PayStubDetail',
            'Employee',
            'Agreement'
        ];
        
        let missingEntities = [];
        
        criticalEntities.forEach(entity => {
            if (!content.includes(`'${entity}'`)) {
                missingEntities.push(entity);
            }
        });
        
        if (missingEntities.length === 0) {
            logSuccess(`All critical business entities are included`);
            return true;
        } else {
            logError(`Missing critical business entities: ${missingEntities.join(', ')}`);
            return false;
        }
        
    } catch (error) {
        logError(`Failed to validate critical entities: ${error.message}`);
        return false;
    }
}

/**
 * Main validation runner
 */
function main() {
    console.log('🔍 Running CI Allowlist Validation...\n');
    
    let allPassed = true;
    
    // Run all validations
    allPassed &= validateCriticalEntities();
    allPassed &= validateAllowlistSync();
    allPassed &= validateAllowlistPerformance();
    
    console.log('');
    
    if (allPassed) {
        logSuccess('All allowlist validations passed! 🎉');
        console.log('💡 Allowlists are synchronized and meet performance requirements.');
        process.exit(0);
    } else {
        logError('Allowlist validation failed!');
        console.log('');
        console.log('📋 To fix allowlist issues:');
        console.log('   1. Run `pnpm gen:allowlist` to regenerate allowlists');
        console.log('   2. Review and commit the updated cacheFilters.ts');
        console.log('   3. Ensure all tests pass with `pnpm test allowlist`');
        console.log('   4. Re-run this validation');
        process.exit(1);
    }
}

// Execute if run directly
if (require.main === module) {
    main();
}

module.exports = { 
    validateAllowlistSync, 
    validateAllowlistPerformance, 
    validateCriticalEntities 
};