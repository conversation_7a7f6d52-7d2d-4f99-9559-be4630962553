#!/usr/bin/env node

/**
 * Security Audit Script for Relay Selective Cache Persistence
 * 
 * This script validates that no PII (Personally Identifiable Information) 
 * is being persisted to IndexedDB cache storage.
 * 
 * Specifically checks for:
 * - Email addresses
 * - Phone numbers
 * - Authentication tokens/errors
 * - User credentials
 */

import { openDB } from 'idb';

const DB_NAME = 'relay-cache-v1';
const STORE_KEY = 'records';

// PII patterns to search for
const PII_PATTERNS = [
  // Email patterns
  /email/i,
  /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/,
  
  // Phone number patterns
  /phoneNumber/i,
  /phoneNumberExtension/i,
  /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/,    // US phone format
  /\(\d{3}\)\s?\d{3}[-.]?\d{4}/,      // (************* format
  
  // Auth patterns
  /password/i,
  /token/i,
  /bearer/i,
  /authorization/i,
  /credential/i,
  
  // SSN patterns (just in case)
  /\b\d{3}-\d{2}-\d{4}\b/,
  /ssn/i,
  
  // Other sensitive data
  /bankAccount/i,
  /creditCard/i,
  /socialSecurity/i
];

// Expected PII field names from UserInfoOutput
const EXPECTED_PII_FIELDS = [
  'email',
  'phoneNumber', 
  'phoneNumberExtension',
  'firstName',
  'lastName'
];

class SecurityAuditResults {
  constructor() {
    this.totalRecordsScanned = 0;
    this.piiViolations = [];
    this.suspiciousRecords = [];
    this.authErrorsFound = [];
    this.clientRootFound = false;
    this.userInfoRecordsFound = [];
  }

  addPiiViolation(dataID, field, value, pattern) {
    this.piiViolations.push({
      dataID,
      field,
      value: typeof value === 'string' ? value.substring(0, 50) + '...' : value,
      pattern: pattern.toString(),
      severity: 'HIGH'
    });
  }

  addSuspiciousRecord(dataID, reason, sample) {
    this.suspiciousRecords.push({
      dataID,
      reason,
      sample: JSON.stringify(sample).substring(0, 100) + '...'
    });
  }

  addAuthError(dataID, errorContent) {
    this.authErrorsFound.push({
      dataID,
      error: JSON.stringify(errorContent).substring(0, 100) + '...'
    });
  }

  addUserInfoRecord(dataID, record) {
    this.userInfoRecordsFound.push({
      dataID,
      hasEmail: 'email' in record,
      hasPhone: 'phoneNumber' in record,
      recordKeys: Object.keys(record)
    });
  }

  getOverallStatus() {
    if (this.piiViolations.length > 0) return 'FAILED';
    if (this.authErrorsFound.length > 0) return 'FAILED';
    if (this.clientRootFound) return 'FAILED';
    if (this.userInfoRecordsFound.length > 0) return 'WARNING';
    if (this.suspiciousRecords.length > 0) return 'WARNING';
    return 'PASSED';
  }
}

/**
 * Scan a record for PII patterns
 */
function scanRecordForPII(dataID, record, results) {
  const recordStr = JSON.stringify(record);
  
  // Check each PII pattern
  PII_PATTERNS.forEach(pattern => {
    if (pattern.test(recordStr)) {
      // Find the specific field that matched
      if (typeof record === 'object' && record !== null) {
        for (const [key, value] of Object.entries(record)) {
          if (pattern.test(key) || (typeof value === 'string' && pattern.test(value))) {
            results.addPiiViolation(dataID, key, value, pattern);
          }
        }
      }
    }
  });

  // Check for UserInfoOutput records specifically
  if (record && typeof record === 'object' && record.__typename === 'UserInfoOutput') {
    results.addUserInfoRecord(dataID, record);
  }

  // Check for auth errors
  if (record && typeof record === 'object') {
    if (record.__errors || record.__typename === 'Error' || 
        record.extensions?.authError || record.extensions?.networkError) {
      results.addAuthError(dataID, record);
    }
  }

  // Check for client:root
  if (dataID === 'client:root') {
    results.clientRootFound = true;
  }

  // Check for other suspicious patterns
  if (typeof record === 'object' && record !== null) {
    const keys = Object.keys(record);
    if (keys.some(key => key.toLowerCase().includes('auth'))) {
      results.addSuspiciousRecord(dataID, 'Contains auth-related fields', record);
    }
    if (keys.some(key => key.toLowerCase().includes('session'))) {
      results.addSuspiciousRecord(dataID, 'Contains session-related fields', record);
    }
  }
}

/**
 * Main security audit function
 */
async function runSecurityAudit() {
  console.log('🔐 Starting Security Audit for Relay Selective Cache Persistence...\n');

  const results = new SecurityAuditResults();

  try {
    // Open IndexedDB
    const db = await openDB(DB_NAME, 1);
    
    // Get cached data
    const storedBlob = await db.get('kv', STORE_KEY);
    
    if (!storedBlob) {
      console.log('ℹ️  No cached data found in IndexedDB');
      console.log('Status: PASSED (No data to audit)\n');
      return;
    }

    console.log(`📊 Found cached data blob: ${JSON.stringify(storedBlob).length} bytes`);
    
    // Handle both old and new blob formats
    const records = storedBlob.records || storedBlob;
    const recordCount = Object.keys(records).length;
    
    console.log(`🔍 Scanning ${recordCount} cached records for PII...\n`);

    // Scan each record
    for (const [dataID, record] of Object.entries(records)) {
      results.totalRecordsScanned++;
      scanRecordForPII(dataID, record, results);
    }

    // Generate report
    console.log('='.repeat(60));
    console.log('📋 SECURITY AUDIT REPORT');
    console.log('='.repeat(60));
    
    console.log(`📊 Records Scanned: ${results.totalRecordsScanned}`);
    console.log(`🔐 Overall Status: ${results.getOverallStatus()}`);
    console.log('');

    // PII Violations (Critical)
    if (results.piiViolations.length > 0) {
      console.log('🚨 CRITICAL: PII Violations Found');
      console.log('-'.repeat(40));
      results.piiViolations.forEach((violation, i) => {
        console.log(`${i + 1}. DataID: ${violation.dataID}`);
        console.log(`   Field: ${violation.field}`);
        console.log(`   Value: ${violation.value}`);
        console.log(`   Pattern: ${violation.pattern}`);
        console.log('');
      });
    } else {
      console.log('✅ No PII violations found');
    }

    // Auth Errors (Critical)
    if (results.authErrorsFound.length > 0) {
      console.log('🚨 CRITICAL: Auth Errors in Cache');
      console.log('-'.repeat(40));
      results.authErrorsFound.forEach((authError, i) => {
        console.log(`${i + 1}. DataID: ${authError.dataID}`);
        console.log(`   Error: ${authError.error}`);
        console.log('');
      });
    } else {
      console.log('✅ No auth errors found in cache');
    }

    // Client Root Check (Critical)
    if (results.clientRootFound) {
      console.log('🚨 CRITICAL: client:root record found in cache');
    } else {
      console.log('✅ client:root properly excluded from cache');
    }

    // UserInfo Records (Warning)
    if (results.userInfoRecordsFound.length > 0) {
      console.log('⚠️  WARNING: UserInfoOutput records found');
      console.log('-'.repeat(40));
      results.userInfoRecordsFound.forEach((userInfo, i) => {
        console.log(`${i + 1}. DataID: ${userInfo.dataID}`);
        console.log(`   Has Email: ${userInfo.hasEmail}`);
        console.log(`   Has Phone: ${userInfo.hasPhone}`);
        console.log(`   Fields: ${userInfo.recordKeys.join(', ')}`);
        console.log('');
      });
    } else {
      console.log('✅ No UserInfoOutput records in cache');
    }

    // Suspicious Records (Info)
    if (results.suspiciousRecords.length > 0) {
      console.log('ℹ️  Suspicious Records Found');
      console.log('-'.repeat(40));
      results.suspiciousRecords.slice(0, 5).forEach((suspicious, i) => {
        console.log(`${i + 1}. DataID: ${suspicious.dataID}`);
        console.log(`   Reason: ${suspicious.reason}`);
        console.log(`   Sample: ${suspicious.sample}`);
        console.log('');
      });
      if (results.suspiciousRecords.length > 5) {
        console.log(`   ... and ${results.suspiciousRecords.length - 5} more`);
      }
    }

    console.log('='.repeat(60));
    
    // Final recommendations
    const status = results.getOverallStatus();
    switch (status) {
      case 'FAILED':
        console.log('❌ AUDIT FAILED: Critical security issues found');
        console.log('🔧 ACTION REQUIRED: Fix PII leakage before deployment');
        break;
      case 'WARNING':
        console.log('⚠️  AUDIT WARNING: Review flagged records');
        console.log('💡 RECOMMENDED: Verify filtering logic');
        break;
      case 'PASSED':
        console.log('✅ AUDIT PASSED: No security issues detected');
        console.log('🎉 Cache persistence is secure for production');
        break;
    }

    console.log('\n📖 Audit completed successfully');
    
    // Exit with appropriate code
    process.exit(status === 'FAILED' ? 1 : 0);

  } catch (error) {
    console.error('❌ Security audit failed:', error.message);
    
    if (error.name === 'NotFoundError') {
      console.log('ℹ️  This is normal if IndexedDB is not yet initialized');
      console.log('✅ Status: PASSED (No cached data to audit)');
      process.exit(0);
    } else {
      console.log('🔧 Please ensure the application has run and cached some data');
      process.exit(1);
    }
  }
}

// Run the audit
if (typeof window === 'undefined') {
  // Node.js environment - we need to simulate a browser environment
  console.log('⚠️  Running in Node.js - simulating browser environment...');
  
  // Import required polyfills for Node.js
  global.self = global;
  global.window = global;
  
  runSecurityAudit().catch(error => {
    console.error('Fatal error during security audit:', error);
    process.exit(1);
  });
} else {
  // Browser environment
  runSecurityAudit();
}