#!/usr/bin/env node

/**
 * Allowlist Generator for Relay Cache Selective Persistence
 * 
 * This script automatically generates business entity allowlists by:
 * 1. Reading the GraphQL schema from schema.graphql
 * 2. Identifying types that implement the Node interface (business entities)
 * 3. Updating cacheFilters.ts with the generated allowlists
 * 
 * Usage: pnpm gen:allowlist
 */

const fs = require('fs');
const path = require('path');

// Configuration
const SCHEMA_PATH = path.join(process.cwd(), 'src/relay/schema.graphql');
const CACHE_FILTERS_PATH = path.join(process.cwd(), 'src/relay/cacheFilters.ts');

// Business entity detection patterns
const NODE_IMPLEMENTS_PATTERN = /^type\s+(\w+)\s+implements\s+Node\s*{/gm;

// Known business entity types that should always be included (fallback/manual additions)
const CORE_BUSINESS_ENTITIES = [
  'TimeSheet',
  'PayStub', 
  'PayStubDetail',
  'Employee',
  'Agreement',
  'Organization',
  'User',
  'Classification',
  'SubClassification',
  'EarningsCode',
  'CustomViews'
];

// Types that should be excluded even if they implement Node (system/metadata types)
const EXCLUDED_TYPES = [
  'ChaptersInfoDto',
  'ThirdPartyInfoDto', 
  'UnionRosterDto',
  'BenefitElectionsRosterDto'
];

/**
 * Read and parse the GraphQL schema file
 */
function readSchema() {
  try {
    return fs.readFileSync(SCHEMA_PATH, 'utf8');
  } catch (error) {
    console.error(`❌ Error reading schema file at ${SCHEMA_PATH}:`, error);
    process.exit(1);
  }
}

/**
 * Extract business entity types from GraphQL schema
 */
function extractBusinessEntities(schema) {
  const entityTypes = new Set();
  
  // Add core business entities first
  CORE_BUSINESS_ENTITIES.forEach(type => entityTypes.add(type));
  
  // Find all types that implement Node interface
  // Reset regex to start from beginning
  NODE_IMPLEMENTS_PATTERN.lastIndex = 0;
  let match;
  while ((match = NODE_IMPLEMENTS_PATTERN.exec(schema)) !== null) {
    const typeName = match[1];
    
    // Skip excluded types
    if (!EXCLUDED_TYPES.includes(typeName)) {
      entityTypes.add(typeName);
    }
  }
  
  return Array.from(entityTypes).sort();
}

/**
 * Generate DataID patterns from business entity types
 */
function generateDataIdPatterns(businessEntities) {
  const patterns = businessEntities.map(entityType => `${entityType}:\\w+`);
  
  // Add connection patterns
  patterns.push('client:.*:__connection');
  
  return patterns.sort();
}

/**
 * Generate TypeScript code for the allowlists
 */
function generateTypeScriptCode(result) {
  const businessEntitiesCode = `// Core business entity types that should always be cached
const BUSINESS_ENTITY_TYPES = new Set([
  // Core timesheet entities (confirmed from mutation files)
  'TimeSheet',
  'PayStub', 
  'PayStubDetail',
  
  // Employee-related entities (confirmed from fragments)
  'Employee',
  'Agreement',
  
  // Organization entities (commonly referenced)
  'Organization',
  'User', // Only business User records with proper IDs
  
  // Common business reference types (keep for cache efficiency)
  'Classification',
  'SubClassification',
  'EarningsCode',
  
  // UI configuration entities with custom size thresholds
  'CustomViews',
  
  // Additional entities found in schema (auto-generated)
${result.businessEntityTypes
  .filter(type => !CORE_BUSINESS_ENTITIES.includes(type))
  .map(type => `  '${type}',`)
  .join('\n')}
]);`;

  const dataIdPatternsCode = `// Patterns for allowed DataIDs (business records and connections)
const ALLOWED_DATAID_PATTERNS = [
${result.allowedDataIdPatterns.map(pattern => {
    const comment = pattern.replace('\\w+', 'entities').replace('.*', 'various').replace(':', ' ');
    return `  /^${pattern}$/,           // ${comment}`;
  }).join('\n')}
];`;

  return { businessEntitiesCode, dataIdPatternsCode };
}

/**
 * Update the cacheFilters.ts file with generated allowlists
 */
function updateCacheFilters(result) {
  try {
    let content = fs.readFileSync(CACHE_FILTERS_PATH, 'utf8');
    const { businessEntitiesCode, dataIdPatternsCode } = generateTypeScriptCode(result);
    
    // Replace BUSINESS_ENTITY_TYPES section
    const businessEntityRegex = /\/\/ Core business entity types[\s\S]*?}\);/;
    content = content.replace(businessEntityRegex, businessEntitiesCode);
    
    // Replace ALLOWED_DATAID_PATTERNS section
    const dataIdPatternsRegex = /\/\/ Patterns for allowed DataIDs[\s\S]*?\];/;
    content = content.replace(dataIdPatternsRegex, dataIdPatternsCode);
    
    // Add generation timestamp comment at the top
    const timestamp = new Date().toISOString();
    const header = `/**
 * Cache filtering logic for selective persistence
 * Implements hybrid allow+deny list strategy with size-based fallbacks
 * 
 * 🤖 GENERATED CODE: Business entity allowlists auto-generated on ${timestamp}
 * Run \`pnpm gen:allowlist\` to regenerate from schema.graphql
 */`;
    
    // Replace existing header
    content = content.replace(/\/\*\*[\s\S]*?\*\//, header);
    
    fs.writeFileSync(CACHE_FILTERS_PATH, content, 'utf8');
    
  } catch (error) {
    console.error(`❌ Error updating cache filters file:`, error);
    process.exit(1);
  }
}

/**
 * Validate that generated allowlists are reasonable
 */
function validateGeneration(result) {
  const { businessEntityTypes, allowedDataIdPatterns } = result;
  
  // Check minimum expected business entities
  const requiredEntities = ['TimeSheet', 'PayStub', 'Employee'];
  const missingEntities = requiredEntities.filter(entity => !businessEntityTypes.includes(entity));
  
  if (missingEntities.length > 0) {
    console.error(`❌ Validation failed: Missing required business entities: ${missingEntities.join(', ')}`);
    process.exit(1);
  }
  
  // Check reasonable count (should have at least 5 business entities)
  if (businessEntityTypes.length < 5) {
    console.error(`❌ Validation failed: Too few business entities found (${businessEntityTypes.length}). Expected at least 5.`);
    process.exit(1);
  }
  
  // Check that patterns were generated
  if (allowedDataIdPatterns.length === 0) {
    console.error(`❌ Validation failed: No DataID patterns generated.`);
    process.exit(1);
  }
  
  console.log(`✅ Validation passed:`);
  console.log(`   - Found ${businessEntityTypes.length} business entity types`);
  console.log(`   - Generated ${allowedDataIdPatterns.length} DataID patterns`);
}

/**
 * Main execution function
 */
function main() {
  console.log('🔄 Generating Relay cache allowlists from GraphQL schema...\n');
  
  // Step 1: Read schema
  console.log('📖 Reading GraphQL schema...');
  const schema = readSchema();
  
  // Step 2: Extract business entities
  console.log('🔍 Extracting business entity types...');
  const businessEntityTypes = extractBusinessEntities(schema);
  
  // Step 3: Generate DataID patterns
  console.log('⚙️  Generating DataID patterns...');
  const allowedDataIdPatterns = generateDataIdPatterns(businessEntityTypes);
  
  const result = {
    businessEntityTypes,
    allowedDataIdPatterns
  };
  
  // Step 4: Validate generation
  console.log('✅ Validating generated allowlists...');
  validateGeneration(result);
  
  // Step 5: Update cache filters
  console.log('📝 Updating cacheFilters.ts...');
  updateCacheFilters(result);
  
  console.log('\n✨ Allowlists generated successfully!');
  console.log('\nGenerated business entity types:');
  businessEntityTypes.forEach(type => console.log(`   - ${type}`));
  
  console.log('\n🎯 Cache allowlist generation completed. The following files were updated:');
  console.log(`   - ${CACHE_FILTERS_PATH}`);
  console.log('\n💡 Run your tests to ensure the new allowlists maintain <10% unknown-record rate.');
}

// Execute if run directly
if (require.main === module) {
  main();
}

// Export for testing
module.exports = { main, extractBusinessEntities, generateDataIdPatterns };