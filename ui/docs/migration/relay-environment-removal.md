# Migration Guide: RelayEnvironment.ts Deprecation

**Status**: Required for v3 Implementation  
**Target Completion**: 2025-08-04  
**Impact**: Breaking change for external consumers  

## Overview

The legacy `RelayEnvironment.ts` file has been deprecated in favor of the new selective cache persistence system. This migration guide provides clear instructions for updating your code to use the new approach.

## What Changed

### Before (Deprecated)
```typescript
// ❌ DEPRECATED - Will be removed in future version
import RelayEnvironment from '@/relay/RelayEnvironment';

// Synchronous access (unreliable)
const response = fetchQuery(RelayEnvironment, query, variables);
```

### After (Recommended)
```typescript
// ✅ NEW - Use getRelayEnvironment() for persistent cache
import { getRelayEnvironment } from '@/relay/withPersistence';

// Async access (recommended)
const environment = await getRelayEnvironment();
const response = fetchQuery(environment, query, variables);
```

## Migration Steps

### Step 1: Update Imports

Replace all imports of the deprecated RelayEnvironment:

```diff
- import RelayEnvironment from '@/relay/RelayEnvironment';
+ import { getRelayEnvironment } from '@/relay/withPersistence';
```

### Step 2: Convert to Async Pattern

Update your component or hook to use the async pattern:

```typescript
// Before
function useMyQuery() {
  return useLazyLoadQuery(MyQuery, {}, { fetchPolicy: 'store-and-network' });
}

// After - Using useEffect pattern
function useMyQuery() {
  const [environment, setEnvironment] = useState<Environment | null>(null);
  
  useEffect(() => {
    getRelayEnvironment().then(setEnvironment);
  }, []);

  return useLazyLoadQuery(
    MyQuery, 
    {}, 
    { 
      fetchPolicy: 'store-and-network',
      // Use environment when available
      ...(environment && { environment })
    }
  );
}
```

### Step 3: Update Manual fetchQuery Calls

For direct fetchQuery usage:

```typescript
// Before
async function loadData() {
  const response = await fetchQuery(RelayEnvironment, MyQuery, variables);
}

// After
async function loadData() {
  const environment = await getRelayEnvironment();
  const response = await fetchQuery(environment, MyQuery, variables);
}
```

### Step 4: Update Relay Context Usage

If you're providing Relay environment via context:

```typescript
// Before
import RelayEnvironment from '@/relay/RelayEnvironment';

function App() {
  return (
    <RelayEnvironmentProvider environment={RelayEnvironment}>
      {/* Your app */}
    </RelayEnvironmentProvider>
  );
}

// After
import { getRelayEnvironment } from '@/relay/withPersistence';

function App() {
  const [environment, setEnvironment] = useState<Environment | null>(null);
  
  useEffect(() => {
    getRelayEnvironment().then(setEnvironment);
  }, []);

  if (!environment) {
    return <LoadingSpinner />; // Show loading while environment initializes
  }

  return (
    <RelayEnvironmentProvider environment={environment}>
      {/* Your app */}
    </RelayEnvironmentProvider>
  );
}
```

## Common Patterns

### Pattern 1: Custom Hook with Environment

Create a reusable hook for environment access:

```typescript
function useRelayEnvironment() {
  const [environment, setEnvironment] = useState<Environment | null>(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    getRelayEnvironment()
      .then(setEnvironment)
      .finally(() => setLoading(false));
  }, []);

  return { environment, loading };
}

// Usage
function MyComponent() {
  const { environment, loading } = useRelayEnvironment();
  
  if (loading) return <LoadingSpinner />;
  
  // Use environment...
}
```

### Pattern 2: Suspense Integration

For React Suspense usage:

```typescript
const environmentResource = {
  promise: getRelayEnvironment(),
  read() {
    throw this.promise; // Suspense will catch this
  }
};

function MyComponent() {
  const environment = environmentResource.read();
  // Use environment...
}

// Wrap with Suspense
<Suspense fallback={<LoadingSpinner />}>
  <MyComponent />
</Suspense>
```

## Benefits of Migration

### 1. Selective Cache Persistence
- **Smart Filtering**: Only business-critical data persists across sessions
- **Auth Error Prevention**: Authentication errors never cached, preventing dialog suppression
- **Performance**: Reduced cache size and improved load times

### 2. Enhanced Debugging
- **Dev Tools**: Access to `window.__RELAY_DEV__.printCacheSummary()`
- **Metrics**: Detailed cache hit rates and performance tracking
- **Transparency**: Clear reasoning for persistence decisions

### 3. Production Optimizations
- **Size Limits**: Configurable thresholds prevent runaway cache growth
- **TTL Management**: Automatic cleanup of stale data
- **Graceful Degradation**: Handles IndexedDB quota exceeded scenarios

## Troubleshooting

### Issue: "RelayEnvironment not initialized" Error

**Cause**: Attempting to access environment before initialization  
**Solution**: Ensure you await `getRelayEnvironment()` before use

```typescript
// ❌ Wrong - synchronous access
const environment = RelayEnvironment; // May throw error

// ✅ Correct - async access
const environment = await getRelayEnvironment();
```

### Issue: Component Renders Before Environment Ready

**Cause**: Race condition between component mount and environment initialization  
**Solution**: Use loading state or Suspense

```typescript
function MyComponent() {
  const [environment, setEnvironment] = useState<Environment | null>(null);
  
  useEffect(() => {
    getRelayEnvironment().then(setEnvironment);
  }, []);

  // Show loading until environment is ready
  if (!environment) {
    return <div>Loading Relay environment...</div>;
  }

  return <ActualComponent environment={environment} />;
}
```

### Issue: Cache Not Persisting

**Cause**: Records may be filtered by selective persistence  
**Solution**: Check cache filters using dev tools

```typescript
// In browser console
await window.__RELAY_DEV__.printCacheSummary();
```

## Validation Checklist

After migration, verify:

- [ ] No imports from `@/relay/RelayEnvironment`
- [ ] All `getRelayEnvironment()` calls are awaited
- [ ] Loading states handle environment initialization
- [ ] Cache persistence works as expected
- [ ] No synchronous environment access
- [ ] Dev tools accessible in development

## Timeline

- **Phase 1**: Update all internal components (Week 1)
- **Phase 2**: Update shared utilities and hooks (Week 2)  
- **Phase 3**: Remove deprecated RelayEnvironment.ts (Week 3)
- **Phase 4**: Validate production deployment (Week 4)

## Support

For migration assistance:

1. **Dev Tools**: Use `window.__RELAY_DEV__.printCacheSummary()` for cache analysis
2. **Documentation**: Review `/ui/docs/RELAY-RULES.md` for best practices
3. **Architecture**: See `/ui/docs/architecture/ADR-002-Selective-Cache-Persistence.md`

---

**Important**: The deprecated `RelayEnvironment.ts` will continue to work during the transition period but will emit console warnings in development. Plan your migration accordingly to avoid disruption.