import { Constants } from '@/src/constants/global';
import { ToastQueue } from '@react-spectrum/toast';
import { FetchFunction } from 'relay-runtime';
import { AuthErrorHandler } from '@/src/services/auth-error-handler';
import { sessionService } from '@/src/services/session-management';
import { sanitizeMutationVariables } from '@/src/services/pii-sanitizer';
import { debug } from './debug';

const HTTP_ENDPOINT = import.meta.env.VITE_GRAPHQL_API_URL;

/**
 * Relay requires developers to configure a "fetch" function that tells <PERSON><PERSON> how to load
 * the results of GraphQL queries from your server (or other data source). See more at
 * https://relay.dev/docs/en/quick-start-guide#relay-environment.
 */
export const fetchRelay: FetchFunction = async (params, variables) => {
    debug.debug('[Relay fetch] Network request', {
        operation: params.name,
        variables
    });
    if (!HTTP_ENDPOINT) {
        throw new Error('Environment variable VITE_GRAPHQL_API_URL is missing');
    }

    try {
        // Register GraphQL activity for session management
        sessionService.registerActivity(HTTP_ENDPOINT, 'POST');
        
        // Fetch data from EPRLive's GraphQL API:
        // Authorization header is automatically added by CookieToHeaderMiddleware
        const response = await fetch(HTTP_ENDPOINT, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include', // Include HttpOnly cookies
            body: JSON.stringify({
                query: params.text,
                variables
            })
        });

        // Get the response as JSON
        if (response.status === 404) {
            throw new Error('Could not find graphql endpoint, please check your environment variables and try restarting your backend');
        }
        const json = await response.json();

        // GraphQL returns exceptions (for example, a missing required variable) in the "errors"
        // property of the response. If any exceptions occurred when processing the request,
        // throw an error to indicate to the developer what went wrong.
        if (Array.isArray(json.errors)) {
            const isAuthenticatedError = json.errors.some(
                (error: any) => error.extensions && error.extensions.code === Constants.UNAUTHENTICATED_RESPONSE_CODE
            );
            const isAuthorizedError = json.errors.some(
                (error: any) => error.extensions && error.extensions.code === Constants.UNAUTHORIZED_RESPONSE_CODE
            );

            if (!isAuthenticatedError && !isAuthorizedError) {
                debug.error('[fetchRelay.ts] Relay Error Catch for Development (Unhandled by auth dialog):', json.errors);
            }

            if (isAuthenticatedError || isAuthorizedError) {
                const errorCode = isAuthenticatedError ? Constants.UNAUTHENTICATED_RESPONSE_CODE : Constants.UNAUTHORIZED_RESPONSE_CODE;
                const errorMessage = json.errors[0]?.message || 'An authentication or authorization error occurred.';

                // Use centralized auth error handler
                AuthErrorHandler.handleAuthError(errorCode, errorMessage);

                // Return appropriate empty data based on the query type to prevent Relay errors
                if (params.name === 'RelayEnvironmentLayoutQuery') {
                    return {
                        data: {
                            fieldDefinitions: [],
                            userInfo: null
                        }
                    };
                }

                // For other queries, return empty data structure
                return {
                    data: {}
                };
            } else {
                // Phase 5: Sanitize variables before including in error message
                const sanitizedVariables = sanitizeMutationVariables(variables);
                
                // For other GraphQL errors, throw a generic error for ErrorBoundary to catch
                throw new Error(
                    `Error fetching GraphQL query '${params.name}' with variables '${JSON.stringify(sanitizedVariables)}': ${JSON.stringify(json.errors)}`,
                    {
                        cause: json.errors
                    }
                );
            }
        }

        // Otherwise, return the full payload.
        return json;
    } catch (error) {
        // Handle network errors (connection refused, timeout, etc.)
        if (error instanceof TypeError && error.message === 'Failed to fetch') {
            debug.warn('[fetchRelay.ts] Backend connection failed. Backend may be down.');

            // Show user-friendly toast notification
            ToastQueue.negative('Backend service is currently unavailable. Please try again later.', {
                timeout: 5000
            });

            // Return appropriate empty data based on the query type to prevent Relay errors
            if (params.name === 'RelayEnvironmentLayoutQuery') {
                return {
                    data: {
                        fieldDefinitions: [],
                        userInfo: null
                    }
                };
            }

            // For other queries, return empty data structure
            return {
                data: {}
            };
        }

        // For other errors (like our custom GraphQL errors), re-throw them
        throw error;
    }
};