/**
 * Debug utility that tree-shakes in production
 * All debug output should go through this module to ensure clean production builds
 * 
 * PHASE 4: Enhanced tree-shaking with production environment checks
 * - All debug functions are gated behind NODE_ENV checks
 * - Expensive operations like JSON.stringify are optimized out in production
 * - Console methods are completely removed in production builds
 */

// PHASE 4: Tree-shaking friendly import - only load in development
// @ts-ignore - Dynamic import for production tree-shaking
const stringify = process.env.NODE_ENV === 'development' 
  ? require('json-stable-stringify')
  : null;

// Type definition for json-stable-stringify (it may return undefined in edge cases)
type StringifyFunction = (obj: unknown) => string | undefined;

/**
 * Stable JSON stringification utility for cache key generation
 * 
 * This utility wraps json-stable-stringify to provide deterministic serialization
 * of objects for cache key generation. In development, it uses the full library
 * for debugging and stability, while in production it could fall back to built-in
 * JSON.stringify for reduced bundle size if needed.
 * 
 * @param obj - Object to stringify
 * @returns Deterministic JSON string representation
 */
export function stableStringify(obj: unknown): string {
    // PHASE 4: Tree-shake development-only logic
    if (process.env.NODE_ENV === 'development') {
        try {
            // Use stable stringify for consistent cache keys in development
            return stringify?.(obj) || JSON.stringify(obj);
        } catch (error) {
            console.warn('[stableStringify] Failed to use stable stringify, falling back to JSON.stringify:', error);
            
            try {
                return JSON.stringify(obj);
            } catch (fallbackError) {
                console.error('[stableStringify] Both stable and regular stringify failed:', fallbackError);
                return '[stringify-error]';
            }
        }
    } else {
        // PHASE 4: Production uses minimal JSON.stringify for performance
        try {
            return JSON.stringify(obj);
        } catch {
            return '[stringify-error]';
        }
    }
}

/**
 * Type-safe version for record objects (most common use case)
 */
export function stableStringifyRecord(obj: Record<string, unknown>): string {
    return stableStringify(obj);
}

/**
 * Clean and normalize variables for consistent cache key generation
 * 
 * This function addresses the variable sanitization requirements from the SWR fix plan:
 * 1. Deep-clones variables to avoid mutations
 * 2. Prunes keys whose value is undefined
 * 3. Converts Date objects to ISO strings for deterministic serialization
 * 4. Ensures arrays are sorted when order doesn't matter (configurable)
 * 
 * @param variables - Raw GraphQL variables object
 * @param options - Configuration options for cleaning behavior
 * @returns Cleaned and normalized variables object
 */
export function cleanVariables(
    variables: Record<string, unknown>,
    options: {
        sortArrays?: boolean;
        convertDates?: boolean;
        pruneUndefined?: boolean;
    } = {}
): Record<string, unknown> {
    const {
        sortArrays = true,
        convertDates = true,
        pruneUndefined = true
    } = options;

    function deepCleanValue(value: unknown): unknown {
        if (value === undefined) {
            return undefined; // Will be pruned later if pruneUndefined is true
        }

        if (value === null) {
            return null;
        }

        // Convert Date objects to ISO strings for deterministic serialization
        if (convertDates && value instanceof Date) {
            return value.toISOString();
        }

        // Handle functions (should never happen in GraphQL variables, but be defensive)
        if (typeof value === 'function') {
            return '[function]';
        }

        // Handle arrays
        if (Array.isArray(value)) {
            const cleanedArray = value.map(deepCleanValue);
            
            // Sort arrays if requested and they contain primitive values for deterministic ordering
            if (sortArrays && cleanedArray.every(item => 
                typeof item === 'string' || 
                typeof item === 'number' || 
                typeof item === 'boolean' || 
                item === null
            )) {
                return cleanedArray.sort();
            }
            
            return cleanedArray;
        }

        // Handle objects
        if (typeof value === 'object' && value !== null) {
            const cleanedObj: Record<string, unknown> = {};
            
            for (const [key, val] of Object.entries(value)) {
                const cleanedVal = deepCleanValue(val);
                
                // Prune undefined values if requested
                if (pruneUndefined && cleanedVal === undefined) {
                    continue;
                }
                
                cleanedObj[key] = cleanedVal;
            }
            
            return cleanedObj;
        }

        // Return primitive values as-is
        return value;
    }

    const cleaned = deepCleanValue(variables) as Record<string, unknown>;
    
    if (process.env.NODE_ENV === 'development') {
        const originalSize = JSON.stringify(variables).length;
        const cleanedSize = JSON.stringify(cleaned).length;
        
        if (originalSize !== cleanedSize) {
            console.log('[cleanVariables] Normalized variables', {
                originalSize,
                cleanedSize,
                reduction: originalSize - cleanedSize,
                pruneUndefined,
                convertDates,
                sortArrays
            });
        }
    }
    
    return cleaned;
}

/**
 * Prepare variables for use with useSWRQuery to ensure cache key stability
 * This is the main function that should be called before passing variables to useSWRQuery
 */
export function prepareVariables(variables: Record<string, unknown>): Record<string, unknown> {
    return cleanVariables(variables, {
        sortArrays: true,
        convertDates: true,
        pruneUndefined: true
    });
}

// Cache miss reason types for detailed debugging
// PHASE 4: Enhanced debug labels for better clarity
export type CacheMissReason = 
  | 'missing_query_records'
  | 'stale_data'
  | 'forced_network_due_to_missing_timestamp' // PHASE 4: Renamed from 'network_first_policy' for clarity
  | 'incomplete_data'
  | 'auth_error_cleared'
  | 'cache_key_mismatch'
  | 'store_hydration_failed'
  | 'variable_normalization_mismatch'
  | 'ttl_expired' // PHASE 4: New reason for TTL-based revalidation
  | 'edge_preservation_triggered' // PHASE 4: New reason for edge snapshot scenarios
  | 'unknown';

export interface CacheMissDetails {
  queryName: string;
  reason: CacheMissReason;
  missingRecords?: string[];
  availableRecords?: string[];
  variables?: Record<string, unknown>;
  storeSize?: number;
  timestamp: string;
}

export interface QueryAnalysis {
  queryName: string;
  expectedRecords: string[];
  availableRecords: string[];
  missingRecords: string[];
  cacheHitProbability: number;
  reason: CacheMissReason;
}

// PHASE 4: Debug functions with aggressive tree-shaking in production
export const debug = {
  log: process.env.NODE_ENV === 'development' 
    ? (...args: unknown[]) => console.log(...args)
    : () => { /* no-op in production */ },

  warn: process.env.NODE_ENV === 'development'
    ? (...args: unknown[]) => console.warn(...args) 
    : () => { /* no-op in production */ },

  error: process.env.NODE_ENV === 'development'
    ? (...args: unknown[]) => console.error(...args)
    : () => { /* no-op in production */ },

  table: process.env.NODE_ENV === 'development'
    ? (data: unknown) => console.table(data)
    : () => { /* no-op in production */ },

  debug: process.env.NODE_ENV === 'development'
    ? (...args: unknown[]) => console.debug(...args)
    : () => { /* no-op in production */ },

  // PHASE 4: Enhanced cache debugging methods with tree-shaking
  cacheMiss: process.env.NODE_ENV === 'development'
    ? (details: CacheMissDetails) => {
        console.group(`🔍 Cache Miss Analysis: ${details.queryName}`);
        console.log(`📋 Reason: ${details.reason}`);
        if (details.missingRecords?.length) {
          console.log(`❌ Missing Records (${details.missingRecords.length}):`, details.missingRecords);
        }
        if (details.availableRecords?.length) {
          console.log(`✅ Available Records (${details.availableRecords.length}):`, details.availableRecords);
        }
        if (details.variables) {
          console.log(`🔧 Variables:`, details.variables);
        }
        if (details.storeSize) {
          console.log(`📦 Store Size: ${details.storeSize} records`);
        }
        console.log(`⏰ Timestamp: ${details.timestamp}`);
        console.groupEnd();
      }
    : () => { /* no-op in production */ },

  cacheHit: process.env.NODE_ENV === 'development'
    ? (queryName: string, loadTime: number, recordCount?: number) => {
        console.log(`⚡ Cache Hit: ${queryName} loaded in ${loadTime}ms${recordCount ? ` (${recordCount} records)` : ''}`);
      }
    : () => { /* no-op in production */ },

  queryAnalysis: (analysis: QueryAnalysis) => {
    if (process.env.NODE_ENV === 'development') {
      console.group(`📊 Query Analysis: ${analysis.queryName}`);
      console.log(`🎯 Cache Hit Probability: ${(analysis.cacheHitProbability * 100).toFixed(1)}%`);
      console.log(`📝 Expected Records: ${analysis.expectedRecords.length}`);
      console.log(`✅ Available Records: ${analysis.availableRecords.length}`);
      console.log(`❌ Missing Records: ${analysis.missingRecords.length}`);
      if (analysis.missingRecords.length > 0) {
        console.log(`🔍 Missing:`, analysis.missingRecords);
      }
      console.log(`📋 Predicted Reason: ${analysis.reason}`);
      console.groupEnd();
    }
  },

  storeAnalysis: (storeSnapshot: Record<string, unknown>) => {
    if (process.env.NODE_ENV === 'development') {
      const recordTypes = new Map<string, number>();
      const totalRecords = Object.keys(storeSnapshot).length;
      
      for (const [dataID, record] of Object.entries(storeSnapshot)) {
        const typedRecord = record as { __typename?: string };
        const typename = typedRecord?.__typename || 'Unknown';
        recordTypes.set(typename, (recordTypes.get(typename) || 0) + 1);
      }
      
      console.group(`🏪 Store Analysis (${totalRecords} total records)`);
      console.table(Object.fromEntries(recordTypes));
      console.groupEnd();
    }
  },

  networkDelay: (queryName: string, delayMs: number) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`⏳ Network Delay Applied: ${queryName} delayed by ${delayMs}ms for cache testing`);
    }
  },

  persistence: process.env.NODE_ENV === 'development'
    ? (message: string, data?: unknown) => {
        if (data) {
          console.log(`💾 [Persistence] ${message}`, data);
        } else {
          console.log(`💾 [Persistence] ${message}`);
        }
      }
    : () => { /* no-op in production */ },

  // Enhanced cache key debugging
  cacheKeyMismatch: (details: {
    queryName: string;
    persistedKeys: string[];
    lookupKeys: string[];
    variables: Record<string, unknown>;
    timestamp: string;
  }) => {
    if (process.env.NODE_ENV === 'development') {
      console.group(`🔑 Cache Key Mismatch Analysis: ${details.queryName}`);
      console.log(`📋 Persisted Keys (${details.persistedKeys.length}):`, details.persistedKeys);
      console.log(`🔍 Lookup Keys (${details.lookupKeys.length}):`, details.lookupKeys);
      console.log(`🔧 Variables:`, details.variables);
      console.log(`⏰ Timestamp: ${details.timestamp}`);
      
      // Find differences
      const persistedSet = new Set(details.persistedKeys);
      const lookupSet = new Set(details.lookupKeys);
      const onlyInPersisted = details.persistedKeys.filter(key => !lookupSet.has(key));
      const onlyInLookup = details.lookupKeys.filter(key => !persistedSet.has(key));
      
      if (onlyInPersisted.length > 0) {
        console.log(`❌ Keys only in persisted cache (${onlyInPersisted.length}):`, onlyInPersisted);
      }
      if (onlyInLookup.length > 0) {
        console.log(`❌ Keys only in lookup (${onlyInLookup.length}):`, onlyInLookup);
      }
      
      const intersection = details.persistedKeys.filter(key => lookupSet.has(key));
      if (intersection.length > 0) {
        console.log(`✅ Matching Keys (${intersection.length}):`, intersection);
      }
      
      console.groupEnd();
    }
  },

  storeHydration: (details: {
    step: 'start' | 'indexeddb_read' | 'store_restore' | 'complete' | 'error' | 'cache_age_set' | 'per_op_timestamps_exposed';
    message: string;
    data?: unknown;
    recordCount?: number;
    duration?: number;
    connectionRecords?: number;
    savedAt?: string;
    hydratedAt?: string;
    sampleKeys?: string[];
    paginationSlices?: {
      businessConnections: number;
      totalEdges: number;
      preservedPageInfo: number;
    };
  }) => {
    if (process.env.NODE_ENV === 'development') {
      const stepEmoji = {
        start: '🚀',
        indexeddb_read: '📖',
        store_restore: '🔄',
        complete: '✅',
        error: '❌',
        cache_age_set: '⏰',
        per_op_timestamps_exposed: '🔑'
      };
      
      let message = `${stepEmoji[details.step]} [Store Hydration] ${details.message}`;
      
      if (details.recordCount) {
        message += ` (${details.recordCount} records)`;
      }
      
      if (details.connectionRecords) {
        message += ` [${details.connectionRecords} connections]`;
      }
      
      if (details.duration) {
        message += ` in ${details.duration}ms`;
      }
      
      console.log(message, details.data || '');
      
      // Enhanced pagination slice reporting
      if (details.paginationSlices) {
        console.log(`📄 Pagination Analysis:`, {
          businessConnections: details.paginationSlices.businessConnections,
          totalEdges: details.paginationSlices.totalEdges,
          preservedPageInfo: details.paginationSlices.preservedPageInfo,
          averageEdgesPerConnection: details.paginationSlices.businessConnections > 0 
            ? Math.round(details.paginationSlices.totalEdges / details.paginationSlices.businessConnections)
            : 0
        });
      }
    }
  },

  // Performance validation utilities
  performanceValidation: {
    validateCachePerformance: (stats: {
      totalRecords: number;
      persistedRecords: number;
      totalSize: number;
      persistedSize: number;
      connectionRecords: number;
      businessConnections: number;
      paginationEdges: number;
    }) => {
      if (process.env.NODE_ENV === 'development') {
        const persistenceRate = stats.persistedRecords / stats.totalRecords;
        const sizeReduction = (stats.totalSize - stats.persistedSize) / stats.totalSize;
        
        console.group('📊 Cache Performance Validation');
        console.log(`✅ Persistence Rate: ${(persistenceRate * 100).toFixed(1)}% (${stats.persistedRecords}/${stats.totalRecords})`);
        console.log(`📦 Size Reduction: ${(sizeReduction * 100).toFixed(1)}% (${Math.round(stats.persistedSize / 1024)}KB saved)`);
        console.log(`🔗 Connection Preservation: ${stats.connectionRecords} connections, ${stats.businessConnections} business`);
        console.log(`📄 Pagination Edges: ${stats.paginationEdges} edges preserved`);
        
        // Validation checks
        const validationResults = [];
        
        if (persistenceRate >= 0.9) {
          validationResults.push('✅ Persistence rate meets >90% requirement');
        } else {
          validationResults.push(`❌ Persistence rate ${(persistenceRate * 100).toFixed(1)}% below 90% target`);
        }
        
        if (stats.businessConnections > 0 && stats.paginationEdges > 0) {
          validationResults.push('✅ Pagination slices are being preserved');
        } else if (stats.businessConnections > 0) {
          validationResults.push('⚠️ Business connections found but no pagination edges preserved');
        }
        
        if (sizeReduction > 0) {
          validationResults.push(`✅ Cache filtering providing ${(sizeReduction * 100).toFixed(1)}% size reduction`);
        }
        
        validationResults.forEach(result => console.log(result));
        console.groupEnd();
        
        return {
          passed: persistenceRate >= 0.9 && (stats.businessConnections === 0 || stats.paginationEdges > 0),
          persistenceRate,
          sizeReduction,
          validationResults
        };
      }
      
      return { passed: true, persistenceRate: 1, sizeReduction: 0, validationResults: [] };
    },

    validateSWRBehavior: (swrStats: {
      totalQueries: number;
      cacheHits: number;
      backgroundRevalidations: number;
      averageLoadTime: number;
      staleServed: number;
    }) => {
      if (process.env.NODE_ENV === 'development') {
        const cacheHitRate = swrStats.cacheHits / swrStats.totalQueries;
        const revalidationRate = swrStats.backgroundRevalidations / swrStats.totalQueries;
        
        console.group('🔄 SWR Behavior Validation');
        console.log(`⚡ Cache Hit Rate: ${(cacheHitRate * 100).toFixed(1)}% (${swrStats.cacheHits}/${swrStats.totalQueries})`);
        console.log(`🔄 Background Revalidation Rate: ${(revalidationRate * 100).toFixed(1)}%`);
        console.log(`📊 Average Load Time: ${swrStats.averageLoadTime.toFixed(1)}ms`);
        console.log(`📤 Stale Content Served: ${swrStats.staleServed} times`);
        
        const validationResults = [];
        
        if (cacheHitRate >= 0.7) {
          validationResults.push(`✅ Cache hit rate ${(cacheHitRate * 100).toFixed(1)}% meets >70% target`);
        } else {
          validationResults.push(`❌ Cache hit rate ${(cacheHitRate * 100).toFixed(1)}% below 70% target`);
        }
        
        if (swrStats.averageLoadTime < 100) {
          validationResults.push(`✅ Average load time ${swrStats.averageLoadTime.toFixed(1)}ms is optimal`);
        } else if (swrStats.averageLoadTime < 500) {
          validationResults.push(`⚠️ Average load time ${swrStats.averageLoadTime.toFixed(1)}ms is acceptable`);
        } else {
          validationResults.push(`❌ Average load time ${swrStats.averageLoadTime.toFixed(1)}ms is too slow`);
        }
        
        if (swrStats.backgroundRevalidations > 0) {
          validationResults.push('✅ Background revalidation is working');
        } else {
          validationResults.push('⚠️ No background revalidations detected');
        }
        
        validationResults.forEach(result => console.log(result));
        console.groupEnd();
        
        return {
          passed: cacheHitRate >= 0.7 && swrStats.averageLoadTime < 500,
          cacheHitRate,
          averageLoadTime: swrStats.averageLoadTime,
          validationResults
        };
      }
      
      return { passed: true, cacheHitRate: 1, averageLoadTime: 0, validationResults: [] };
    }
  },

  // PHASE 4: Enhanced SWR debugging utilities
  swrBehavior: {
    logTTLCheck: (details: {
      cacheKey: string;
      queryName: string;
      savedAt?: number;
      currentTime: number;
      ttlMs: number;
      isCacheFresh: boolean;
      willTriggerRevalidation: boolean;
    }) => {
      if (process.env.NODE_ENV === 'development') {
        const ageMs = details.savedAt ? details.currentTime - details.savedAt : undefined;
        console.group(`⏰ [SWR] TTL Check: ${details.queryName}`);
        console.log(`🔑 Cache Key: ${details.cacheKey}`);
        console.log(`📅 Saved At: ${details.savedAt ? new Date(details.savedAt).toISOString() : 'not set'}`);
        console.log(`🕐 Current Time: ${new Date(details.currentTime).toISOString()}`);
        console.log(`⏱️ Age: ${ageMs ? `${Math.round(ageMs / 1000)}s` : 'unknown'}`);
        console.log(`🎯 TTL: ${Math.round(details.ttlMs / 1000)}s`);
        console.log(`✅ Is Fresh: ${details.isCacheFresh}`);
        console.log(`🔄 Will Revalidate: ${details.willTriggerRevalidation}`);
        console.groupEnd();
      }
    },

    logEdgeSnapshot: (details: {
      cacheKey: string;
      fieldName: string;
      edgeCount: number;
      success: boolean;
      error?: unknown;
    }) => {
      if (process.env.NODE_ENV === 'development') {
        const emoji = details.success ? '📸' : '❌';
        console.log(`${emoji} [SWR] Edge snapshot ${details.fieldName}:`, {
          cacheKey: details.cacheKey,
          edgeCount: details.edgeCount,
          success: details.success,
          error: details.error
        });
      }
    },

    logConnectionDiscovery: (details: {
      explicitFields: string[];
      discoveredFields: string[];
      totalFields: string[];
    }) => {
      if (process.env.NODE_ENV === 'development') {
        console.group(`🔍 [SWR] Connection Discovery`);
        console.log(`📋 Explicit Fields:`, details.explicitFields);
        console.log(`🆕 Discovered Fields:`, details.discoveredFields);
        console.log(`📊 Total Fields:`, details.totalFields);
        console.groupEnd();
      }
    },

    logHashComparison: (details: {
      cacheKey: string;
      fieldName: string;
      prevHash?: string;
      incomingHash: string;
      dataChanged: boolean;
      action: 'merge' | 'replace';
      existingEdgeCount: number;
      newEdgeCount: number;
    }) => {
      if (process.env.NODE_ENV === 'development') {
        const emoji = details.dataChanged ? '🔄' : '♻️';
        console.log(`${emoji} [SWR] Hash comparison ${details.fieldName}:`, {
          cacheKey: details.cacheKey,
          prevHash: details.prevHash || 'none',
          incomingHash: details.incomingHash,
          dataChanged: details.dataChanged,
          action: details.action,
          edges: `${details.existingEdgeCount} → ${details.newEdgeCount}`
        });
      }
    },

    logPersistenceEvent: (details: {
      event: 'immediate_flush' | 'debounced_save' | 'beforeunload' | 'sendbeacon_fallback';
      cacheKey?: string;
      success: boolean;
      error?: unknown;
      duration?: number;
    }) => {
      if (process.env.NODE_ENV === 'development') {
        const emoji = details.success ? '💾' : '❌';
        console.log(`${emoji} [SWR] Persistence ${details.event}:`, {
          cacheKey: details.cacheKey,
          success: details.success,
          duration: details.duration,
          error: details.error
        });
      }
    }
  }
};