/**
 * Types for Relay cache persistence system
 */

export type PersistenceReason =
  | 'denied_error_indicators'
  | 'denied_security_denylist'
  | 'denied_size_exceeded'
  | 'allowed_default'
  | 'allowed_client_root_sanitized';

export interface CacheFilterStats {
  totalRecords: number;
  persistedRecords: number;
  sizeBytes: number;
}

export interface PersistenceDecision {
  shouldPersist: boolean;
  reason: PersistenceReason;
}

/**
 * Extended PersistedBlob with per-operation timestamp tracking
 * for SWR cache age management
 */
export interface PersistedBlobV2 {
  version: string;
  schemaHash?: string;
  savedAt: number;          // legacy whole-store timestamp (kept for compatibility)
  opSavedAt?: Record<string, number>; // per-operation timestamps
  records: Record<string, unknown>;
}

/**
 * Hash table for tracking payload hashes to avoid no-op publishes
 * Maps cache key to SHA-1 hash of last payload
 */
export type PayloadHashTable = Record<string, string>;