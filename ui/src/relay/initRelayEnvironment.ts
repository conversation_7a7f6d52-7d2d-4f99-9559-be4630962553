import { Environment, Network } from 'relay-runtime';
import { fetchRelay } from './fetchRelay';
import { createPersistedStore } from './createPersistedStore';

// Cache environment once initialized
let environmentPromise: Promise<Environment> | null = null;

/**
 * Async factory for initializing Relay Environment with selective cache persistence
 * Returns a Promise that resolves to the fully configured Environment
 */
export async function initRelayEnvironment(): Promise<Environment> {
    if (!environmentPromise) {
        environmentPromise = createEnvironment();
    }
    return environmentPromise;
}

/**
 * Internal function to create the environment
 * Loads persisted cache and configures environment with optimizations
 */
async function createEnvironment(): Promise<Environment> {
    if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.log('[initRelayEnvironment] Initializing environment with selective persistence');
    }

    // Initialize store with selective persistence
    const store = await createPersistedStore();

    // Create environment with network layer and persisted store
    const environment = new Environment({
        network: Network.create(fetchRelay),
        store,
        // Use partial render policy for better UX with selective cache
        UNSTABLE_defaultRenderPolicy: 'partial'
    });

    if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.log('[initRelayEnvironment] Environment initialized successfully');
    }

    return environment;
}

/**
 * Legacy compatibility function - maintains existing API
 * @deprecated Use initRelayEnvironment() for new code
 */
export const getRelayEnvironment = initRelayEnvironment;
