# Relay Cache Debugging Guide

This guide shows you how to use the enhanced dev utilities to track and debug cache usage in your application, specifically addressing cache miss issues and performance problems.

## Overview

The eprlive24 frontend uses a sophisticated Relay cache system with selective persistence. When queries show "fromCache: false" unexpectedly, it indicates cache misses that can significantly impact performance. This enhanced debugging system provides comprehensive tools to identify and resolve these issues.

## Auto-Enabled Features

The dev utils are automatically enabled in development mode and provide detailed console logging for all GraphQL queries made through `useSWRQuery`.

### Enhanced Console Logs

You'll now see comprehensive logs including cache miss analysis:
```
🚀 Starting GraphQL query: GetTimeSheets { variables: {...}, debugDelay: "disabled" }
📊 Query Analysis: GetTimeSheets
🎯 Cache Hit Probability: 75.3%
📝 Expected Records: 8
✅ Available Records: 6
❌ Missing Records: 2
🔍 Missing: TimeSheet:new-123, TimeSheet:new-456
📋 Predicted Reason: incomplete_data

🏪 Store Analysis (245 total records)
┌─────────────┬───────┐
│    Type     │ Count │
├─────────────┼───────┤
│ TimeSheet   │   45  │
│ PayStub     │   32  │
│ User        │    5  │
│ client:*    │  163  │
└─────────────┴───────┘

🔍 Cache Miss Analysis: GetTimeSheets
📋 Reason: incomplete_data
❌ Missing Records (2): TimeSheet:new-123, TimeSheet:new-456
✅ Available Records (150): User:789, TimeSheet:101...
🔧 Variables: { employeeId: '123', limit: 20 }
📦 Store Size: 245 records
⏰ Timestamp: 2025-08-04T...

✅ GraphQL query completed: GetTimeSheets { loadTime: "245ms", fromCache: false, cacheStatus: "MISS" }
```

## Manual Debugging Commands

Open your browser's developer console and use these enhanced commands:

### 1. Cache vs Network Testing
```javascript
// Enable 10-second delay to differentiate cache vs network
window.__enableCacheDelay()
// 🐌 Cache debug delay enabled (10s network delay). Reload page to test cache vs network loading.

// Disable delay
window.__disableCacheDelay()
// ⚡ Cache debug delay disabled

// NOTE: With delay enabled, cache hits load instantly while network requests wait 10 seconds
```

### 2. Query-Specific Cache Miss Analysis
```javascript
// Analyze why a specific query misses cache
window.__RELAY_DEV__.analyzeQueryCacheMiss('PayStubsQuery')
// Shows available vs needed records, record types, and possible miss reasons

// Analyze general cache state
window.__RELAY_DEV__.analyzeQueryCacheMiss()
```

### 3. Real-Time Cache Monitoring
```javascript
// Start monitoring cache changes in real-time
window.__RELAY_DEV__.startCacheMonitoring()
// 📊 Cache monitoring started (2s intervals). Use stopCacheMonitoring() to stop.

// Stop monitoring
window.__stopCacheMonitoring()
// 🛑 Cache monitoring stopped
```

### 4. Cache Snapshots and Comparison
```javascript
// Create snapshots for before/after comparison
window.__RELAY_DEV__.createCacheSnapshot('before_operation')
// ... perform operations ...
window.__RELAY_DEV__.createCacheSnapshot('after_operation')

// Compare snapshots
window.__RELAY_DEV__.compareCacheSnapshots('before_operation', 'after_operation')
// Shows added/removed records, size changes, etc.
```

### 5. Network Simulation
```javascript
// Simulate slow network to test cache effectiveness
window.__RELAY_DEV__.simulateSlowNetwork(5000) // 5-second delay
// 🌍 Network simulation enabled (5000ms delay). Use restoreNetwork() to disable.

// Restore normal network
window.__restoreNetwork()
// ⚡ Network simulation disabled
```

### 6. Enhanced Cache Analysis
```javascript
// Get comprehensive cache summary
window.__RELAY_DEV__.printCacheSummary()
// Shows cache version, persistence rates, size reduction, reason breakdown

// Inspect selective cache with persistence analysis
window.__RELAY_DEV__.inspectSelectiveCache()
// Shows persistence decisions and reason breakdown

// Check cache health with specific warnings
window.__RELAY_DEV__.checkCacheHealth()
// Identifies specific issues like high unknown record rates
```

### 7. Legacy Commands (Still Available)
```javascript
// Clear cache
window.__RELAY_DEV__.clearRelayCache()

// Basic cache inspection
window.__RELAY_DEV__.inspectRelayCache()

// Overall metrics
window.__RELAY_DEV__.getCacheMetrics()

// Query-specific metrics
window.__RELAY_DEV__.getCacheMetricsByQuery('QueryName')
```

## Enhanced Debugging Scenarios

### Scenario 1: High Cache Miss Rate (Primary Issue)
**Problem**: Many queries show "fromCache: false" when data should be available

**Debug Steps**:
1. **Check overall cache health**:
   ```javascript
   window.__RELAY_DEV__.checkCacheHealth()
   ```
   Look for warnings about high unknown record rates or low business entity ratios.

2. **Analyze specific query misses**:
   ```javascript
   window.__RELAY_DEV__.analyzeQueryCacheMiss('ProblematicQuery')
   ```
   This shows exactly what records are missing vs available.

3. **Use debug delay to confirm cache vs network**:
   ```javascript
   window.__enableCacheDelay()
   // Reload page - true cache hits load instantly, network requests wait 10s
   ```

4. **Check persistence decisions**:
   ```javascript
   window.__RELAY_DEV__.inspectSelectiveCache()
   ```
   Look for high "denied_security_denylist" counts (>30% may indicate overly broad patterns).

### Scenario 2: Security Denial Rate Too High (30%+ denied_security_denylist)
**Problem**: Console shows many records being denied by security patterns

**Debug Steps**:
1. **Inspect persistence breakdown**:
   ```javascript
   window.__RELAY_DEV__.printCacheSummary()
   ```
   Check the "Persistence Reasons Breakdown" table.

2. **Review denied record samples**:
   - Check console for "Unknown record denied" debug messages
   - Look for patterns in the dataID formats

3. **Test specific record types**:
   ```javascript
   window.__RELAY_DEV__.testPersistenceLogic({
     'SuspiciousRecord:123': { __typename: 'SomeType', id: '123' }
   })
   ```

**Solutions**:
- Update allowlist patterns in `cacheFilters.ts`
- Add missing patterns to `ALLOWED_DATAID_PATTERNS`
- Add missing types to `BUSINESS_ENTITY_TYPES`

### Scenario 3: Query Names Show as "UnknownQuery"
**Problem**: Debug logs show "UnknownQuery" instead of actual query names

**Debug Steps**:
1. **Check query metrics by name**:
   ```javascript
   window.__RELAY_DEV__.getCacheMetricsByQuery()
   ```
   If all queries show as "UnknownQuery", the extraction is failing.

2. **Verify query name extraction is working**:
   - Look at console logs during query execution
   - Should show actual query names, not "UnknownQuery"

**Solutions**:
- Enhanced query name extraction with multiple fallback methods
- Improved logging visibility

### Scenario 4: Fast Responses Still Marked as Cache Miss
**Problem**: Load times < 50ms but still "fromCache: false"

**Debug Steps**:
1. **Use debug delay to verify true cache behavior**:
   ```javascript
   window.__enableCacheDelay()
   // Reload and navigate - cache hits should be instant
   ```

2. **Check timing thresholds**:
   - New threshold is 100ms instead of 50ms
   - Network requests typically > 200ms

**Solutions**:
- Adjusted cache detection threshold
- Added debug delay for clear differentiation
- Enhanced cache hit detection logic

### Scenario 5: Real-Time Performance Testing
**Problem**: Need to understand cache behavior during actual usage

**Debug Steps**:
1. **Start real-time monitoring**:
   ```javascript
   window.__RELAY_DEV__.startCacheMonitoring()
   ```

2. **Create snapshots during usage**:
   ```javascript
   window.__RELAY_DEV__.createCacheSnapshot('start')
   // Use application normally
   window.__RELAY_DEV__.createCacheSnapshot('after_usage')
   window.__RELAY_DEV__.compareCacheSnapshots('start', 'after_usage')
   ```

3. **Simulate slow network conditions**:
   ```javascript
   window.__RELAY_DEV__.simulateSlowNetwork(3000)
   // Test how app performs with slow network - cache should provide fast responses
   ```

### Scenario 6: Persistence Effectiveness Analysis
**Problem**: Need to understand what data is being cached effectively

**Debug Steps**:
1. **Get comprehensive cache analysis**:
   ```javascript
   window.__RELAY_DEV__.printCacheSummary()
   ```

2. **Analyze persistence decisions**:
   ```javascript
   window.__RELAY_DEV__.analyzePersistenceDecisions()
   ```

3. **Test specific record persistence**:
   ```javascript
   window.__RELAY_DEV__.testPersistenceLogic()
   ```

## Enhanced Automatic Tracking

The updated `useSWRQuery` hook now automatically provides:

- ✅ **Detailed cache miss analysis** - Shows exactly why queries miss cache with missing record details
- ✅ **Pre-query analysis** - Analyzes cache hit probability before executing queries
- ✅ **Store analysis** - Shows record type distribution in the cache store
- ✅ **Enhanced query name extraction** - Multiple fallback methods for better query identification
- ✅ **Debug delay support** - 10-second network delay to clearly differentiate cache vs network
- ✅ **Improved cache hit detection** - More generous 100ms threshold with detailed reasoning
- ✅ **Unknown record analysis** - Detailed logging of why records are denied persistence
- ✅ **Persistence effectiveness tracking** - Shows which records are being cached vs denied

## Key Improvements for Cache Miss Debugging

### 1. Cache Miss Reason Detection
The system now identifies specific reasons for cache misses:
- `missing_query_records` - Required records not in cache
- `incomplete_data` - Some records available but not all needed
- `network_first_policy` - Forced network request due to policy
- `stale_data` - Cached data is too old
- `auth_error_cleared` - Cache cleared due to authentication errors

### 2. Unknown Record Analysis
High unknown record rates (70%+) are automatically detected and analyzed:
- Sample unknown records are logged for pattern analysis
- Warnings are displayed when unknown rates exceed 50%
- Detailed breakdown of denial reasons is provided

### 3. Query Name Visibility
Enhanced query name extraction replaces "UnknownQuery" with actual names:
- Multiple fallback methods for name extraction
- Better debugging visibility
- Query-specific performance tracking

### 4. Cache vs Network Testing
Debug delay functionality provides clear differentiation:
- Enable with `window.__enableCacheDelay()`
- Cache hits load instantly
- Network requests wait 10 seconds
- Clear visual distinction between cache and network behavior

## Production Monitoring

In production, the lightweight `relayObservability` still tracks basic metrics without console logging. Enable with:
```bash
VITE_ENABLE_RELAY_METRICS=true
```

## Enhanced Tips for Cache Debugging

1. **Use Debug Delay First**: Enable `window.__enableCacheDelay()` to clearly see cache vs network behavior
2. **Monitor Unknown Record Rates**: Check `printCacheSummary()` regularly for high denial rates
3. **Analyze Specific Query Misses**: Use `analyzeQueryCacheMiss('QueryName')` for detailed miss analysis
4. **Real-Time Monitoring**: Use `startCacheMonitoring()` during development sessions
5. **Snapshot Comparisons**: Create before/after snapshots for major operations
6. **Network Simulation**: Test cache effectiveness under slow network conditions
7. **Check Query Names**: Ensure queries show actual names, not "UnknownQuery"
8. **Regular Health Checks**: Run `checkCacheHealth()` to identify systemic issues

## Performance Notes

- Enhanced console logging only happens in development
- Debug delay is opt-in and disabled by default
- Real-time monitoring can be started/stopped as needed
- Snapshots are stored in window globals for easy access
- Network simulation is temporary and easily reversible
- All debug features tree-shake out in production builds

## Quick Start for Cache Miss Debugging

If you're experiencing cache miss issues, follow this workflow:

1. **Enable debug delay**: `window.__enableCacheDelay()`
2. **Reload and observe**: Cache hits are instant, network requests wait 10s
3. **Check cache health**: `window.__RELAY_DEV__.checkCacheHealth()`
4. **Analyze specific misses**: `window.__RELAY_DEV__.analyzeQueryCacheMiss('QueryName')`
5. **Review persistence**: `window.__RELAY_DEV__.printCacheSummary()`
6. **Fix allowlist patterns** if unknown record rate is high

This comprehensive debugging system provides deep visibility into cache behavior and helps identify and resolve performance issues effectively.
