/**
 * Cache filtering logic for selective persistence
 * Implements denylist-only strategy with default allow approach
 * 
 * Security-first approach: Deny sensitive records, allow business data by default
 */

// import { debug } from './debug'; // Unused in production cache filters
import type { RecordProxy } from 'relay-runtime';

// Security denylist patterns - records that should NEVER be cached
const SECURITY_DENYLIST = [
  // Authentication & Session (Critical)
  /^client:root:userInfo$/,
  /^UserInfoOutput:/,
  /^client:root:currentUser/,
  /^client:root:isAuthenticated$/,
  /^client:root:auth(Token|State|Error|Session)/,
  
  // System Errors (Critical)  
  /__typename.*Error$/,
  /^client:root:(error|networkError|authError)/,
  /Error:(.*)/,
  
  // Transient State (Important)
  /^client:root:(loading|submitting|validating|pending)/,
  /^client:root:(upload|temp|draft)/,
  
  // Additional security patterns
  /^client:[\w-]+:__field/,      // Field metadata  
  /^client:mutation:/,           // Mutation records
  /^client:query:/,              // Query records
  /^__/,                         // System records
  
  // PII Patterns (Configurable) - case insensitive
  /SSN|SocialSecurity/,
  /password|pwd|creditCard|bankAccount/i
];

// Connection edge limit configuration
const MAX_EDGES_PER_CONNECTION = parseInt(import.meta.env?.VITE_MAX_EDGES as string) || 100;

// Size limits for cache records (in bytes) 
const MAX_RECORD_SIZE_BYTES = 100 * 1024; // 100KB default limit
const SIZE_THRESHOLDS: Record<string, number> = {
  CustomViews: 30_000,  // 30KB for CustomViews as specified in feedback
};

// WeakMap for caching size calculations
const sizeCache = new WeakMap<object, number>();

/**
 * Calculate size of a record in bytes with caching
 */
function getRecordSize(record: unknown): number {
  if (typeof record !== 'object' || record === null) {
    return 0;
  }

  // Check cache first
  if (sizeCache.has(record)) {
    return sizeCache.get(record)!;
  }

  // Calculate size
  const size = JSON.stringify(record).length;
  sizeCache.set(record, size);
  return size;
}

/**
 * Get size threshold for a specific record type using lookup table
 */
function getSizeThreshold(typename?: string): number {
  // Use lookup table with default 100KB limit
  const maxSize = SIZE_THRESHOLDS[typename ?? ''] ?? MAX_RECORD_SIZE_BYTES;
  return maxSize;
}

/**
 * Check if dataID matches any security denylist pattern
 */
function isSecurityDenied(dataID: string): boolean {
  return SECURITY_DENYLIST.some(pattern => pattern.test(dataID));
}

/**
 * Check if record has error indicators
 */
function hasErrorIndicators(record: unknown): boolean {
  const typedRecord = record as { 
    __errors?: unknown[]; 
    __typename?: string; 
    extensions?: { authError?: unknown; networkError?: unknown }; 
  };

  return !!(
    typedRecord?.__errors || 
    typedRecord?.__typename === 'Error' ||
    typedRecord?.extensions?.authError || 
    typedRecord?.extensions?.networkError
  );
}

/**
 * Check if record contains user info/PII that should never be persisted
 */
export function isUserInfoRecord(id: string, record: unknown): boolean {
  const typedRecord = record as { __typename?: string };
  return id === 'client:root:userInfo' || typedRecord?.__typename === 'UserInfoOutput';
}

/**
 * Enhanced connection edge persistence with pagination slice preservation
 * Supports whitelisted entity types for pagination caching while maintaining size guards
 */
function trimConnectionEdges(record: unknown): unknown {
  if (typeof record !== 'object' || record === null) {
    return record;
  }

  const typedRecord = record as { 
    edges?: Record<string, unknown>; 
    __typename?: string;
    pageInfo?: unknown;
  };
  
  if (!typedRecord.edges || typeof typedRecord.edges !== 'object') {
    return record;
  }

  const edges = typedRecord.edges;
  const edgeEntries = Object.entries(edges);
  
  if (edgeEntries.length <= MAX_EDGES_PER_CONNECTION) {
    return record;
  }

  // Determine if this is a whitelisted entity type for pagination persistence
  const isWhitelistedForPagination = isBusinessEntityConnection(edgeEntries);
  
  let trimmedEdges: Record<string, unknown>;
  
  if (isWhitelistedForPagination) {
    // For business entities: preserve pagination slices intelligently
    // Keep edges that represent fetched pages (non-sparse preservation)
    trimmedEdges = preservePaginationSlices(edges, edgeEntries);
    
    if (process.env.NODE_ENV === 'development') {
      const entityType = getEntityTypeFromEdges(edgeEntries);
      console.log(`🔄 Preserved pagination slices for ${entityType}:`, {
        original: edgeEntries.length,
        preserved: Object.keys(trimmedEdges).length,
        entityType
      });
    }
  } else {
    // For non-business connections: use simple limit approach
    trimmedEdges = Object.fromEntries(
      edgeEntries.slice(0, MAX_EDGES_PER_CONNECTION)
    );
  }

  return {
    ...typedRecord,
    edges: trimmedEdges,
    // Preserve pageInfo for pagination state
    pageInfo: typedRecord.pageInfo
  };
}

/**
 * Check if connection contains business entity edges that should support pagination
 */
function isBusinessEntityConnection(edgeEntries: [string, unknown][]): boolean {
  if (edgeEntries.length === 0) return false;
  
  // Sample first few edges to determine entity type
  const sampleSize = Math.min(3, edgeEntries.length);
  
  for (let i = 0; i < sampleSize; i++) {
    const [, edgeValue] = edgeEntries[i];
    const edge = edgeValue as { node?: { __typename?: string } };
    const typename = edge?.node?.__typename;
    
    if (typename && isBusinessEntityType(typename)) {
      return true;
    }
  }
  
  return false;
}

/**
 * Check if typename represents a business entity that should be cached with pagination
 */
function isBusinessEntityType(typename: string): boolean {
  const businessEntityTypes = [
    'TimeSheet', 'Timesheet', 'TimesheetRow',
    'PayStub', 'PayStubDetail', 
    'Employee', 'EmployeeInfo',
    'Employer', 'EmployerInfo',
    'User', 'UserInfo',
    'Agreement', 'AgreementDetail',
    'Classification', 'Job', 'Position'
  ];
  
  return businessEntityTypes.includes(typename);
}

/**
 * Get entity type from edges for debugging
 */
function getEntityTypeFromEdges(edgeEntries: [string, unknown][]): string {
  if (edgeEntries.length === 0) return 'Unknown';
  
  const [, edgeValue] = edgeEntries[0];
  const edge = edgeValue as { node?: { __typename?: string } };
  return edge?.node?.__typename || 'Unknown';
}

/**
 * Preserve pagination slices by keeping fetched pages while respecting size limits
 * Strategy: Keep contiguous ranges of edges (representing fetched pages)
 */
function preservePaginationSlices(
  edges: Record<string, unknown>, 
  edgeEntries: [string, unknown][]
): Record<string, unknown> {
  // If we're under the limit anyway, keep everything
  if (edgeEntries.length <= MAX_EDGES_PER_CONNECTION) {
    return edges;
  }
  
  // Strategy: Keep the first N edges and the most recent fetched slice
  // This preserves both the initial page and the user's current position
  
  const halfLimit = Math.floor(MAX_EDGES_PER_CONNECTION / 2);
  
  // Keep first half (initial pages)
  const initialSlice = edgeEntries.slice(0, halfLimit);
  
  // Keep last half (most recent pages) 
  const recentSlice = edgeEntries.slice(-halfLimit);
  
  // Combine slices, removing duplicates if they overlap
  const preservedEntries = [...initialSlice];
  const initialKeys = new Set(initialSlice.map(([key]) => key));
  
  for (const entry of recentSlice) {
    if (!initialKeys.has(entry[0])) {
      preservedEntries.push(entry);
    }
  }
  
  return Object.fromEntries(preservedEntries);
}

/**
 * Check if record exceeds size threshold
 */
function exceedsSizeThreshold(record: unknown, typename?: string): boolean {
  const size = getRecordSize(record);
  const threshold = getSizeThreshold(typename);
  return size > threshold;
}

/**
 * Get persistence decision reason for debugging
 */
export type PersistenceReason = 
  | 'denied_error_indicators'
  | 'denied_security_denylist'
  | 'denied_size_exceeded'
  | 'allowed_default'
  | 'allowed_client_root_sanitized';


/**
 * Simplified denylist-only persistence filter with default allow approach
 * Returns [shouldPersist, reason, processedRecord] tuple for debugging
 */
export function shouldPersistRecordWithReason(dataID: string, record: unknown): [boolean, PersistenceReason, unknown?] {
  // (a) Sanitize and persist client:root with sensitive fields removed
  if (dataID === 'client:root') {
    // Type-safe sanitization using denylist approach
    const sourceRecord = record as Record<string, unknown>;
    const sanitized: Record<string, unknown> = {};
    
    // Sensitive fields that must be excluded (security-critical)
    const sensitiveFields = new Set([
      // Authentication & Session data
      'userInfo', 'currentUser', 'isAuthenticated',
      'authToken', 'authState', 'authError', 'authSession',
      'sessionToken', 'accessToken', 'refreshToken',
      
      // Transient state
      'loading', 'submitting', 'validating', 'pending',
      'error', 'networkError',
      
      // Upload/temp data
      'upload', 'temp', 'draft',
      
      // Other sensitive patterns
      'password', 'pwd', 'secret', 'key', 'token'
    ]);
    
    // Copy all fields except sensitive ones
    for (const [key, value] of Object.entries(sourceRecord)) {
      const isFieldSensitive = sensitiveFields.has(key) || 
                               key.toLowerCase().includes('password') ||
                               key.toLowerCase().includes('secret') ||
                               key.toLowerCase().includes('token');
                               
      if (!isFieldSensitive) {
        sanitized[key] = value;
      }
    }
    
    return [true, 'allowed_client_root_sanitized', sanitized];
  }

  // (b) Check for error indicators
  if (hasErrorIndicators(record)) {
    return [false, 'denied_error_indicators'];
  }

  // (c) Check security denylist patterns (includes PII, auth, errors, system records)
  if (isSecurityDenied(dataID)) {
    return [false, 'denied_security_denylist'];
  }

  // Additional PII check by typename (legacy compatibility)
  if (isUserInfoRecord(dataID, record)) {
    return [false, 'denied_security_denylist'];
  }

  // Type guard for record structure
  const typedRecord = record as { __typename?: string };
  const typename = typedRecord?.__typename;

  // Process connection records with edge trimming
  let processedRecord = record;
  if (dataID.includes('__connection')) {
    processedRecord = trimConnectionEdges(record);
  }

  // (d) Check size threshold (use processed record for connections)
  if (exceedsSizeThreshold(processedRecord, typename)) {
    return [false, 'denied_size_exceeded'];
  }

  // (e) Default allow - everything else persists
  return [true, 'allowed_default', processedRecord];
}

/**
 * Main persistence filter function (backward compatible)
 * Returns [shouldPersist, processedRecord] to support edge trimming
 */
export function shouldPersistRecord(dataID: string, record: unknown): boolean {
  return shouldPersistRecordWithReason(dataID, record)[0];
}

/**
 * Get processed record for persistence (with edge trimming applied)
 */
export function getProcessedRecord(dataID: string, record: unknown): unknown {
  // Apply edge trimming for connections regardless of persistence decision
  if (dataID.includes('__connection')) {
    return trimConnectionEdges(record);
  }
  return record;
}

/**
 * Get cache statistics for monitoring
 */
export interface CacheFilterStats {
  totalRecords: number;
  persistedRecords: number;
  totalSize: number;
  persistedSize: number;
  reasonBreakdown: Record<PersistenceReason, number>;
}

export function getCacheFilterStats(recordMap: Record<string, unknown>): CacheFilterStats {
  const stats: CacheFilterStats = {
    totalRecords: 0,
    persistedRecords: 0,
    totalSize: 0,
    persistedSize: 0,
    reasonBreakdown: {
      denied_error_indicators: 0,
      denied_security_denylist: 0,
      denied_size_exceeded: 0,
      allowed_default: 0,
      allowed_client_root_sanitized: 0
    }
  };

  for (const [dataID, record] of Object.entries(recordMap)) {
    stats.totalRecords++;
    const size = getRecordSize(record);
    stats.totalSize += size;

    const [shouldPersist, reason] = shouldPersistRecordWithReason(dataID, record);
    stats.reasonBreakdown[reason]++;

    if (shouldPersist) {
      stats.persistedRecords++;
      stats.persistedSize += size;
    }
  }

  return stats;
}

/**
 * PHASE 3: Performance metrics tracking for cache operations
 */
interface CacheMetrics {
  edgeMergeOperations: number;
  snapshotUsageCount: number;
  businessEntityMerges: number;
  fallbackOperations: number;
}

// PHASE 3: Global metrics counters for cache performance monitoring
const cacheMetrics: CacheMetrics = {
  edgeMergeOperations: 0,
  snapshotUsageCount: 0,
  businessEntityMerges: 0,
  fallbackOperations: 0
};

/**
 * PHASE 3: Get current cache performance metrics
 */
export function getCacheMetrics(): Readonly<CacheMetrics> {
  return { ...cacheMetrics };
}

/**
 * PHASE 3: Reset cache metrics (useful for testing)
 */
export function resetCacheMetrics(): void {
  cacheMetrics.edgeMergeOperations = 0;
  cacheMetrics.snapshotUsageCount = 0;
  cacheMetrics.businessEntityMerges = 0;
  cacheMetrics.fallbackOperations = 0;
}

/**
 * Edge merge helper for background revalidation to preserve pagination slices
 * Merges new edges into existing connection without losing paginated data
 * 
 * PHASE 2: Enhanced to accept existingEdgesSnapshot to avoid relying on connection's current state
 * PHASE 3: Enhanced with metrics tracking and comprehensive store param documentation
 * 
 * @param store - PHASE 3: Relay store proxy for transaction context (required for RecordProxy operations)
 *                This provides the transactional context needed for all RecordProxy methods.
 *                While not directly used in current implementation, it's required by Relay's 
 *                commitUpdate pattern and may be needed for future store invalidation operations.
 * @param connection - Connection record proxy containing edges to merge
 * @param newEdges - New edges from background revalidation to merge in
 * @param existingEdgesSnapshot - PHASE 2: Pre-captured existing edges to avoid relying on connection's current state
 */
export function mergeConnectionEdges(
  store: RecordProxy, // PHASE 3: Documented - Relay store proxy for transaction context
  connection: RecordProxy, // Connection record proxy
  newEdges: readonly RecordProxy[], // New edges from revalidation
  existingEdgesSnapshot?: readonly RecordProxy[] // PHASE 2: Pre-captured existing edges
): void {
  if (!connection || !newEdges || newEdges.length === 0) {
    return;
  }

  // PHASE 3: Track edge merge operations
  cacheMetrics.edgeMergeOperations++;

  try {
    // PHASE 2: Use existingEdgesSnapshot if provided, otherwise fallback to connection's current edges
    const existingEdges = existingEdgesSnapshot || connection.getLinkedRecords('edges') || [];
    
    // PHASE 3: Track snapshot usage
    if (existingEdgesSnapshot) {
      cacheMetrics.snapshotUsageCount++;
    }
    
    // Create a map of existing edges by their cursor or node ID for deduplication
    const existingEdgeMap = new Map();
    
    existingEdges.forEach((edge: RecordProxy | null, index: number) => {
      if (edge) {
        const cursor = edge.getValue('cursor');
        const node = edge.getLinkedRecord('node');
        const nodeId = node?.getValue('id');
        
        // Use cursor as primary key, fallback to node ID, then position
        const key = cursor || nodeId || `position_${index}`;
        existingEdgeMap.set(key, edge);
      }
    });

    // Merge new edges, avoiding duplicates
    const mergedEdges = [...existingEdges];
    
    newEdges.forEach((newEdge: RecordProxy) => {
      if (newEdge) {
        const cursor = newEdge.getValue('cursor');
        const node = newEdge.getLinkedRecord('node');
        const nodeId = node?.getValue('id');
        
        const key = cursor || nodeId;
        
        // Only add if we don't already have this edge
        if (key && !existingEdgeMap.has(key)) {
          mergedEdges.push(newEdge);
          existingEdgeMap.set(key, newEdge);
        }
      }
    });

    // Apply size limits while preserving pagination slices for business entities
    let finalEdges = mergedEdges;
    
    if (mergedEdges.length > MAX_EDGES_PER_CONNECTION) {
      // Check if this is a business entity connection
      const isBusinessEntity = mergedEdges.some((edge: RecordProxy | null) => {
        const node = edge?.getLinkedRecord('node');
        const typename = node?.getValue('__typename');
        return typename && isBusinessEntityType(typename as string);
      });
      
      if (isBusinessEntity) {
        // PHASE 3: Track business entity merges
        cacheMetrics.businessEntityMerges++;
        
        // Use smart pagination preservation for business entities
        const halfLimit = Math.floor(MAX_EDGES_PER_CONNECTION / 2);
        const initialSlice = mergedEdges.slice(0, halfLimit);
        const recentSlice = mergedEdges.slice(-halfLimit);
        
        // Combine slices, removing duplicates
        const preservedEdges = [...initialSlice];
        const initialIds = new Set(
          initialSlice.map((edge: RecordProxy | null) => {
            const node = edge?.getLinkedRecord('node');
            return node?.getValue('id') || edge?.getValue('cursor');
          }).filter(Boolean)
        );
        
        for (const edge of recentSlice) {
          const node = edge?.getLinkedRecord('node');
          const id = node?.getValue('id') || edge?.getValue('cursor');
          if (id && !initialIds.has(id)) {
            preservedEdges.push(edge);
          }
        }
        
        finalEdges = preservedEdges;
      } else {
        // Simple truncation for non-business entities
        finalEdges = mergedEdges.slice(0, MAX_EDGES_PER_CONNECTION);
      }
    }

    // Update the connection with merged edges
    connection.setLinkedRecords(finalEdges, 'edges');
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔄 [SWR] Merged connection edges:`, {
        existing: existingEdges.length,
        new: newEdges.length,
        merged: mergedEdges.length,
        final: finalEdges.length,
        preserved: finalEdges.length < mergedEdges.length ? 'pagination slices' : 'all edges',
        usingSnapshot: !!existingEdgesSnapshot // PHASE 2: Indicate if snapshot was used
      });
    }
    
  } catch (error) {
    // PHASE 3: Track fallback operations
    cacheMetrics.fallbackOperations++;
    
    if (process.env.NODE_ENV === 'development') {
      console.warn('[SWR] Failed to merge connection edges:', error);
    }
    // Fallback: just update with new edges if merge fails
    try {
      connection.setLinkedRecords([...newEdges], 'edges');
    } catch (fallbackError) {
      console.error('[SWR] Failed to set new edges as fallback:', fallbackError);
    }
  }
}