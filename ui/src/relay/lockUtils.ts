/**
 * Web Locks API utilities for multi-tab coordination
 * 
 * Provides centralized lock configuration and error handling patterns
 * to eliminate duplication and ensure consistent behavior across the app.
 */

import { relayObservability } from './observability';

/**
 * Build standardized lock options based on controller availability
 * 
 * @param controller - Optional AbortController for timeout/cancellation
 * @returns LockOptions with proper mode and signal configuration
 */
export function buildLockOptions(controller?: AbortController): LockOptions {
  return controller && 'signal' in controller
    ? { mode: 'exclusive', signal: controller.signal }
    : { mode: 'exclusive', ifAvailable: true };
}

/**
 * Check if Web Locks API is available in the current environment
 * 
 * @returns true if Web Locks API is supported and available
 */
export function isWebLocksAvailable(): boolean {
  return typeof navigator !== 'undefined' && 
         'locks' in navigator && 
         navigator.locks !== null && 
         navigator.locks !== undefined;
}

/**
 * Enhanced multi-tab lock wrapper with centralized error handling
 * 
 * @param lockName - Name of the lock to acquire
 * @param operation - Async operation to execute while holding the lock
 * @param timeoutMs - Lock timeout in milliseconds (default: 5000)
 * @returns Promise resolving to operation result
 */
export async function withWebLock<T>(
  lockName: string,
  operation: () => Promise<T>,
  timeoutMs: number = 5000
): Promise<T> {
  // Fallback for browsers without Web Locks API
  if (!isWebLocksAvailable()) {
    return await operation();
  }

  try {
    // Use AbortController for timeout
    const abortController = new AbortController();
    const timeoutId = setTimeout(() => abortController.abort(), timeoutMs);
    
    try {
      const result = await navigator.locks.request(
        lockName,
        buildLockOptions(abortController),
        async (lock): Promise<T | undefined> => {
          clearTimeout(timeoutId);
          
          // If lock is null, it means ifAvailable was true but lock was not available
          if (!lock) {
            // Track lock unavailable for observability
            if (relayObservability?.trackLockUnavailable) {
              relayObservability.trackLockUnavailable();
            }
            if (process.env.NODE_ENV === 'development') {
              // eslint-disable-next-line no-console
              console.warn(`[Web Locks] Lock "${lockName}" unavailable, skipping operation`);
            }
            return undefined; // Skip operation but don't fail
          }
          
          return await operation();
        }
      );
      
      return result as T;
    } finally {
      clearTimeout(timeoutId);
    }
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.warn(`[Web Locks] Lock "${lockName}" failed, proceeding without coordination:`, error);
    }
    // Fallback to direct operation if locks fail
    return await operation();
  }
}