import React, { <PERSON><PERSON>N<PERSON>, ErrorInfo } from 'react';
import { View, Heading, Text, Button, Flex } from '@adobe/react-spectrum';
import { useNavigate } from 'react-router';
import { LABEL_TEXT } from '../../constants/text';

interface ErrorBoundaryContentProps {
    error: Error | null;
    errorInfo: ErrorInfo | null;
    fallbackComponent?: ReactNode;
    onReset: () => void;
}

export const ErrorBoundaryContent: React.FC<ErrorBoundaryContentProps> = ({ error, fallbackComponent, onReset }) => {
    const navigate = useNavigate();

    const handleBackToRoster = React.useCallback(() => {
        void navigate('/timesheet-roster');
    }, [navigate]);

    if (fallbackComponent) {
        return <>{fallbackComponent}</>;
    }

    return (
        <View padding="size-500">
            <Flex direction="column" gap="size-200" alignItems="center">
                <Heading level={2}>{LABEL_TEXT.SOMETHING_WENT_WRONG}</Heading>
                <Text>{LABEL_TEXT.ERROR_DISPLAYING_TIMESHEET}</Text>
                {error && (
                    <View backgroundColor="gray-200" padding="size-200" borderRadius="medium">
                        <Text UNSAFE_style={{ fontFamily: 'monospace', fontSize: '12px' }}>{error.message}</Text>
                    </View>
                )}
                <Flex gap="size-200">
                    <Button variant="primary" onPress={onReset}>
                        {LABEL_TEXT.TRY_AGAIN}
                    </Button>
                    <Button variant="secondary" onPress={handleBackToRoster}>
                        {LABEL_TEXT.BACK_TO_ROSTER}
                    </Button>
                </Flex>
            </Flex>
        </View>
    );
};
