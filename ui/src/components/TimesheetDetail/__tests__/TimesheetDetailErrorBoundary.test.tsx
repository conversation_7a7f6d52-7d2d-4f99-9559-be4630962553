import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { TimesheetDetailErrorBoundary } from '../TimesheetDetailErrorBoundary';
import { LABEL_TEXT } from '../../../constants/text';

// Mock react-router
const mockNavigate = jest.fn();
jest.mock('react-router', () => ({
    useNavigate: () => mockNavigate
}));

// Mock Adobe Spectrum components
jest.mock('@adobe/react-spectrum', () => ({
    View: ({ children, ...props }: any) => (
        <div data-testid="view" {...props}>
            {children}
        </div>
    ),
    Heading: ({ children, level }: any) => (
        <h2 data-testid="heading" data-level={level}>
            {children}
        </h2>
    ),
    Text: ({ children }: any) => (
        <span data-testid="text">{children}</span>
    ),
    Button: ({ children, onPress, variant }: any) => (
        <button data-testid={`button-${variant}`} onClick={onPress}>
            {children}
        </button>
    ),
    Flex: ({ children, ...props }: any) => (
        <div data-testid="flex" {...props}>
            {children}
        </div>
    )
}));

// Component that throws an error for testing
const ThrowingComponent: React.FC<{ shouldThrow?: boolean; message?: string }> = ({ 
    shouldThrow = true, 
    message = 'Test error' 
}) => {
    if (shouldThrow) {
        throw new Error(message);
    }
    return <div data-testid="working-component">Working component</div>;
};

describe('TimesheetDetailErrorBoundary', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockNavigate.mockClear();
        // Suppress console.error during tests
        jest.spyOn(console, 'error').mockImplementation(() => {});
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('Error Handling', () => {
        it('should catch and display error with default fallback UI', () => {
            render(
                <TimesheetDetailErrorBoundary>
                    <ThrowingComponent />
                </TimesheetDetailErrorBoundary>
            );

            expect(screen.getByText(LABEL_TEXT.SOMETHING_WENT_WRONG)).toBeInTheDocument();
            expect(screen.getByText(LABEL_TEXT.ERROR_DISPLAYING_TIMESHEET)).toBeInTheDocument();
            expect(screen.getByText(LABEL_TEXT.TRY_AGAIN)).toBeInTheDocument();
            expect(screen.getByText(LABEL_TEXT.BACK_TO_ROSTER)).toBeInTheDocument();
            expect(screen.queryByTestId('working-component')).not.toBeInTheDocument();
        });

        it('should display error message details in the fallback UI', () => {
            const errorMessage = 'Custom error message for testing';
            
            render(
                <TimesheetDetailErrorBoundary>
                    <ThrowingComponent message={errorMessage} />
                </TimesheetDetailErrorBoundary>
            );

            expect(screen.getByText(errorMessage)).toBeInTheDocument();
        });

        it('should display custom fallback component when provided', () => {
            const customFallback = <div data-testid="custom-fallback">Custom error display</div>;

            render(
                <TimesheetDetailErrorBoundary fallbackComponent={customFallback}>
                    <ThrowingComponent />
                </TimesheetDetailErrorBoundary>
            );

            expect(screen.getByTestId('custom-fallback')).toBeInTheDocument();
            expect(screen.getByText('Custom error display')).toBeInTheDocument();
            expect(screen.queryByText(LABEL_TEXT.SOMETHING_WENT_WRONG)).not.toBeInTheDocument();
        });

        it('should log error to console', () => {
            const consoleSpy = jest.spyOn(console, 'error');
            const errorMessage = 'Console error test';

            render(
                <TimesheetDetailErrorBoundary>
                    <ThrowingComponent message={errorMessage} />
                </TimesheetDetailErrorBoundary>
            );

            expect(consoleSpy).toHaveBeenCalledWith(
                'TimesheetDetail Error:',
                expect.objectContaining({
                    message: errorMessage
                }),
                expect.objectContaining({
                    componentStack: expect.any(String)
                })
            );
        });
    });

    describe('onError Callback', () => {
        it('should call onError prop with error and errorInfo when error occurs', () => {
            const onError = jest.fn();
            const errorMessage = 'Callback test error';

            render(
                <TimesheetDetailErrorBoundary onError={onError}>
                    <ThrowingComponent message={errorMessage} />
                </TimesheetDetailErrorBoundary>
            );

            expect(onError).toHaveBeenCalledTimes(1);
            expect(onError).toHaveBeenCalledWith(
                expect.objectContaining({
                    message: errorMessage
                }),
                expect.objectContaining({
                    componentStack: expect.any(String)
                })
            );
        });

        it('should not throw when onError is not provided', () => {
            expect(() => {
                render(
                    <TimesheetDetailErrorBoundary>
                        <ThrowingComponent />
                    </TimesheetDetailErrorBoundary>
                );
            }).not.toThrow();
        });
    });

    describe('Recovery and Reset', () => {
        it('should reset error state when Try Again button is clicked', async () => {
            const user = userEvent.setup();
            let shouldThrow = true;

            const TestComponent = () => {
                const [throwError, setThrowError] = React.useState(true);
                shouldThrow = throwError;

                return (
                    <TimesheetDetailErrorBoundary>
                        <ThrowingComponent shouldThrow={throwError} />
                        <button onClick={() => setThrowError(false)}>Fix Error</button>
                    </TimesheetDetailErrorBoundary>
                );
            };

            const { rerender } = render(<TestComponent />);

            // Verify error state
            expect(screen.getByText(LABEL_TEXT.SOMETHING_WENT_WRONG)).toBeInTheDocument();
            expect(screen.queryByTestId('working-component')).not.toBeInTheDocument();

            // Fix the error source
            shouldThrow = false;

            // Click Try Again
            const tryAgainButton = screen.getByTestId('button-primary');
            await user.click(tryAgainButton);

            // Force re-render
            rerender(<TestComponent />);

            // Verify component is now working
            await waitFor(() => {
                expect(screen.queryByText(LABEL_TEXT.SOMETHING_WENT_WRONG)).not.toBeInTheDocument();
                expect(screen.getByTestId('working-component')).toBeInTheDocument();
                expect(screen.getByText('Working component')).toBeInTheDocument();
            });
        });

        it('should navigate back when Back to Roster button is clicked', async () => {
            const user = userEvent.setup();
            mockNavigate.mockClear();

            render(
                <TimesheetDetailErrorBoundary>
                    <ThrowingComponent />
                </TimesheetDetailErrorBoundary>
            );

            const backButton = screen.getByTestId('button-secondary');
            await user.click(backButton);

            expect(mockNavigate).toHaveBeenCalledTimes(1);
            expect(mockNavigate).toHaveBeenCalledWith('/timesheet-roster');
        });
    });

    describe('Normal Operation', () => {
        it('should render children normally when no error occurs', () => {
            render(
                <TimesheetDetailErrorBoundary>
                    <ThrowingComponent shouldThrow={false} />
                </TimesheetDetailErrorBoundary>
            );

            expect(screen.getByTestId('working-component')).toBeInTheDocument();
            expect(screen.getByText('Working component')).toBeInTheDocument();
            expect(screen.queryByText(LABEL_TEXT.SOMETHING_WENT_WRONG)).not.toBeInTheDocument();
        });

        it('should pass through props to children', () => {
            const TestComponent: React.FC<{ testProp: string }> = ({ testProp }) => (
                <div data-testid="test-component">{testProp}</div>
            );

            render(
                <TimesheetDetailErrorBoundary>
                    <TestComponent testProp="test value" />
                </TimesheetDetailErrorBoundary>
            );

            expect(screen.getByText('test value')).toBeInTheDocument();
        });

        it('should handle multiple children', () => {
            render(
                <TimesheetDetailErrorBoundary>
                    <div data-testid="child-1">Child 1</div>
                    <div data-testid="child-2">Child 2</div>
                    <div data-testid="child-3">Child 3</div>
                </TimesheetDetailErrorBoundary>
            );

            expect(screen.getByTestId('child-1')).toBeInTheDocument();
            expect(screen.getByTestId('child-2')).toBeInTheDocument();
            expect(screen.getByTestId('child-3')).toBeInTheDocument();
        });
    });

    describe('Error State Management', () => {
        it('should maintain error state until reset', async () => {
            const user = userEvent.setup();
            
            const { rerender } = render(
                <TimesheetDetailErrorBoundary>
                    <ThrowingComponent />
                </TimesheetDetailErrorBoundary>
            );

            // Verify error state persists
            expect(screen.getByText(LABEL_TEXT.SOMETHING_WENT_WRONG)).toBeInTheDocument();

            // Re-render without changes
            rerender(
                <TimesheetDetailErrorBoundary>
                    <ThrowingComponent />
                </TimesheetDetailErrorBoundary>
            );

            // Error state should still be shown
            expect(screen.getByText(LABEL_TEXT.SOMETHING_WENT_WRONG)).toBeInTheDocument();
        });

        it('should handle errors thrown during render phase', () => {
            const RenderErrorComponent = () => {
                throw new Error('Render phase error');
            };

            render(
                <TimesheetDetailErrorBoundary>
                    <RenderErrorComponent />
                </TimesheetDetailErrorBoundary>
            );

            expect(screen.getByText(LABEL_TEXT.SOMETHING_WENT_WRONG)).toBeInTheDocument();
            expect(screen.getByText('Render phase error')).toBeInTheDocument();
        });

        it('should handle errors thrown in event handlers when error is rethrown in render', async () => {
            const user = userEvent.setup();
            const EventErrorComponent = () => {
                const [error, setError] = React.useState<Error | null>(null);
                
                // Rethrow error in render to be caught by error boundary
                if (error) {
                    throw error;
                }
                
                const handleClick = () => {
                    setError(new Error('Event handler error'));
                };
                
                return <button onClick={handleClick}>Click me</button>;
            };

            render(
                <TimesheetDetailErrorBoundary>
                    <EventErrorComponent />
                </TimesheetDetailErrorBoundary>
            );

            // Click button to trigger error
            const button = screen.getByText('Click me');
            await user.click(button);

            // Error boundary should catch the error
            await waitFor(() => {
                expect(screen.getByText(LABEL_TEXT.SOMETHING_WENT_WRONG)).toBeInTheDocument();
                expect(screen.getByText('Event handler error')).toBeInTheDocument();
            });
        });
    });
});