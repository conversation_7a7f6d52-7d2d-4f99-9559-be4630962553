import type { 
    EmployerRosterViewFilterInput, 
    IntOperationFilterInput 
} from '@/relay/__generated__/EmployerRosterGridRefetchQuery.graphql';
import type { BenefitElectionsRosterDtoFilterInput } from '@/relay/__generated__/BenefitElectionsRefetchQuery.graphql';
import { INACTIVE_FILTER_FIELD } from '@/src/constants/employer-roster';

/**
 * Type definitions for employer roster filter operations
 */

// Union type for all possible filter conditions in employer roster context
export type FilterCondition = EmployerRosterViewFilterInput | BenefitElectionsRosterDtoFilterInput;

// Extended filter condition type that includes the inactive filter field
export type FilterConditionWithInactiveField = FilterCondition & {
    [INACTIVE_FILTER_FIELD]?: IntOperationFilterInput | null;
};

/**
 * Type guard to check if a filter condition has the inactive filter field
 */
export const hasInactiveFilterField = (condition: FilterCondition): condition is FilterConditionWithInactiveField => {
    return INACTIVE_FILTER_FIELD in condition && condition[INACTIVE_FILTER_FIELD as keyof FilterCondition] !== undefined;
};

/**
 * Combined type guard for null check and inactive filter field check
 */
export const isValidInactiveFilter = (condition: FilterCondition | null | undefined): condition is FilterConditionWithInactiveField => {
    return condition != null && hasInactiveFilterField(condition);
};