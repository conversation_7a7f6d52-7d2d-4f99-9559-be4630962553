/**
 * Bootstrap module for early application initialization
 * 
 * This module MUST be imported before any Relay environment creation
 * to ensure proper schema hash initialization for selective cache persistence.
 * 
 * Implementation addresses G-1: Schema hash bootstrap moved to proper location
 */

import { setSchemaHash } from '@/src/relay/createPersistedStore';

/**
 * Initialize schema hash from environment variables
 * 
 * CRITICAL: This must run before the Relay environment is created
 * to ensure cache versioning works correctly across deployments.
 * 
 * Environment variable expectations:
 * - VITE_GQL_SCHEMA_HASH: Required in production builds
 * - Development: Falls back to 'dev' for local development
 */
function initializeSchemaHash(): void {
  const schemaHash = import.meta.env.VITE_GQL_SCHEMA_HASH;
  
  if (schemaHash) {
    setSchemaHash(schemaHash);
    
    if (import.meta.env.DEV) {
      console.log(`[Bootstrap] Schema hash initialized: ${schemaHash}`);
    }
  } else {
    // In production, missing schema hash is a critical error
    if (import.meta.env.PROD) {
      throw new Error(
        'VITE_GQL_SCHEMA_HASH environment variable is required for production builds. ' +
        'This ensures cache invalidation works correctly across deployments.'
      );
    }
    
    // Development fallback
    if (import.meta.env.DEV) {
      console.warn('[Bootstrap] No VITE_GQL_SCHEMA_HASH found, using development fallback');
      setSchemaHash('dev');
    }
  }
}

// Execute initialization immediately when module is imported
initializeSchemaHash();

/**
 * Export for explicit initialization if needed
 * (primarily for testing scenarios)
 */
export { initializeSchemaHash };