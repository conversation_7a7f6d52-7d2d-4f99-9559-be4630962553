---
name: Technology Request
about: Propose a new technology change or use in EPRLive
title: ""
labels: Tech Request
type: "Task"
assignees: ''
---

### Technology Name
##### The name of the technology being proposed (and what it's replacing if applicable)

---

### What Problem Are We Solving?
Explain the pain point and why the current stack is insufficient

---

### Why This Technology?
How it helps, unique strengths, community support, maintenance record, size

---

### Alternatives Considered
List at least two alternatives and why they were rejected

---

### Risks & Costs
- Long-term maintenance?
- Security/compliance?
- Performance/regression?
- Does the team need onboarding?
- What friction might this introduce?

---

### Accountability
Name whose expected to:
- Own implementation
- Maintain documentation
- Answer any questions about this technology

---

### Documentation Requirements
*What are the documentation requirements for implementing this tech change, for example:*
- Create a new 'Using [Tech] in EPRLive' document
- PR includes notes on where/how it was implemented
- Update internal documentation (e.g., SharePoint: Page Knowledge Base or Tech Stack Overview)

---

### Approval Path Documentation
*Who is needed to approve this new technology? For example:*
- Lead Developer 
- Product Manager 
- Leadership Team and/or CEO for platform-level changes
