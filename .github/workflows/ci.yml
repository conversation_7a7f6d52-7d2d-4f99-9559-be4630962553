name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'pnpm'
        cache-dependency-path: 'ui/pnpm-lock.yaml'
    
    - name: Install pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 8
    
    - name: Install UI dependencies
      working-directory: ./ui
      run: pnpm install --frozen-lockfile
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: '9.0.x'
    
    - name: Restore backend dependencies
      working-directory: ./backend
      run: dotnet restore
    
    - name: Build backend
      working-directory: ./backend
      run: dotnet build --no-restore
    
    - name: Generate GraphQL schema
      working-directory: ./backend
      run: dotnet run --project backend.csproj -- schema export --output ../ui/schema.graphql
    
    - name: Compute schema hash
      id: compute-hash
      working-directory: ./ui
      run: |
        mkdir -p .cache
        node -e "
          const fs = require('fs');
          const crypto = require('crypto');
          const schema = fs.readFileSync('schema.graphql', 'utf8');
          const hash = crypto.createHash('sha256').update(schema).digest('hex').substring(0, 16);
          fs.writeFileSync('.cache/schema-hash.txt', hash);
          console.log('Generated schema hash:', hash);
        "
        echo "hash=$(cat .cache/schema-hash.txt)" >> $GITHUB_OUTPUT
    
    - name: Generate schema hash script
      working-directory: ./ui
      run: |
        node -e "
          const fs = require('fs');
          const hash = fs.readFileSync('.cache/schema-hash.txt', 'utf8').trim();
          const script = \`#!/bin/bash\necho \"export VITE_GQL_SCHEMA_HASH=\${hash}\"\`;
          fs.writeFileSync('scripts/generate-schema-hash.js', \`
            const fs = require('fs');
            const crypto = require('crypto');
            const schema = fs.readFileSync('schema.graphql', 'utf8');
            const hash = crypto.createHash('sha256').update(schema).digest('hex').substring(0, 16);
            fs.writeFileSync('.cache/schema-hash.txt', hash);
            console.log(hash);
          \`);
        "
    
    - name: Validate allowlist synchronization
      working-directory: ./ui
      run: pnpm validate:allowlist
    
    - name: Build client
      working-directory: ./ui
      env:
        VITE_GQL_SCHEMA_HASH: ${{ steps.compute-hash.outputs.hash }}
      run: |
        export VITE_GQL_SCHEMA_HASH=$(cat .cache/schema-hash.txt)
        echo "Using schema hash: $VITE_GQL_SCHEMA_HASH"
        pnpm build
    
    - name: Run backend tests
      working-directory: ./backend
      run: dotnet test --no-build --verbosity normal
    
    - name: Run UI tests
      working-directory: ./ui
      run: pnpm test:ci
    
    - name: Validate cache performance
      working-directory: ./ui
      run: |
        # Run performance validation test
        pnpm test --testNamePattern="cache performance meets targets"
        
        # Validate schema hash integration
        if [ ! -f ".cache/schema-hash.txt" ]; then
          echo "❌ Schema hash not generated"
          exit 1
        fi
        
        echo "✅ All performance validations passed"