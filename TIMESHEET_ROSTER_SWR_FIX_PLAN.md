# 📑 Implementation Plan – <PERSON><PERSON> for `TimesheetRosterQuery`

**Date created:** 2025-08-04
**Owner:** Frontend Infra Team (Relay & SWR)

---

## 1 Objectives

1. Guarantee cache-key stability so the same string is produced for every read/write of a query.
2. Ensure variables sent to `createCacheKey()` never contain `undefined`, functions, or non-serialisable values.
3. Persist an initial per-operation timestamp even when the associated records are trimmed/filtered, so age-based revalidation still works.
4. Provide explicit instrumentation to trace per-operation timestamp writes in development.
5. Ship unit & integration tests that capture the regression.

---

## 2 Scope of Work

| # | Area / File | Action |
|---|-------------|--------|
| 2.1 | `ui/src/relay/useSWRQuery.ts` | Replace current `createCacheKey()` with **deep-stable serialisation** (use `json-stable-stringify` or internal util) so nested objects/arrays get deterministic ordering. |
| 2.2 | `ui/src/container/TimesheetRoster/TimesheetRoster.tsx` | Introduce a small helper `prepareVariables()` that: 1) deep-clones variables, 2) prunes keys whose value is `undefined`, 3) converts any `Date` → ISO string, and 4) ensures arrays are sorted when order doesn’t matter. Pass the result to `useSWRQuery`. |
| 2.3 | `ui/src/relay/cacheFilters.ts` / `createPersistedStore.ts` | When persisting records, if **edge-trimming** removes all edges from a connection but the connection record remains, ensure that `globalOpSavedAt[cacheKey]` is written **once** right after publish so SWR can still compute age. |
| 2.4 | `ui/src/relay/useSWRQuery.ts` | Add `debug.log('📝 Per-op timestamp write', { cacheKey })` immediately after the assignment in `triggerBackgroundRevalidation()` (dev-only). |
| 2.5 | 🆕 `ui/src/relay/__tests__/createCacheKey.test.ts` | Unit tests that assert two differently-ordered variable objects yield identical cache keys. |
| 2.6 | 🆕 `ui/src/relay/__tests__/swrRoster.integration.test.ts` | End-to-end test (Jest + msw) that mounts `TimesheetRoster` → asserts 1) initial store-only read, 2) background revalidation request after 30 s, 3) `window.__RELAY_OP_SAVED_AT__` entry exists. |

---

## 3 Detailed Steps

### Step A – Stable serialisation util
1. Add dependency `json-stable-stringify` (pnpm).
2. Create `ui/src/utils/stableStringify.ts` that wraps the lib and falls back to built-in `JSON.stringify` in prod to keep bundle small.
3. Update `createCacheKey()`:
   ```ts
   import stableStringify from '@/src/utils/stableStringify';
   …
   const variablesKey = stableStringify(variables);
   ```

### Step B – Variable sanitiser in roster container
1. New helper `cleanVariables(obj)` using recursion: remove `undefined`, convert functions → string `"[fn]"` (should never happen but keeps deterministic), convert `Date`.
2. Apply before call to `useSWRQuery`.

### Step C – Guarantee timestamp persistence
1. Inside `createPersistedStore` after every `store.publish`, check if `operationDescriptor.request.node.params.name` exists in `globalOpSavedAt`; if **missing**, add it with `Date.now()` and call `notifyStoreUpdated()`.
2. Guard with `if (!(key in globalOpSavedAt))` to avoid hot-path performance hit.

### Step D – Instrumentation
Add `debug.log` statement (dev-only) as described.

### Step E – Tests
1. Unit: build two variable objects with property order swapped → expect identical keys.
2. Integration: mount component with mocked network, advance fake timers by 31 000 ms → expect `fetchQuery` to have been called once.

---

## 4 Risk & Mitigation

* **Bundle size:** adding `json-stable-stringify` (<3 KB gzipped) is acceptable.
* **Performance:** stable serialisation runs only once per render; cost negligible (<0.1 ms for typical variable size).
* **Edge cases:** sanitiser may inadvertently strip legitimate `undefined` meant for server defaults; reviewers must double-check variable requirements in schema.

---

## 5 Acceptance Criteria

- [ ] All unit & integration tests pass.
- [ ] `TimesheetRosterQuery` creates an entry in `__RELAY_OP_SAVED_AT__` on first mount.
- [ ] Subsequent visits after >30 s trigger exactly one background revalidation.
- [ ] No other queries regress (run existing SWR test-suite).

---

## 6 Timeline / Ownership

| Date | Task | Owner |
|------|------|-------|
| Aug 04 | Implement Steps A–D | @alice |
| Aug 05 | Write & fix tests (Step E) | @bob |
| Aug 06 | Code review / merge | Team |
| Aug 07 | QA on staging | QA |

---

## 7 Rollback Plan

Revert the feature branch; delete new timestamp-persistence hook; run `pnpm i --frozen-lockfile` to remove the additional package. Existing functionality will fall back to status quo ante (no background revalidation for roster query).

---

_End of plan_
