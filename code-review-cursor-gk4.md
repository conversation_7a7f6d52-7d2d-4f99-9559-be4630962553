# Code Review: Staged Changes (ui/src/relay/__tests__/selectivePersistence.test.ts)

**Reviewer:** <PERSON>ursor AI Assistant (Grok 4)
**Date:** [Current Date]
**Changes Reviewed:** Addition of comprehensive test suite for selective persistence, SWR behavior, per-operation timestamps, and phased improvements. Based on git diff --staged and re-reading of updated source files (e.g., useSWRQuery.ts now at 1055 lines).

This review focuses on low-level implementation correctness in the tests, potential issues, uncovered edge cases, and unintended consequences. No code changes are made; this is research-only. The tests appear well-structured and cover key scenarios, but some areas need more depth for robustness.

## Summary of Changes
- **File Modified:** ui/src/relay/__tests__/selectivePersistence.test.ts (added ~500 lines of tests).
- **Key Additions:**
  - Tests for persistence logic (security denylist, size filtering, reasons).
  - SWR query behavior (fresh/stale cache, debug delay, error handling).
  - Per-operation timestamps (TTL logic, persistence, backward compatibility).
  - Phased tests for SWR improvements (timestamps, edge preservation, hash logic, debug logging, integration).
- **Overall Quality:** Tests are comprehensive, use mocks effectively, and align with the updated implementation in useSWRQuery.ts (e.g., PHASE 1-5 features like snapshots, stable hash, dynamic connections). Good use of jest timers for async behavior. No accidental changes observed in the diff.

## Positive Aspects
- **Coverage:** Excellent coverage of core SWR flows (cache hit/miss, revalidation triggers, TTL boundaries). Phase-specific tests ensure incremental improvements are verified.
- **Edge Cases Handled:** Includes boundary tests (exactly 30s, 30.001s), error recovery (failed revalidation, missing timestamps), and backward compatibility (legacy blobs).
- **Implementation Alignment:** Tests match updated code, e.g.:
  - TTL logic tests reflect simplified condition in useSWRQuery.ts (line 693: `shouldTriggerBackgroundRevalidation = !isCacheFresh && availability.status !== 'missing'`).
  - Edge snapshot/merge tests verify PHASE 2/3 changes (snapshots, dynamic discovery).
  - Hash tests check stable fields omission (volatile like updatedAt).
- **Mocking:** Proper mocking of Relay internals, timers, and globals (e.g., __RELAY_OP_SAVED_AT__).
- **No Unintended Changes:** Diff only adds tests; no modifications to existing code. No syntax errors or lint issues apparent.

## Potential Issues
- **Mock Inconsistencies:** Some tests mock fetchQuery to resolve/reject, but don't verify if commitUpdate is called post-fetch. E.g., in 'should update per-operation timestamp after successful background revalidation', verify environment.commitUpdate was invoked.
- **Async Timing Assumptions:** Reliance on setTimeout(..., 10) or jest.runAllTimers() may flake in slower environments. Consider awaiting promises directly where possible.
- **Incomplete Assertions:** In some tests (e.g., 'flushNow should provide sendBeacon fallback'), assertions are missing or placeholder (expect(true).toBe(true)). Add verifies for sendBeacon call.
- **Global State Leakage:** Tests modify window globals without isolation; use jest.restoreAllMocks() or setup/teardown to prevent cross-test pollution.
- **Error Handling Gaps:** Tests cover happy paths well, but not all failure modes, e.g., commitUpdate throwing during merge.

## Uncovered Edge Cases
- **Concurrent Revalidations:** No test for multiple queries triggering revalidation simultaneously (race conditions in timestamp updates).
- **Hash Collisions:** Hash tests check stability but not collisions (e.g., different data hashing to same value, leading to incorrect merge).
- **Large Datasets:** Performance tests mentioned but not implemented (e.g., merging 1000+ edges; check for timeouts or memory issues).
- **Dynamic Discovery Failures:** Tests assume discovery works; add cases where store data is malformed or empty.
- **Persistence Quota Exceeded:** Integration tests don't simulate IndexedDB quota errors during timestamp save.
- **Reload Scenarios:** While persistence is tested, no e2e simulation of page reload with timestamp verification.

## Suggestions for Improvement
- **Add More Assertions:** For integration tests, assert on logs/mocks (e.g., debug.log calls) to verify instrumentation.
- **Parameterized Tests:** Use jest.each for TTL boundaries instead of separate tests.
- **Cleanup Globals:** Add afterEach to reset window.__RELAY_OP_SAVED_AT__.
- **Performance Assertions:** In large pagination tests, add expects for execution time < threshold.
- **PR Readiness:** Changes are accurate and low-risk (tests only). Approve after addressing missing assertions and adding concurrent/collision cases. No high-level architectural issues; low-level is solid but could be more exhaustive.

**Approval Recommendation:** Approve with minor revisions for completeness.

**End of Review**
