# Relay Cache Lookup Investigation Report (Windsurf)

**Date:** 2025-08-04  
**Author:** Cascade AI (Windsurf)  
**Related Ticket:** `P0-Relay-Cache-Miss`  
**Codebase SHA:** _(current local)_

---

## 1. Executive Summary
Re<PERSON>’s cache **persistence is perfect**, but **lookup always fails** because the **Relay root record (`client:root`) is deliberately filtered-out during persistence**.  
Without this root record, <PERSON><PERSON> has no entry point to reach the persisted entity records, so every query resolves as a cache-miss and goes to the network, causing the offline failure.

---

## 2. Detailed Findings

| # | Question from Prompt | Finding |
|---|----------------------|---------|
| 1 | **Root Cause – why cache miss?** | `client:root` is excluded in `shouldPersistRecordWithReason` (see `ui/src/relay/cacheFilters.ts`, case **(a)**). This removes all root-level field links (e.g. `timesheets({...}) → <TimeSheetConnection-ID>`). At query evaluation <PERSON><PERSON> first looks for the field on `client:root`; not finding it, it stops and marks the query `MISS` even though the child records exist. |
| 2 | **Store Integration – why invisible?** | `createPersistedStore()` successfully hydrates `RecordSource` with 6 k+ records, but since root links are absent the records are **unreachable** through normal selectors. Dev tools that iterate the full store still “see” them – hence the misleading 100 % hit analysis.
| 3 | **Cache Key Analysis – mismatch?** | Keys of individual entities match, but the *lookup path* (`client:root → field(args) → entity-ids`) is broken. Relay therefore never compares the entity keys; it fails immediately on the missing root field.
| 4 | **Fetch Policy – why acts network-only?** | `store-or-network` first tries the store. Because the selector for the operation cannot be fulfilled it falls back to network – behaviour identical to `network-only`.
| 5 | **Offline Failure** | With backend down there is no network fallback, so UI shows empty state/ errors. The data is present but unreachable.

### Code Evidence
```ts
// ui/src/relay/cacheFilters.ts
// (excerpt)
if (dataID === 'client:root') {
  return [false, 'denied_client_root']; // <- Excludes the vital root record
}
```

Debug tooling focuses on business entity records and never checks that `client:root` survived persistence, leading to false “100 % hit” reports.

---

## 3. Specific Fix
1. **Persist `client:root`** while still protecting sensitive sub-fields.
2. Recommended minimal change:
   ```ts
   // cacheFilters.ts
   if (dataID === 'client:root') {
     // Strip auth/session fields but keep query links
     const sanitized = { ...record } as any;
     delete sanitized.userInfo;           // contains PII
     delete sanitized.authToken;          // etc.
     return [true, 'allowed_default', sanitized];
   }
   ```
3. Optionally introduce an explicit **allowlist of safe root fields** instead of delete-after-persist.
4. Run `yarn vitest relay/…` (unit tests exist under `__tests__`) – they should now pass in offline-mode.

---

## 4. Implementation Guide
1. Update `shouldPersistRecordWithReason` in `cacheFilters.ts` as above.  
2. Bump `CACHE_VERSION` in `createPersistedStore.ts` to invalidate existing bad caches.  
3. Ship to staging → open the app once online → verify one successful network fetch + persistence.  
4. Go offline → reload page → queries should resolve from cache (check console: `fromCache: true`).

---

## 5. Validation Plan
* **Automated**: Add a Jest/Vitest that
  1. Creates a store with a root record + linked entity.
  2. Persists & reloads via `createPersistedStore()`.
  3. Executes `Environment.lookup(selector)` and asserts data is returned.
* **Manual**:
  1. Enable dev tool `window.__enableCacheDelay()`.
  2. Load `TimesheetRoster` once online (should hit network).
  3. Reload within 10 s – should render instantly (`fromCache:true`).
  4. Kill backend → hard-reload page → data should still render from cache.

---

## 6. Prevention Strategy
* **Unit-test** persistence filter to ensure `client:root` survives.
* Extend debug tool `analyzeCacheMissDeep()` to explicitly verify presence of root record & field payloads.
* Document rule: *Never exclude `client:root`; instead sanitise sensitive fields.*

---

> **Conclusion:** The cache contains all entity records, but by removing the root record we severed Relay’s only pointer to them. Restoring a sanitized `client:root` solves the cache-miss, fetch-policy, and offline-mode issues.
