# Code Review – Timesheet Roster SWR Patch 2025-08-05

_Reviewer: Windsurf AI_

This review focuses on the staged diff (`git diff --staged`) across five files:

* `ui/src/relay/createPersistedStore.ts`
* `ui/src/relay/cacheFilters.ts`
* `ui/src/relay/useSWRQuery.ts`
* `ui/src/relay/debug.ts`
* `ui/src/relay/__tests__/selectivePersistence.test.ts`

The high-level direction (edge snapshot + immediate persistence) looks sound, so the notes below concentrate on low-level correctness, edge-cases and maintainability.

---

## 1 createPersistedStore.ts

### ✅ Strengths
* Introduces `flushNow()` / `flushNowCallback` for synchronous writes — addresses missing-timestamp bug.
* `beforeunload` now calls `flushNowImpl`, reducing debounce-window loss.
* Added `navigator.sendBeacon` fallback for rare IndexedDB failures.

### ⚠️ Concerns / Suggestions
| Line(s) | Issue |
|---------|-------|
| `notifyStoreUpdated()` | If the function is imported **before** the first call to `createPersistedStore()`, both callbacks are still `null` ⇒ no persistence. Consider exporting a tiny `initPersistence()` that guarantees store creation before hooks are used, or at least log a warning when neither callback is set. |
| `flushNow()` | Relies on `window`/`navigator` making it **browser-only**. OK for app, but Jest environment may need `jsdom`. Ensure unit tests set `global.navigator.sendBeacon` stub or guard behind `typeof navigator?.sendBeacon`. |
| `persist()` | Signature unchanged but `existingEdgeMap` variable leaks from previous block (nothing critical). |
| `flushNowImpl` | Inside `createPersistedStore` but used externally via closure; good, but document that callers **must** await the returned `Promise` if they depend on completion (none do currently). |

### ⏳ Performance
* `source.toJSON()` is called twice (debounced + immediate) and can be heavy for large stores. Consider snapshot re-use or throttling for very large payloads.

---

## 2 cacheFilters.ts – `mergeConnectionEdges`

### ✅ Strengths
* Backwards-compatible: new `existingEdgesSnapshot` parameter is optional, old call-sites still compile.
* Debug output now indicates whether snapshot was used.

### ⚠️ Concerns / Suggestions
* **Param ordering**: `existingEdgesSnapshot` is the 4th arg, but in `useSWRQuery` it is passed as 4th. Existing external callers (if any) might pass 3 args. Good.
* `store` parameter is **still unused**; consider removing or using for future GC logic.

---

## 3 useSWRQuery.ts

### ✅ Strengths
* Simplified TTL guard (`isCacheFresh`) – easier to reason about.
* Captures edge snapshots before network publish and merges regardless of hash miss.
* Hash now excludes volatile fields (`updatedAt`, etc.) and includes stable business fields.
* Extensive debug logging hooks added.

### 🟥 Critical Issues
| Line(s) | Problem | Impact / Fix |
|---------|---------|--------------|
| 342-415 | Snapshot capture uses
`const rootRecord = source.get('client:root');` then passes it to `ConnectionHandler.getConnection(rootRecord, ...)`. `rootRecord` is **not** a `RecordProxy`; `ConnectionHandler.getConnection` expects a `RecordProxy` obtained inside `commitUpdate`. This will return `null` or throw. | Obtain snapshots inside `environment.commitUpdate()` before the publish, or call `environment.lookup` + selector to read edges; alternatively use `store.getSource().get()` and manually traverse.
| 358-374 | `source.toJSON()` for reflection runs **on every background revalidation**, potentially large (MBs). | Cache result or limit to dev mode.

### ⚠️ Other Notes
* Debug enums changed (`network_first_policy` → `forced_network_due_to_missing_timestamp`). Ensure every other reference is updated (grep repo).
* `stableBusinessFields` inline list duplicates domain knowledge; extracting to shared constant would DRY.
* `isRecordProxy` type-guard is too permissive (`object` check only). Safe at runtime but defeats TS benefits.
* New map `existingEdgesSnapshots` is built even if later not used (when background call fails). Consider clearing to free memory.

---

## 4 debug.ts

### ✅ Strengths
* New structured SWR logging helpers – will aid troubleshooting.
* Renamed cache-miss reason improves clarity.

### ⚠️ Concerns
* Added union members to `CacheMissReason` but did **not** update type usages elsewhere (apart from `useSWRQuery`). Run `tsc` to catch missing switches / exhaustive checks.
* Ensure log helpers are tree-shaken out of prod bundle (they guard `NODE_ENV === 'development'`, good).

---

## 5 selectivePersistence.test.ts

### ✅ Strengths
* Adds coverage for stable-field hashing, connection discovery, debug reason changes and full SWR workflow.

### ⚠️ Concerns
* Tests are heavy on mock data but do **not** assert integration with actual Relay environment. They serve more as documentation; consider marking as `integration` or splitting into smaller unit tests.
* `process.env.NODE_ENV` mutation in tests can leak; restore in `afterEach` (done) but watch for parallel test interference.

---

## 6 Edge Cases Not Yet Covered
1. **Store not yet hydrated**: If `notifyStoreUpdated()` fires before store creation (rare but possible in SSR), timestamps are lost.
2. **Failed snapshot merge**: `mergeConnectionEdges` tries-catch but still sets `connection.setLinkedRecords(finalEdges, "edges")`; on error it falls back silently, but hash table gets updated. This can mask bugs.
3. **sendBeacon 404**: Fallback URL `/relay-cache-backup` might generate server error noise; consider pointing to `about:blank` or configurable endpoint.
4. **Memory pressure**: For large edge sets (>1000 edges), snapshot + merge duplicates arrays (existing + new) causing peak memory 2×. Monitor.

---

## 7 Recommendation
Overall the patch meaningfully improves SWR correctness. Address the **critical snapshot capture bug** before merging; once fixed, run `tsc --noEmit` and the Jest suite. The remainder are polish / follow-ups.

**PR Status**: _Changes Requested_

---

© 2025 Windsurf Engineering
