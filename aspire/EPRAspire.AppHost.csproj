﻿<Project Sdk="Microsoft.NET.Sdk">

  <Sdk Name="Aspire.AppHost.Sdk" Version="9.0.0" />

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsAspireHost>true</IsAspireHost>
    <UserSecretsId>40058590-3ec6-4647-858f-b86d7c979121</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Hosting.AppHost" Version="9.3.0" />
    <PackageReference Include="Aspire.Hosting.Kafka" Version="9.3.0" />
	<PackageReference Include="Aspire.Hosting.SqlServer" Version="9.3.0" />
    <PackageReference Include="Aspire.Hosting.NodeJs" Version="9.3.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\backend\backend.csproj" />
    <ProjectReference Include="..\identity\EPRIdentity.Web.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </None>
  </ItemGroup>

   <!-- Install npm packages if node_modules is missing -->
   <!-- 
  <Target Name="RestoreNpm" BeforeTargets="Build" Condition=" '$(DesignTimeBuild)' != 'true' ">
   <ItemGroup>
    <PackageJsons Include="..\*\package.json" />
   </ItemGroup>

   <Message Importance="Normal" Text="Installing npm packages for %(PackageJsons.RelativeDir)" Condition="!Exists('%(PackageJsons.RootDir)%(PackageJsons.Directory)/node_modules')" />
   <Exec Command="npm install" WorkingDirectory="%(PackageJsons.RootDir)%(PackageJsons.Directory)" Condition="!Exists('%(PackageJsons.RootDir)%(PackageJsons.Directory)/node_modules')" />	  
  </Target>
   -->

</Project>
