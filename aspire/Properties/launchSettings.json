{"profiles": {"Development": {"commandName": "Project", "launchBrowser": false, "dotnetRunMessages": true, "applicationUrl": "http://localhost:15176", "environmentVariables": {"DOTNET_ENVIRONMENT": "Development", "DOTNET_DASHBOARD_OTLP_ENDPOINT_URL": "http://localhost:19011", "DOTNET_RESOURCE_SERVICE_ENDPOINT_URL": "http://localhost:20192", "ASPIRE_ALLOW_UNSECURED_TRANSPORT": "true"}}, "Staging": {"commandName": "Project", "launchBrowser": false, "dotnetRunMessages": true, "applicationUrl": "http://localhost:15176", "environmentVariables": {"DOTNET_ENVIRONMENT": "Staging", "DOTNET_DASHBOARD_OTLP_ENDPOINT_URL": "http://localhost:19011", "DOTNET_RESOURCE_SERVICE_ENDPOINT_URL": "http://localhost:20192", "ASPIRE_ALLOW_UNSECURED_TRANSPORT": "true"}}, "Production": {"commandName": "Project", "launchBrowser": false, "dotnetRunMessages": true, "applicationUrl": "http://localhost:15176", "environmentVariables": {"DOTNET_ENVIRONMENT": "Production", "DOTNET_DASHBOARD_OTLP_ENDPOINT_URL": "http://localhost:19011", "DOTNET_RESOURCE_SERVICE_ENDPOINT_URL": "http://localhost:20192", "ASPIRE_ALLOW_UNSECURED_TRANSPORT": "true"}}}}