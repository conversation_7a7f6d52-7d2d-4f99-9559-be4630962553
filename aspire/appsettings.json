{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Aspire.Hosting.Dcp": "Warning"}, "EnableFileLogging": true}, "AllowedHosts": "*", "ConnectionStrings": {"EPRIdentityConnection": "Server=localhost;Initial Catalog=EPRIdentity;User ID=devuser;Password=**********;Integrated Security=false;Trusted_Connection=false;MultipleActiveResultSets=true;TrustServerCertificate=True;", "EPRLiveConnection": "Server=localhost;Initial Catalog=jul29;User ID=devuser;Password=**********;Integrated Security=false;Trusted_Connection=false;MultipleActiveResultSets=true;TrustServerCertificate=True;"}, "TrustedOrigins": ["http://localhost:3001", "http://localhost:9090", "http://localhost:5173", "http://localhost:*", "https://*.eprlive.com"], "Identity": {"Server": "http://localhost:5211", "ClientId": "8AE1C309-DBC3-4D24-B1AC-5BC49534DC58", "ClientSecret": "7842AD54-6486-4661-9406-B524619EA439"}, "MailSettings": {"Mail": "<EMAIL>", "DisplayName": "EPRLive", "Password": "", "Host": "ubuntu1.localdomain", "Port": 25}, "Kafka": {"GroupId": "EPRIdentity", "BootstrapServers": "localhost:29092", "Topic": "EPRLive", "Consumer": {"BootstrapServers": "localhost:29092", "GroupId": "EPRIdentity", "DefaultTopic": "EPRLive", "SocketTimeoutMs": 30000, "ConnectionsMaxIdleMs": 60000, "SessionTimeoutMs": 45000, "ReconnectBackoffMs": 1000, "ReconnectBackoffMaxMs": 60000, "TopicMetadataRefreshIntervalMs": 300000, "EnableAutoOffsetStore": false, "AllowAutoCreateTopics": true, "AutoOffsetReset": "Earliest", "MaxSubscriptionRetries": 5, "SubscriptionRetryInitialDelayMs": 1000, "MaxConnectionRetriesIdentity": 10, "InitialConnectionRetryDelayMsIdentity": 5000, "DLQTopicName": "EPRLive-DLQ", "DLQMaxRetries": 3, "DLQEnabled": true}}}