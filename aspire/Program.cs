using Aspire.Hosting;
using Microsoft.Extensions.Hosting;

var builder = DistributedApplication.CreateBuilder(args);
// IResourceBuilder<KafkaServerResource> kafka;
IResourceBuilder<ProjectResource> eprBackend;
IResourceBuilder<ProjectResource> eprIdentity;

/*
if (builder.Environment.IsDevelopment())
{
    var traefik = builder
        .AddContainer("traefik", "traefik")
        .WithContainerName("traefik")
        .WithVolume("/var/run/docker.sock:/var/run/docker.sock")
        .WithBindMount($"../traefik", "/etc/traefik")
        .WithHttpEndpoint(port: 8080, targetPort: 8080, name: "dashboard")
        .WithHttpEndpoint(port: 9090, targetPort: 9090, name: "web")
        .WithLifetime(ContainerLifetime.Persistent);

    kafka = builder
        .AddKafka(name: "kafka", port: 9092)
        .WithContainerName("kafka")
        .WithKafkaUI(ui =>
            ui.WithHostPort(9100)
                .WithContainerName("kafka-ui")
                .WithLifetime(ContainerLifetime.Persistent)
        )
        .WithLifetime(ContainerLifetime.Persistent);

    eprIdentity = builder
        .AddProject<Projects.EPRIdentity_Web>("epridentity")
        .WaitFor(kafka)
        .WithExternalHttpEndpoints();

    eprBackend = builder
        .AddProject<Projects.backend>("eprbackend")
        .WaitFor(kafka)
        .WithExternalHttpEndpoints();
}
else
{
*/
eprIdentity = builder
    .AddProject<Projects.EPRIdentity_Web>("epridentity")
    .WithExternalHttpEndpoints();
eprBackend = builder.AddProject<Projects.backend>("eprbackend").WithExternalHttpEndpoints();

// }

var script = args.FirstOrDefault(arg => arg.StartsWith("script="))?.Split('=').LastOrDefault();

if (script == null)
{
    if (builder.Environment.IsDevelopment())
    {
        script = "dev";
    }
    else if (builder.Environment.IsStaging())
    {
        script = "prod-start";
    }
    else if (builder.Environment.IsProduction())
    {
        script = "prod-start";
    }
}

/*
Console.WriteLine($"eprlive-ui npm script: {script}");

builder
    .AddNpmApp("eprlive24", "../ui", scriptName: script)
    .WaitFor(eprBackend)
    .WithReference(eprBackend)
    .WithEnvironment("BROWSER", "none") // Disable opening browser on npm start
    .WithHttpEndpoint(port: 3000, targetPort: 3001)
    .WithExternalHttpEndpoints()
    .PublishAsDockerFile();
    */

builder.Build().Run();
