{"$schema": "https://json.schemastore.org/aspire-8.0.json", "resources": {"epridentity": {"type": "project.v0", "path": "../identity/EPRIdentity.Web.csproj", "env": {"OTEL_DOTNET_EXPERIMENTAL_OTLP_EMIT_EXCEPTION_LOG_ATTRIBUTES": "true", "OTEL_DOTNET_EXPERIMENTAL_OTLP_EMIT_EVENT_LOG_ATTRIBUTES": "true", "OTEL_DOTNET_EXPERIMENTAL_OTLP_RETRY": "in_memory", "ASPNETCORE_FORWARDEDHEADERS_ENABLED": "true", "HTTP_PORTS": "{epridentity.bindings.http.targetPort}"}, "bindings": {"http": {"scheme": "http", "protocol": "tcp", "transport": "http", "external": true}, "https": {"scheme": "https", "protocol": "tcp", "transport": "http", "external": true}}}, "eprbackend": {"type": "project.v0", "path": "../backend/backend.csproj", "env": {"OTEL_DOTNET_EXPERIMENTAL_OTLP_EMIT_EXCEPTION_LOG_ATTRIBUTES": "true", "OTEL_DOTNET_EXPERIMENTAL_OTLP_EMIT_EVENT_LOG_ATTRIBUTES": "true", "OTEL_DOTNET_EXPERIMENTAL_OTLP_RETRY": "in_memory", "ASPNETCORE_FORWARDEDHEADERS_ENABLED": "true", "HTTP_PORTS": "{eprbackend.bindings.http.targetPort}"}, "bindings": {"http": {"scheme": "http", "protocol": "tcp", "transport": "http", "external": true}, "https": {"scheme": "https", "protocol": "tcp", "transport": "http", "external": true}}}, "eprlive24": {"type": "dockerfile.v0", "path": "../ui/Dockerfile", "context": "../ui", "env": {"NODE_ENV": "development", "services__eprbackend__http__0": "{eprbackend.bindings.http.url}", "services__eprbackend__https__0": "{eprbackend.bindings.https.url}", "BROWSER": "none"}, "bindings": {"http": {"scheme": "http", "protocol": "tcp", "transport": "http", "port": 3000, "targetPort": 3001, "external": true}}}}}