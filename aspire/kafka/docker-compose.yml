services:
  kafka:
    image: bitnami/kafka:latest
    container_name: kafka
    # We do NOT set hostname: localhost
    ports:
      - "9092:9092"     # mapped so other containers can see it at 'kafka:9092'
      - "29092:29092"   # mapped for external traffic from host at localhost:29092
    environment:
      - KAFKA_ENABLE_KRAFT=yes
      - KAFKA_CFG_PROCESS_ROLES=controller,broker
      - KAFKA_CFG_NODE_ID=1
      - KAFKA_CFG_CONTROLLER_QUORUM_VOTERS=1@kafka:9093
      
      # We define 3 listeners:
      # 1) INTERNAL on :9092
      # 2) CONTROLLER on :9093
      # 3) EXTERNAL on :29092
      - KAFKA_CFG_LISTENERS=INTERNAL://:9092,CONTROLLER://:9093,EXTERNAL://:29092
      
      # Advertise them differently for each context:
      #  - INTERNAL -> "kafka:9092" (for docker containers)
      #  - CONTROLLER -> "kafka:9093"
      #  - EXTERNAL -> "localhost:29092" (for host apps)
      - <PERSON><PERSON><PERSON>_CFG_ADVERTISED_LISTENERS=INTERNAL://kafka:9092,CONTROLLER://kafka:9093,EXTERNAL://localhost:29092
      
      - KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=INTERNAL:PLAINTEXT,CONTROLLER:PLAINTEXT,EXTERNAL:PLAINTEXT
      - KAFKA_CFG_INTER_BROKER_LISTENER_NAME=INTERNAL
      - KAFKA_CFG_CONTROLLER_LISTENER_NAMES=CONTROLLER
      - ALLOW_PLAINTEXT_LISTENER=yes
      
    volumes:
      - kafka_data:/bitnami/kafka
    networks:
      - kafka_network
    healthcheck:
      test: ["CMD-SHELL", "kafka-broker-api-versions.sh --bootstrap-server kafka:9092"]
      interval: 10s
      timeout: 5s
      retries: 6

  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: kafka-ui
    ports:
      - "9080:8080"
    environment:
      # kafka-ui should connect via the INTERNAL listener address
      - KAFKA_CLUSTERS_0_NAME=local
      - KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS=kafka:9092
    depends_on:
      kafka:
        condition: service_healthy
    networks:
      - kafka_network

volumes:
  kafka_data:
    driver: local

networks:
  kafka_network:
    driver: bridge
