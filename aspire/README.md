## Aspire - AppHost project to orchestrate backend, frontend and other services.

EPRAspire is an orchestration project for the applications that can be deployed in the cloud, also makes it easy to run the applications in the local environment.

Aspire.AppHost project creates all resources required for the applications to run like Kafka, Traefik, Project Containers, etc.


## Run the application

    Development mode:

        dotnet watch run
        
        or 
        
        dotnet watch run --launch-profile Development

    QA/Staging mode:

        dotnet run --launch-profile Staging

    Production mode:

        dotnet run --launch-profile Production

