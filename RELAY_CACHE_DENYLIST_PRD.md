# Product Requirements Document: Relay Cache Denylist Architecture

**Document Version**: 1.0
**Date**: 2025-08-04
**Status**: Draft
**Author**: <PERSON> Assistant
**Stakeholders**: Frontend Team, Security Team, DevOps

---

## Executive Summary

### Problem Statement
The current Relay selective cache persistence system relies on a large **allow-list plus a small deny-list**. Because the valid GraphQL object space is huge (hundreds of types and thousands of `__client` records), keeping the allow-list accurate is effectively impossible. Real-world browser logs (see Appendix C) show that 72 % of records are still denied as “unknown”, including legitimate business entities such as `Employer`, `ChaptersInfoDto`, and connection edges. This results in repeat network requests and slow page loads.

### Proposed Solution
Migrate from allowlist-based filtering to a **security-focused denylist approach** that explicitly excludes only sensitive data (authentication, authorization, errors, PII) while caching all other business data by default.

### Expected Impact
- **Cache hit rate improvement**: 27.6% → 85-90%
- **Network request reduction**: ~70% fewer API calls
- **Maintenance reduction**: ~90% less filter maintenance overhead
- **Developer experience**: New GraphQL types work automatically
- **Page load performance**: 2-3x faster for cached scenarios

---

## Product Overview

### Current Architecture Problems
1. **Unbounded maintenance overhead** – The allow-list would need to enumerate *every* valid object type and client record, which is not feasible in practice
2. **Schema dependency** - Automated allowlist generation frequently fails or misses entities
3. **Poor performance** - 72.4% of business data incorrectly denied as "unknown"
4. **Developer friction** - New features require cache filter updates
5. **Brittle automation** - Schema introspection scripts prone to failure

### Target Architecture Benefits
1. **Security-first approach** - Explicit denial of sensitive data only
2. **Default inclusion** - All business data cached unless specifically dangerous
3. **Maintenance simplicity** - Static security patterns rarely change
4. **Future-proof design** - New GraphQL types automatically supported
5. **Performance optimization** - Maximum cache utilization while maintaining security

---

## Requirements

### Functional Requirements

#### FR-1: Security Data Exclusion
**Priority**: P0 (Critical)
**Description**: System MUST deny caching of authentication, authorization, and error data

**Acceptance Criteria**:
- [ ] User authentication tokens never persisted
- [ ] User session data never persisted
- [ ] Error messages and error states never persisted
- [ ] PII data patterns explicitly denied
- [ ] Authorization state never persisted
- [ ] Temporary upload/form states never persisted

**Security Patterns to Deny**:
```javascript
// Authentication & Authorization
/^client:root:userInfo$/
/^UserInfoOutput:/
/^client:root:currentUser/
/^client:root:isAuthenticated$/
/^client:root:auth(Token|State|Error)/

// Errors & System State
/__typename.*Error$/
/^client:root:(error|networkError|authError)/
/^client:root:(loading|submitting|validating)/

// PII Patterns (configurable)
/SSN|SocialSecurity/
/password|creditCard|bankAccount/i

// Temporary Data
/^client:root:(upload|temp|pending)/
```

#### FR-2: Business Data Inclusion
**Priority**: P0 (Critical)
**Description**: System MUST cache all business entities and operational data by default

**Acceptance Criteria**:
- [ ] All GraphQL business types cached (Employer, TimeSheet, etc.)
- [ ] Connection and pagination data cached
- [ ] Static reference data cached (Chapters, Categories, etc.)
- [ ] GraphQL introspection metadata cached if needed for hydration
- [ ] UI preferences and settings cached
- [ ] Custom views and user configurations cached

#### FR-3: Performance Optimization
**Priority**: P1 (High)
**Description**: System SHOULD maintain performance guardrails while maximizing cache utilization

**Acceptance Criteria**:
- [ ] Cache persistence rate >80%
- [ ] Total cache size monitoring and alerting
- [ ] Connection edge trimming preserved (configurable limit)
- [ ] Size-based exclusions for exceptionally large records (>100KB)
- [ ] Cache cleanup and TTL management maintained


### Non-Functional Requirements

#### NFR-1: Security Compliance
**Priority**: P0 (Critical)
- **Data Protection**: No PII, authentication, or sensitive data in persistent cache
- **Audit Trail**: Clear logging of what data is excluded and why
- **Access Control**: Denylist patterns secured from tampering
- **Compliance**: Meet GDPR, CCPA, SOX requirements for data handling

#### NFR-2: Performance Standards
**Priority**: P0 (Critical)
- **Cache Hit Rate**: >80% for business queries
- **Page Load Time**: <2s for cached scenarios
- **Network Requests**: <30% of current volume for repeat visits
- **Storage Efficiency**: <10MB total cache size per user
- **Memory Usage**: No memory leaks from enhanced caching

#### NFR-3: Maintainability
**Priority**: P1 (High)
- **Pattern Simplicity**: <10 security denial patterns total
- **Documentation**: Comprehensive security pattern documentation
- **Testing**: Automated tests for all security exclusions
- **Monitoring**: Real-time alerts for security pattern violations
- **Schema Evolution**: Process for identifying new sensitive patterns

#### NFR-4: Developer Experience
**Priority**: P1 (High)
- **Zero Configuration**: New GraphQL types work automatically
- **Debug Tools**: Enhanced debugging for cache behavior
- **Clear Errors**: Actionable error messages for cache issues
- **Documentation**: Migration guides and troubleshooting docs

---

## Technical Architecture

### Current State Analysis
```javascript
// Current hybrid filter complexity (~200 allow-list lines + ~20 deny-list lines)
const BUSINESS_ENTITY_TYPES = [/* 20+ manually maintained types */];
const ALLOWED_DATAID_PATTERNS = [/* 17+ regex patterns */];
const SIZE_LIMITS = {/* per-type size configurations */};
// Plus schema introspection scripts, generators, CI validation
```

**Problems**:
- 72.4% denial rate (1054/1456 records denied as "unknown")
- Manual maintenance **required** for every schema or feature change
- Brittle automation frequently breaks
- Poor developer experience with cryptic cache misses

### Target State Design
```javascript
// Proposed denylist simplicity (~20 lines)
const SECURITY_DENYLIST = [
  // Authentication (5 patterns)
  /^client:root:userInfo$/,
  /^UserInfoOutput:/,
  /^client:root:(auth|session)/,

  // Errors (3 patterns)
  /__typename.*Error$/,
  /^client:root:(error|loading)/,

  // PII (2 patterns, configurable)
  /SSN|password|creditCard/i,
];

function shouldPersistRecord(dataID, record) {
  // Security check (hard deny)
  if (SECURITY_DENYLIST.some(pattern => pattern.test(dataID))) {
    return { persist: false, reason: 'security_denied' };
  }

  // Size check (performance optimization)
  if (getRecordSize(record) > MAX_RECORD_SIZE) {
    return { persist: false, reason: 'size_exceeded' };
  }

  // Default: cache everything else
  return { persist: true, reason: 'allowed_default' };
}
```

**Benefits**:
- ~90% persistence rate (only security/size exclusions)
- Zero maintenance for new GraphQL types
- Simple, auditable security patterns
- Excellent developer experience

### Deployment Strategy (Direct Denylist Adoption)

The denylist architecture will be deployed directly in a single release cycle. There will be no hybrid mode, phased migration, or rollback feature flags. All allowlist logic will be removed in this release.





---

## Success Metrics

### Performance KPIs
| Metric | Current | Target | Measurement |
|--------|---------|--------|-------------|
| Cache Persistence Rate | 27.6% | >85% | Debug logs analysis |
| Page Load Time (cached) | 3-5s | <2s | Browser performance API |
| Network Request Volume | 100% | <30% | Network tab analysis |
| Cache Hit Rate | ~30% | >80% | Relay dev tools |
| Unknown Record Rate | 72.4% | <10% | Persistence statistics |

### Operational KPIs
| Metric | Current | Target | Measurement |
|--------|---------|--------|-------------|
| Cache Filter Updates/Month | 5-10 | <1 | Git commit analysis |
| Schema Change Impact | High | Minimal | Deployment success rate |
| Developer Support Tickets | 10-15/month | <3/month | Support system |
| Cache-related Bugs | 8-12/month | <2/month | Bug tracking system |

### Security KPIs
| Metric | Target | Measurement |
|--------|--------|-------------|
| PII Data in Cache | 0% | Automated security scans |
| Auth Token Persistence | 0% | Security audit tests |
| Error Message Leaks | 0% | Privacy compliance review |
| Security Pattern Coverage | 100% | Automated pattern testing |

---

## Implementation Plan

### Development Phases

#### Phase 1: Foundation (Week 1)
**Deliverables**:
- [ ] Design denylist architecture and patterns
- [ ] Create security pattern definitions
- [ ] Implement denylist filtering function


**Risk Mitigation**:

- Comprehensive test coverage for security patterns
- Security team review of denial patterns

#### Phase 2: Implementation (Week 2)
**Deliverables**:
- [ ] Implement denylist filtering logic
- [ ] Enhance debug tools for new approach
- [ ] Create performance monitoring
- [ ] Update documentation
- [ ] Security audit and penetration testing

**Risk Mitigation**:
- Direct implementation without hybrid system
- Extensive security testing in staging
- Performance benchmarking tools

#### Phase 3: Testing & Validation (Week 3-4)
**Deliverables**:

- [ ] Performance comparison analysis
- [ ] Security validation and audit
- [ ] Bug fixes and optimizations
- [ ] Stakeholder review and approval

**Risk Mitigation**:
- Gradual rollout with monitoring

- Security incident response plan

#### Phase 4: Deployment (Week 5-6)
**Deliverables**:
- [ ] Production deployment
- [ ] Full migration to denylist approach
- [ ] Remove allowlist complexity
- [ ] Performance monitoring setup
- [ ] Team training and documentation

**Risk Mitigation**:
- Blue-green deployment strategy
- Real-time monitoring and alerting
- Support team readiness

#### Phase 5: Optimization (Week 7-8)
**Deliverables**:
- [ ] Performance optimization based on real data
- [ ] Denylist pattern refinements
- [ ] Long-term monitoring setup
- [ ] Process documentation for maintenance
- [ ] Success metrics reporting

---

## Risk Assessment

### High Risk Items

#### R-1: Security Data Leakage
**Risk**: Sensitive data accidentally cached due to incomplete denylist patterns
**Impact**: High - Privacy violations, compliance issues
**Probability**: Medium
**Mitigation**:
- Comprehensive security audit before deployment
- Automated security scanning of cached data
- Security team review of all denylist patterns
- Regular penetration testing

#### R-2: Performance Regression
**Risk**: Larger cache size causes performance issues
**Impact**: Medium - Slower app performance
**Probability**: Low
**Mitigation**:
- Gradual rollout with performance monitoring
- Size limits and cleanup mechanisms preserved
- Performance benchmarking to validate improvements


#### R-3: Cache Invalidation Issues
**Risk**: More cached data harder to invalidate properly
**Impact**: Medium - Stale data display
**Probability**: Medium
**Mitigation**:
- Preserve existing TTL and invalidation logic
- Enhanced cache versioning and cleanup
- Testing framework for cache invalidation scenarios



---

## Dependencies

### Technical Dependencies
- **Relay Environment**: Requires stable Relay environment initialization
- **IndexedDB**: Browser IndexedDB support and quota management
- **TypeScript**: Strict mode compliance for type safety
- **Feature Flags**: Environment variable support for controlled rollout

### Team Dependencies
- **Security Team**: Review of denylist patterns and security audit
- **Backend Team**: GraphQL schema stability during migration
- **DevOps Team**: Deployment pipeline and monitoring setup
- **QA Team**: Testing framework and validation procedures

### External Dependencies
- **Browser Support**: IndexedDB and Web Locks API availability
- **Compliance**: GDPR, CCPA, SOX requirements validation
- **Performance Tools**: Monitoring and alerting infrastructure

---

## Success Criteria

### Launch Readiness Checklist
- [ ] **Security Audit Passed**: All denylist patterns validated by security team
- [ ] **Performance Benchmarks Met**: >80% cache hit rate, <2s page loads

- [ ] **Documentation Updated**: All guides, troubleshooting, and processes documented
- [ ] **Team Training Complete**: All developers trained on new approach
- [ ] **Monitoring Deployed**: Real-time alerts and dashboards operational

### Go-Live Criteria
- [ ] **Zero Security Violations**: No PII or auth data in cached samples
- [ ] **Performance Improvement**: Measurable improvement in page load times
- [ ] **Cache Efficiency**: >85% persistence rate achieved
- [ ] **System Stability**: No cache-related errors or crashes


### Post-Launch Success Metrics (30 days)
- [ ] **User Experience**: Page load times improved by >50%
- [ ] **Network Efficiency**: API call volume reduced by >60%
- [ ] **Maintenance Overhead**: Cache filter updates reduced by >90%
- [ ] **Developer Satisfaction**: Positive feedback on new approach
- [ ] **Security Compliance**: Zero security incidents related to cache

---

## Appendices

### Appendix A: Current Performance Analysis
```
Cache Statistics (Current Allowlist Approach):
- Total Records Processed: 1456
- Records Persisted: 402 (27.6%)
- Records Denied: 1054 (72.4%)
  - denied_unknown_record: 916 (62.9%)
  - denied_size_exceeded: 3 (0.2%)
  - denied_blacklist_pattern: 1 (0.1%)
  - denied_client_root: 1 (0.1%)
  - denied_error_indicators: 0 (0.0%)

Performance Impact:
- Multiple identical queries due to cache misses
- High network request volume
- Poor user experience with loading spinners
- Developer frustration with maintenance overhead
```

### Appendix B: Security Pattern Analysis
```javascript
// Proposed Security Denylist Patterns
const SECURITY_DENYLIST = [
  // Authentication & Session (Critical)
  /^client:root:userInfo$/,
  /^UserInfoOutput:/,
  /^client:root:currentUser/,
  /^client:root:isAuthenticated$/,
  /^client:root:auth(Token|State|Error|Session)/,

  // System Errors (Critical)
  /__typename.*Error$/,
  /^client:root:(error|networkError|authError)/,
  /Error:(.*)/,

  // Transient State (Important)
  /^client:root:(loading|submitting|validating|pending)/,
  /^client:root:(upload|temp|draft)/,

  // PII Patterns (Configurable)
  /SSN|SocialSecurity/,
  /password|pwd|creditCard|bankAccount/i,
  /personalInfo|privateData/i,
];

// Expected Impact: ~95% of current "unknown" records would be allowed
// Security Coverage: 100% of sensitive data patterns covered
```

### Appendix C: Browser Log Evidence (2025-08-04)
```
Excerpt – unknown record denials
client:root:fieldDefinitions:0
client:root:chapters(...)
Unknown typename 'ChaptersInfoDto' …
Unknown typename 'Employer' …
...
```
These snippets corroborate that a large number of legitimate business records are currently denied because they are not enumerated in the allow-list.

---

**Document Control**:
- **Version**: 1.0
- **Last Updated**: 2025-08-04
- **Next Review**: 2025-08-11
- **Approval Required**: Security Team, Frontend Lead, Engineering Manager
- **Distribution**: Frontend Team, Security Team, DevOps, QA
