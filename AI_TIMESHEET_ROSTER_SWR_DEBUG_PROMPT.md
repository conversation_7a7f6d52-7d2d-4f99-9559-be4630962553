# 🐞 Debug Prompt – TimesheetRosterQuery Stale-While-Revalidate Failure

**Date:** 2025-08-04

---

## 1 Background & Recent Changes

Our Relay cache layer now tracks **per-operation freshness** via a new `opSavedAt` map:

* `ui/src/relay/createPersistedStore.ts`
  * Loads `opSavedAt` from IndexedDB (line ~490).
  * Exposes it globally as `window.__RELAY_OP_SAVED_AT__` (line ~560).
  * Provides `notifyStoreUpdated()` helper to debounce persistence (line ~480).
* `ui/src/relay/useSWRQuery.ts`
  * Builds a unique `cacheKey` with `createCacheKey()` (line ~175).
  * Computes age with:
    ```ts
    const opSavedAt = (window.__RELAY_OP_SAVED_AT__ ?? {})[cacheKey];
    const cacheAge   = opSavedAt ? Date.now() - opSavedAt : Infinity;
    ```
  * Flags background revalidation when `cacheAge > 30_000` or when status is `'stale'` (lines 320-334).
  * After successful `triggerBackgroundRevalidation` it updates
    ```ts
    window.__RELAY_OP_SAVED_AT__[cacheKey] = Date.now();
    notifyStoreUpdated();
    ```
    (lines 229-236).

The rest of the app stayed the same.  Unit tests pass, dev helpers show timestamps changing for most queries.

---

## 2 Current Issue

`TimesheetRosterQuery` **never** triggers background revalidation – cached data is served indefinitely even after many minutes.

* Component path: `ui/src/container/TimesheetRoster/TimesheetRoster.tsx`
  * Runs
    ```ts
    useSWRQuery<TimesheetRosterQueryType>(TimesheetRosterQuery, vars)
    ```
* Dev console shows `window.__RELAY_OP_SAVED_AT__` **contains no entry** for the roster cacheKey, so `useSWRQuery` treats it as `Infinity` and should revalidate — yet the network request is absent.

Suspect areas:
1. **Cache key mismatch** – The key used when updating may differ from the one used when checking.
2. **Availability status** – Relay may be returning `status:'missing'` which forces `store-or-network` but still uses cache without firing `fetchQuery`.
3. **Variables** – Roster component passes large variable objects; stringify order could differ between reads/writes.
4. **notifyStoreUpdated() linkage** – The schedule callback is only set inside the store factory; race condition could break persistence.

---

## 3 Useful Files

| Path | Purpose |
|------|---------|
| `ui/src/relay/useSWRQuery.ts` | SWR logic, cacheAge calc, revalidation trigger |
| `ui/src/relay/createPersistedStore.ts` | Persistence, `opSavedAt` map, `notifyStoreUpdated()` |
| `ui/src/container/TimesheetRoster/TimesheetRoster.tsx` | Component invoking the query |
| `ui/src/relay/cacheFilters.ts` | Filtering logic (may influence roster persistence) |

---

## 4 Desired Outcome

1. Identify **why** `TimesheetRosterQuery` bypasses background revalidation.
2. Provide a concrete fix (code-level) or adjustment to cache-key logic.
3. Ensure per-operation timestamp is created and updated for this query.
4. Confirm roster list updates after >30 s without blocking the UI.

---

## 5 Debug Aids

* In console:
  ```js
  window.__getPerOpTimestamps();        // lists timestamp map
  window.__clearPerOpTimestamps();      // force all queries stale
  window.__enableCacheDelay();          // 10s delay to highlight cache vs net
  ```
* Relay devtools: `window.__RELAY_DEV__?.printCacheSummary()`.

---

## 6 Questions for the AI Assistant

1. Does `createCacheKey()` generate **identical strings** on read & write paths for `TimesheetRosterQuery`?
2. Could sorting of variables (JSON.stringify with sorted keys) break if roster variables include functions or undefined values?
3. Is `environment.check()` for roster query reporting `status:'missing'` and, if so, why isn’t `fetchPolicy:'store-or-network'` causing a network fetch?
4. Is `notifyStoreUpdated()` connected before `triggerBackgroundRevalidation()` runs?  Any timing issues?
5. Any edge cases with connection records and denylist filters that prevent roster data from being persisted, leading to partial data considered “available”?

Please analyse the above files, reproduce the issue if needed, and propose a precise code fix.

---

**End of prompt**
