# Timesheet Roster SWR — Implementation Plan

_Last updated: 2025-08-04_

This document converts the problem analysis into a **step-by-step engineering plan**.  Each section lists what to change, why, and how to verify.

---

## Glossary / Constants
* `TTL_MS` = `REVALIDATE_MS` = 30 000 ms (30 s)
* `cacheKey` = `TimesheetRosterQuery:` + stable-stringified vars
* **Files touched**:
  * `ui/src/relay/useSWRQuery.ts`
  * `ui/src/relay/cacheFilters.ts` (edge-merge helper)
  * `ui/src/relay/__tests__/…`

---

## 1  Respect 30-second TTL
### 1.1  Re-write the `shouldTriggerBackgroundRevalidation` condition
```ts
const isCacheFresh = savedAt && (Date.now() - savedAt) <= REVALIDATE_MS;
const isStaleButExpired = availability.status === 'stale' && !isCacheFresh;
shouldTrigger = !savedAt || isStaleButExpired;
```
*Remove* the existing compound OR.

### 1.2  Unit tests
* `createCacheKey.test.ts` – add a case: timestamp < TTL ⇒ background flag **false**.

### 1.3  QA checklist
* Load the roster twice within 30 s; confirm **no** network call on 2nd load (DevTools).

---

## 2  Avoid no-op store publishes
### 2.1  Switch `fetchPolicy`
* In `triggerBackgroundRevalidation()` change `fetchPolicy: 'network-only'` → `'store-and-network'`.

### 2.2  Hash-based guard (optional hardening)
* Compute `payloadHash = sha1(JSON.stringify(operation.response))`.
* Compare to `window.__RELAY_OP_PAYLOAD_HASHES__[cacheKey]`.
  * If identical, **skip** `commitPayload`.
  * Otherwise update the hash table and proceed.

### 2.3  Dev-only logging
* Log `identicalPayloadSkipped: true` when applicable.

### 2.4  Tests
* Mock network returning identical data; assert that `relayObservability.trackCacheHit()` fires **without** `cacheMiss`.

---

## 3  Preserve paginated edges
### 3.1  Request window alignment (simpler path)
* Pass current end-cursor into the revalidation variables.
  * Retrieve it via `ConnectionHandler.getConnection(record, connectionKey)`.
* Fallback: if no cursor found, keep existing behaviour.

### 3.2  Edge-merge updater (robust path)
* Add helper `mergeConnectionEdges(store, connectionID, newEdges)` in `cacheFilters.ts`.
* In `triggerBackgroundRevalidation`, register a custom updater:
  ```ts
  updater: store => {
    const conn = ConnectionHandler.getConnection(
      store.get(rootID)!, connectionKey, connectionFilters);
    mergeConnectionEdges(store, conn, newEdges);
  }
  ```

### 3.3  Integration test
* Seed store with page 1 & page 2.
* Run revalidation with page 1 payload.
* Assert that edges for page 2 persist (`store.getSource().has(edgeID)`).

---

## 4  Lint / Type safety
* Update the `useEffect` deps to satisfy ESLint:
  ```ts
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [/* intentionally excluding environment, query */]);
  ```
* Add missing types (`PayloadHashTable`) in `types/persistence.ts`.

---

## 5  Documentation & DX
* Update `README_SWR.md` with the TTL policy and debugging tips.
* Add new debug util `window.__RELAY_DEV__.getPayloadHash(cacheKey)`.

---

## 6  Roll-out plan
1. **PR 1** – ttl logic + tests
2. **PR 2** – store-and-network + hash guard
3. **PR 3** – pagination merge
4. **PR 4** – docs & cleanup

Each PR must pass existing CI plus new tests.

---

## 7  Post-deployment monitoring
* Grafana panel: rate of background revalidations / minute — expect sharp drop.
* React Profiler: component commit counts — expect ~1 commit on page load.
* Watch error logs for `mergeConnectionEdges` until confidence is high.
