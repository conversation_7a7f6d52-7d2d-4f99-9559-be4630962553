# Timesheet Roster SWR Refresh – Design Goals, Current State & Path Forward

_Last updated: 2025-08-05_

---

## 1  Problem Statement

Timesheet‐related React components should follow a **stale-while-revalidate (SWR)** model when executing the `TimesheetRosterQuery` (and similar queries):

1. **Serve cached data instantly** if the Relay store has everything needed.
2. **Do not re-fetch** while the cache is _fresh_ ( ≤ 30 s by policy).
3. If the cache is **stale**, fetch in the **background** but **retain** already-fetched paginated pages.
4. **Suppress UI re-rendering** when the server returns a payload identical to what is already cached.

Unfortunately, after a series of hot-fixes the behaviour is again only _partially_ correct:

* The timestamp‐update bug was solved by forcing `fetchPolicy: "network-only"`.
* But the **network-only** policy always publishes the payload, so React Relay re-renders even if data is unchanged.
* The default connection updater **replaces** `edges`, so previously paginated pages can disappear unless we manually merge them.

The goal is a robust mechanism that—per operation—**merges** when the data is logically identical and **replaces** when it truly changed.

---

## 2  Current Code Status (as of this commit)

| Area | State |
|------|-------|
| **Revalidation trigger** | `triggerBackgroundRevalidation()` fires when TTL > 30 s or `RelayAvailability` = `missing`/`stale`. |
| **Network request** | `fetchQuery(..., { fetchPolicy: 'network-only', networkCacheConfig: {force: true} })` guarantees a round-trip. |
| **Edge preservation helper** | `mergeConnectionEdges(store, connection, newEdges)` is fully implemented in `cacheFilters.ts`. |
| **Hash table** | `payloadHashes[cacheKey]` stores a simple hash of the last published edge list. |
| **Store diff instrumentation** | Pre- and post-fetch snapshots log added/removed/changed records & edge deltas. |
| **Attempted updater** | A recent attempt to pass an `updater` to `fetchQuery()` failed to compile (Relay’s API doesn’t accept it). |
| **Build status** | Type-errors introduced by that attempt have been removed; the project now compiles. |

_No logic currently discards cached pages when the hash differs; data is always **merged**, never force-replaced._

---

## 3  What We Still Need

* **Hash-based consistency guard** – After the fresh payload arrives we must decide:
  * If `incomingHash === previousHash` → _merge_ to retain paginated pages.
  * If hashes differ → _replace_ all cached pages with the new list and discard the old ones.
* The implementation must compile, be type-safe, and keep runtime overhead minimal.

---

## 4  Two Implementation Strategies

Below are the two viable approaches we discussed, with detailed trade-offs.

### 4.1  Approach A – Post-Publish `commitUpdate()`

1. Keep **`fetchPolicy: 'network-only'`**. Relay publishes the payload as usual.
2. Await `fetchQuery(...).toPromise()`.
3. Call `environment.commitUpdate(proxy => { … })`.
   * Inside the updater locate the connection record.
   * Compute `incomingHash` from the newly written edges.
   * Compare to `payloadHashes[cacheKey]`.
   * **Decision**
     * Hashes equal → call `mergeConnectionEdges(proxy, connection, newEdges)`.
     * Hashes differ → call `connection.setLinkedRecords(newEdges, 'edges')` (replacement).
   * Update `payloadHashes[cacheKey] = incomingHash`.

#### Pros
* **No custom network layer** – we use public Relay APIs only.
* Easy to reason about: happens after publish, so we can inspect store state.
* Unit-testable in isolation with Relay mock environment.

#### Cons
* **Double work** in the happy-path (identical payload): Relay first writes, then we overwrite a subset in `commitUpdate`.
* Two store publishes in quick succession can trigger two render passes unless we manually debounce them.
* Slightly more boilerplate every time we need similar logic for other queries.

### 4.2  Approach B – Hash Check in the Network Layer

1. Extend the Relay **network layer** (`fetchFn` or `subscribeFn`).
2. When a GraphQL response arrives but _before_ calling `sink.next(payload)`:
   * Compute `incomingHash` on `payload.data`.
   * Compare against `payloadHashes[cacheKey]`.
   * **Decision**
     * Hashes equal → emit **empty payload** `{data: {}, extensions: {skipped: true}}`; Relay treats it as a no-op.
     * Hashes differ → pass the payload through unchanged and optionally mark it for “replace” in extensions.
3. The default connection handler will now _replace_ edges automatically because a full payload arrived; no second publish needed.
4. Update `payloadHashes[cacheKey]` accordingly.

#### Pros
* **Single store publish** → less write-amplification, fewer render passes.
* Logic resides in _one place_ for all queries, not repeated per operation.
* Cleaner separation of concerns: network layer decides whether data is new.

#### Cons
* Requires **custom network** code; harder to onboard new devs.
* Must ensure compatibility with Relay DevTools / logging / error handling.
* Impossible to use Relay’s built-in connection merge helpers; you must either:
  * accept replacement (lose pagination) when data changed, or
  * encode merge semantics in the extensions and handle in a store updater.
* Middleware must remain in sync with any future Relay upgrades.

---

## 5  Recommended Next Steps

| Priority | Task | Rationale |
|----------|------|-----------|
| ☑ (1) | **Re-enable compilation** (done) | Type errors removed so CI passes. |
| ☐ (2) | **Pick a strategy** & implement | Team must decide whether to optimise for simplicity (Approach A) or for fewer store writes (Approach B). |
| ☐ (3) | **Unit tests** | 1) identical payload → `edgeDelta ≥ 0`, `changedRecords ≈ 0` ; 2) changed payload → lost pages replaced. |
| ☐ (4) | **Documentation / DX** | Add debug util `window.__SWR.getPayloadHash(cacheKey)` and update README. |

My recommendation: **start with Approach A** to unblock the bug-fix—minimal risk and easy to roll out. If performance profiling later shows measurable gains from eliminating the second publish, revisit Approach B.

---

## 6  Key Design Decisions & Justifications

1. **Per-operation timestamp (TTL)** – keeps SWR logic query-specific instead of global, aligning with Relay’s normalisation granularity.
2. **Hash granularity** – hashing just `{id, cursor}` per edge is fast, stable across field additions, and good enough to detect list changes.
3. **Merge vs Replace** – we merge by default to preserve pagination slices; _only_ replace when data truly changed, because user-fetched pages are valuable and costly to re-fetch.
4. **30-second TTL** – chosen from product feedback: roster edits typically propagate within that window; shorter TTL would hammer the backend, longer TTL delays updates.

---

## 7  Glossary (quick reference)

| Term | Meaning |
|------|---------|
| **TTL** | Time-to-live; here 30 s per operation. |
| **Edge** | Relay record representing one row in a GraphQL connection. |
| **Connection** | Relay’s list abstraction (`edges[]` + `pageInfo`). |
| **Payload hash** | Simple numeric hash (base36) of edge identities used to detect list changes. |

---

© 2025 EPR Live — Engineering
