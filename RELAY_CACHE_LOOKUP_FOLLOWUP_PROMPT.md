# Follow-up Analysis Prompt for AI Assistant

You are the second-stage AI investigator. The first stage has implemented numerous diagnostics, but **Re<PERSON> is still going to the network for `TimesheetRosterQuery` even though `environment.check()` reports the operation as `available`.**

Below is every piece of context you should need:

---
## Key Symptom
* Store persistence & hydration are successful (1 553 records restored).
* Pre-execution `environment.check()` (added in `ui/src/relay/useSWRQuery.ts`) logs:
  * `✅ Relay pre-check: 'TimesheetRosterQuery' is AVAILABLE from cache`
* Despite that, the query immediately fetches from the network; `useSWRQuery` logs:
  * `cacheStatus: 'MISS'`, `fromCache: false`
* `fetchPolicy` is `store-or-network`.

> In Relay’s API `store-or-network` should **skip** the network when `check()` → `available`. Something else is forcing the fetch.

---
## Relevant Source Files

| Path | Purpose |
|------|---------|
| `ui/src/relay/cacheFilters.ts` | Selective persistence deny-list (now allows sanitised `client:root`). |
| `ui/src/relay/createPersistedStore.ts` | IndexedDB load/save logic, `CACHE_VERSION` `2025-08-04-client-root-fix`. |
| `ui/src/relay/initRelayEnvironment.ts` | Builds `Environment` with persisted `store`, fetch layer, `UNSTABLE_defaultRenderPolicy: 'partial'`. |
| `ui/src/relay/useSWRQuery.ts` | Custom query hook; **lines ~55-100** contain new pre-query `check()` diagnostic and high-level analysis. |
| `ui/src/relay/devUtils.ts` | Adds `__RELAY_DEV__` helpers. |
| `ui/src/relay/fetchRelay.ts` | Network fetch implementation (nothing obviously forcing network first). |

---
## Current Browser Console Log (excerpt)
```
💾 [Persistence] Loaded 1553 cached records …
✅ Relay pre-check: 'TimesheetRosterQuery' is AVAILABLE from cache
🚀 Starting GraphQL query: TimesheetRosterQuery { fetchPolicy: 'store-or-network' }
✅ GraphQL query completed: TimesheetRosterQuery { cacheStatus: 'MISS', fromCache: false }
✅ Relay check(): 'TimesheetRosterQuery' reported AVAILABLE but still fetched network
```

_No `missing` paths are logged, confirming `environment.check()` really returned `available`._

---
## Hypotheses Already Ruled Out
1. **Missing root record** – `client:root` is now persisted & sanitised.
2. **Hydration failure** – Hydration ratio = 100 %.
3. **Size / edge trimming** – Still reports `available` ⇒ not relevant.

---
## Open Questions For You To Solve
1. **Why does Relay still dispatch a network request when its own availability check returns `available`?**
2. Is there anything in our custom hook (`useSWRQuery.ts`) or environment configuration that could override the normal `store-or-network` behaviour?
3. Could another component retain/extract the same query with a different set of variables causing Relay to treat them as distinct?
4. Are we mis-interpreting the `check()` result (e.g. missing optimistic updates) because we don’t retain the operation snapshot?
5. Any known Relay edge-cases (e.g. `@defer`, `renderPolicy:'partial'`, connections) that call the network even when `check()` says available?

---
## Deliverables
1. **Root cause analysis** – clear explanation.
2. **Targeted code change(s)** – point to exact file & lines.
3. **Validation plan** – how to verify fix.
4. **Long-term guardrail** – test or lint that would have caught this.

When referencing code, use relative paths shown above. Be concise but thorough.
