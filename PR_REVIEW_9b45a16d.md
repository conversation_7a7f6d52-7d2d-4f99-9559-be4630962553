# PR Review: `fix(relay): resolve critical SWR behavior issues with TTL and pagination`

**Commit:** `9b45a16d909a505071ae8c3b5e536f0487046964`

This review assesses the commit against the problem statement in `TIMESHEET_ROSTER_SWR_PROBLEMS.md` and the `TIMESHEET_ROSTER_SWR_IMPLEMENTATION_PLAN.md`.

---

## 1. Overall Summary

**Excellent work.** This commit is a significant improvement that directly addresses the critical issues outlined in the planning documents. The fixes for the TTL logic, unnecessary re-renders, and pagination data loss are well-implemented and backed by a strong set of new unit tests. The enhanced debugging tools are a valuable addition for future development.

The implementation successfully resolves the three core problems:
1.  **"Always Fetch" Bug:** Correctly fixed by refining the revalidation condition.
2.  **Unnecessary Re-renders:** Addressed by changing the fetch policy.
3.  **Pagination Loss:** Mitigated with a cursor-alignment strategy.

The changes align well with the implementation plan, with one key area noted for discussion regarding the pagination strategy.

## 2. Adherence to Implementation Plan

The commit successfully implements most of the plan.

| Plan Section | Status | Comments |
| :--- | :--- | :--- |
| **1. Respect 30-second TTL** | ✅ **Implemented** | The `shouldTriggerBackgroundRevalidation` logic in `useSWRQuery.ts` is now correct. The new tests in `selectivePersistence.test.ts` are comprehensive and validate this fix thoroughly. |
| **2. Avoid no-op store publishes** | ✅ **Implemented** | Switched to `store-or-network` fetch policy. While the plan mentioned `store-and-network`, this achieves the same goal of preventing re-renders on identical data. The optional hash-guard was also implemented, which is great. |
| **3. Preserve paginated edges** | ⚠️ **Partially Implemented / Deviates** | The "simpler path" (Request window alignment) was implemented. However, the "robust path" (`mergeConnectionEdges` helper) was also implemented in `cacheFilters.ts` but **is not used** as a Relay `updater` in `useSWRQuery.ts`. This is the most significant deviation from the plan. |
| **4. Lint / Type safety** | ✅ **Implemented** | The `PayloadHashTable` type was correctly added. Code appears clean. |
| **5. Documentation & DX** | ✅ **Implemented** | New debugging utilities for payload hashes have been added as planned. |

---

## 3. File-by-File Analysis & Suggestions

### `ui/src/relay/useSWRQuery.ts`

**Positive:**
- The core TTL logic fix is precise and correctly handles the `stale` status vs. cache expiry. This is the most critical part of the commit.
- The addition of developer debugging tools (`__getPayloadHashes`, etc.) is a fantastic improvement for observability.
- The cursor alignment strategy for pagination is a clever and effective way to mitigate data loss for the common timesheet/roster use case.

**Suggestion / Point for Discussion:**
- **Pagination Strategy:** The code implements cursor alignment but doesn't use the new `mergeConnectionEdges` function as a Relay updater. Was this intentional? The current approach is good, but it's less robust than a full edge merge. For instance, if an item on page 1 is deleted, the cursor alignment strategy won't remove it from the cache on revalidation, whereas a merge strategy could.
  - **Recommendation:** Clarify the intent. If `mergeConnectionEdges` is for future use, consider adding a comment to that effect. If it was intended to be used here, it should be wired up as a custom `updater` in the `fetchQuery` call.

### `ui/src/relay/cacheFilters.ts`

**Positive:**
- The `mergeConnectionEdges` function is well-written and robust. The logic to handle deduplication and smart truncation for business entities is excellent.

**Suggestion:**
- **Error Handling Fallback:** The `catch` block in `mergeConnectionEdges` falls back to replacing the connection's edges entirely (`connection.setLinkedRecords([...newEdges], 'edges')`). This is the original behavior that caused pagination loss. A safer fallback would be to simply log the error and do nothing, preserving the existing (potentially stale) paginated data rather than destroying it.

```typescript
// In cacheFilters.ts -> mergeConnectionEdges -> catch block
} catch (error) {
  if (process.env.NODE_ENV === 'development') {
    console.warn('[SWR] Failed to merge connection edges:', error);
  }
  // CONSIDER: Instead of this fallback which can cause data loss,
  // it might be safer to simply abort the update and preserve the old data.
  // try {
  //   connection.setLinkedRecords([...newEdges], 'edges');
  // } catch (fallbackError) {
  //   console.error('[SWR] Failed to set new edges as fallback:', fallbackError);
  // }
}
```

### `ui/src/relay/__tests__/selectivePersistence.test.ts`

**Positive:**
- The new test suite for the TTL logic is **outstanding**. It covers all relevant cases: fresh cache, stale cache, stale-but-fresh (the bug), missing timestamps, and boundary conditions. This provides high confidence in the fix.

---

## 4. Conclusion

This is a high-quality commit that solves real-world problems. The code is clean, the primary fix is well-tested, and the developer experience is improved.

**Recommendation:** **Approve** after a brief discussion on the pagination strategy (`cursor alignment` vs. `edge merging`) to ensure the current implementation meets all requirements and to clarify the purpose of the unused `mergeConnectionEdges` function.
