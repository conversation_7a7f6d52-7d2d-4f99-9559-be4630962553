---
description:
globs:
alwaysApply: true
---
- Git commit messages need to be in plain text format. dashes should be used for formatting list items.
- Never take shortcuts or workarounds. Always prefer quality over expedience and never make changes to hide symptoms.
- My frontend code uses React and Relay and pnpm package manager.
- My backend code uses C# and .NET 9 and Hot Chocolate GraphQL library.
- When testing changes, please refrain from running commands that are expected to produce a very large result. e.g Running all tests. Also, do not start the application for testing purposes. It may already be running. Just the user to do it.
