---
description: Apply these rules when the user asks for <PERSON><PERSON><PERSON> to review the pull request
alwaysApply: false
---

# Pull Request Review Rules

## Workflow Requirements

### MANDATORY PRE-REVIEW CHECK
When the user requests a pull request review, you MUST:

1. **Check for @Branch context** - Verify that the user has included "@Branch (Diff with Main Branch)" context in their message
2. **If context is missing** - STOP and inform the user they need to include the @Branch context
3. **Do NOT proceed** with the review until the proper context is provided

### Required Context Format
The user must include: `@Branch (Diff with Main Branch)` in their message requesting the review.

**Example sample request:**
"@Branch (Diff with Main Branch) Please PR review"

### If Context is Missing
Respond with:
```
I need the branch diff context to perform a proper PR review. Please include "@Branch (Diff with Main Branch)" in your message and ask again.

Example: "Please review this PR using pr-review rules @Branch (Diff with Main Branch)"
```

### Once Context is Provided
1. **Use the @Branch context** - Do NOT run additional git commands
2. **Analyze the diff directly** - The @Branch context contains all the necessary change information
3. **Focus on the provided diff content** - Review what's actually changed without executing redundant commands

## Issue Reference Requirements

### MANDATORY: GitHub Issue Auto-Close Reference
When reviewing a PR, you MUST check for and recommend including a GitHub issue reference that will automatically close the related issue:

#### Required Format and Positioning:
- The issue reference MUST be placed **at the very top of the PR body** (immediately after the PR title, before any other content).
- Use one of these closing keywords followed by the issue number:
  - `Resolves: #[issue-number]`
  - `Closes: #[issue-number]`
  - `Fixes: #[issue-number]`
- **IMPORTANT:**
  - The PR review document you generate will be directly used as the PR body on GitHub.
  - Therefore, you MUST start the PR review document with the required issue reference (e.g., `Resolves: #660`) in **plain text** at the very top—**not** in a code block, section, or as an example. This is mandatory for GitHub's auto-closing functionality.

#### How to Find the Issue Number:
1. **From Branch Name**: Look for issue numbers in the branch name (e.g., `Joseph-660-add-issue-generation-cursor-rules` → `#660`)
2. **From Commit Messages**: Check commit messages for issue number references (e.g., `#660: Add issue generation rules`)
3. **From PR Context**: Look for issue references in the diff or commit history
4. **Ask User**: If no issues number can be found from the options above, ask the user for the issue number

**Note**: This follows the exact format demonstrated in the EPRLive24 project, where the issue reference appears as the very first line of the PR body, followed by a blank line, then the rest of the PR content.

#### Review Requirement:
- **If issue reference is missing**: Flag this as a required improvement in your review
- **If issue reference is present**: Verify the issue number matches the branch/commits
- **If multiple issues are addressed**: List all relevant issue numbers:
  Resolves: #660
  Resolves: #661

#### Why This Matters:
- Automatically closes issues when PR is merged
- Creates clear traceability between issues and code changes
- Improves project management and issue tracking

## Review Structure

Please review the changes in this branch in detail from the perspective of someone reviewing a git PR for approval. The main branch for this repo is develop. 

The review should have at minimum the following sections:

1. **Introduction**
   - What is the primary goal of these changes
   - Brief summary of what files/areas are affected
   - **Issue Reference Check**: Verify if proper GitHub issue auto-close reference is included (e.g., "Resolves: #660")

2. **Detailed description of changes**
   - File-by-file analysis of modifications
   - New files added and their purpose
   - Removed content and rationale
   - Configuration or structural changes

3. **Review accuracy and adherence to best practices**
   - Code quality assessment
   - Adherence to project conventions
   - Documentation completeness
   - Security considerations
   - Performance implications

4. **Potential issues and areas of improvement**
   - Any concerns or red flags
   - Suggestions for enhancement
   - Missing elements or considerations
   - Risk assessment

5. **Conclusion**
   - Overall assessment (approve/request changes/needs discussion)
   - Key takeaways and next steps
   - Priority of any identified issues

## Output Requirements

- Save the output in a markdown file
- Use clear headings and bullet points for readability
- Reference specific files and line numbers when relevant
- Provide actionable feedback where improvements are suggested
- **MANDATORY:** The PR review document you generate will be used as the PR body. Therefore, it MUST start with the required issue reference (e.g., `Resolves: #660`) in plain text at the very top, not in a code block or as an example.
- Use clear headings and bullet points for readability
- Reference specific files and line numbers when relevant
- Provide actionable feedback where improvements are suggested 
- Provide actionable feedback where improvements are suggested 
- Use clear headings and bullet points for readability
- Reference specific files and line numbers when relevant
- Provide actionable feedback where improvements are suggested 