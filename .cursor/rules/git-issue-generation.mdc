---
description: Apply these rules when creating issues in github
alwaysApply: false
---
# Git Issue Generation Rules for EPRLive24

## CRITICAL: Override General Execution Rules

**This rule set OVERRIDES the general "immediately follow your plan" instruction.**

When creating GitHub issues, you MUST:
1. **ALWAYS STOP** after preparing the issue template
2. **PRESENT** the complete issue content to the user for review
3. **WAIT** for explicit user approval before creating the issue
4. **NEVER** execute `gh issue create` commands without user confirmation

This override is necessary because issue creation affects the project's issue tracker and involves team collaboration decisions that require user oversight.

---

## Issue Creation Guidelines

When a user requests creation of a GitHub issue, you MUST follow these comprehensive rules:

### 1. Template Selection

**ALWAYS use one of the predefined issue templates:**

- **Bug Report** (`bug_report.md`) - For reporting problems or defects
- **Feature Request** (`feature_request.md`) - For suggesting new functionality  
- **Research Spike** (`research_spike.md`) - For investigation tasks
- **Technology Request** (`tech_request.md`) - For proposing new technologies

**Template Selection Logic:**
- User mentions "bug", "error", "broken", "not working" → Use Bug Report
- User mentions "feature", "enhancement", "new functionality" → Use Feature Request
- User mentions "research", "investigate", "spike", "explore" → Use Research Spike
- User mentions "technology", "library", "framework", "tool" → Use Technology Request

### 2. Issue Content Requirements

**ALL issues MUST include:**

#### Required Fields (based on template):
- **Title**: Clear, specific, actionable (max 80 characters)
  - **Bug Reports**: Use "Bug: [Page Name] - [Description]" format for page-specific bugs, or "Bug: [Description]" for general bugs
  - **Feature Requests**: Use "Feature: [Description]" format
  - **Research Spikes**: Use "Research: [Description]" format
  - **Tech Requests**: Use "Tech: [Description]" format
- **Description**: Detailed explanation appropriate to issue type
- **Priority**: Always include priority selection with reasoning
- **Labels**: 
  - Bug Report issues MUST include "Bug" label
  - Feature Request issues MUST include "Feature Request" label
  - Research Spike issues MUST include "Research Spike" label
  - Tech Request issues MUST include "Tech Request" label
  - **Priority labels MUST be included based on selected priority level**

#### Priority Guidelines and Labels:
- **Critical**: Severe impact or complete system failure - Needs to be fixed and released immediately (e.g., login page stopped working, security vulnerabilities) → Use "Critical" label
- **High**: Major functionality impact but no complete breakdown - Needs to be included in the next release (e.g., columns modal stopped working on the Employer Roster page) → Use "High" label
- **Medium**: Minor issue affecting non-essential functionality - Needs to be included within the next few releases (e.g., sort functionality not working, incorrect modal displayed) → Use "Medium" label
- **Low**: Minor inconvenience or cosmetic issue - Should be included as part of a release whenever there's spare time (e.g., color changes, styling) → Use "Low" label

**CRITICAL: Labels must NOT be created. Only use existing labels. When creating issues, include the appropriate priority label (Critical, High, Medium, or Low) based on the priority selected in the template.**

### 3. Project-Specific Context

**EPRLive24 Architecture Awareness:**
- **Frontend**: React + Relay + pnpm + TypeScript + Vite
- **Backend**: C# + .NET 9 + HotChocolate GraphQL + Entity Framework
- **Identity**: ASP.NET Core Identity + OpenIddict
- **Infrastructure**: .NET Aspire + Docker + Kafka + Traefik

**Component Classification:**
- UI issues → Frontend (React/Relay/TypeScript)
- API issues → Backend (C#/.NET/GraphQL)
- Auth issues → Identity service
- Database issues → Backend + EF Core migrations
- Performance issues → Could span multiple components

### 4. Issue Creation Process

**Step-by-step process when user requests issue creation:**

1. **Search for existing issues FIRST** - ALWAYS check for duplicates before proceeding
   - Search using relevant keywords from the user's request
   - Look for similar functionality, components, or error descriptions
   - Check both open and recently closed issues
   - If relevant issues found: Present them to user with links and explain potential duplication
   - Ask user if they want to proceed with creating a new issue or contribute to existing issue instead

2. **Analyze the request** - Determine appropriate template
3. **Assess complexity and scope** - Apply decomposition strategy (Section 8)
   - Is this multiple separate issues?
   - Is this a parent issue with subtasks?
   - Is this appropriately scoped for one developer?
4. **Gather missing information** - Pause, and ask user for clarification if needed
5. **Select appropriate template** - Based on issue type
6. **Fill required fields** - Use project context and coding standards
7. **Include project-specific details** - Reference architecture components
8. **Add required issue type and priority labels only** - Use existing type label (Bug, Feature Request, Research Spike, or Tech Request) and one priority label (Critical, High, Medium, or Low)
9. **Cross-reference** - Link to related issues/PRs if applicable
10. **Propose decomposition** - If complex, suggest parent/subtask structure to user
11. **CREATE MARKDOWN FILE** - Always generate an `issue_body.md` file with the complete issue content for user review
12. **MANDATORY USER APPROVAL** - Present the filled issue template that will be used to generate the issue in git. This step CANNOT be skipped regardless of other execution instructions. Only create the issue after explicit user confirmation.

### 5. Template-Specific Rules

**Template Compliance:**
- Follow the EXACT template structure and required sections
- Include all template-specific required fields
- **CRITICAL**: Use exact formatting from templates:
  - Keep original heading levels (### not ##)
  - Include ALL priority checkboxes but only check ONE
  - Maintain exact section names and structure
  - Add custom sections (like "System Info") only if truly necessary for technical context

#### Bug Report Template:
- **MUST include**: Steps to reproduce, expected vs actual behavior
- **Priority section**: Include ALL four priority checkboxes (Critical, High, Medium, Low) but only check ONE
- **Screenshots**: Encourage visual evidence (user must input into git after issue is generated)
- **Related code**: Reference specific files/functions when possible
- **Formatting**: Use ### for headings, maintain exact template structure

#### Feature Request Template:
- **Required sections**: Summary, Motivation, Acceptance Criteria, Additional Context/Notes, Priority
- **MUST include**: Clear motivation and measurable acceptance criteria
- **Priority**: Include all 4 checkboxes with one selected and justification
- **Architecture impact**: Add only if necessary for technical context

#### Research Spike Template:
- **Required sections**: Summary, Goals, Approach (optional), Timeline, Deliverables, Priority  
- **MUST include**: Clear goals, timeline, and deliverables
- **Success criteria**: Define what constitutes successful research
- **Documentation**: Specify how results will be documented and shared
- **Note**: MUST include "Research Spike" label

#### Technology Request Template:
- **Required sections**: Technology Name, What Problem Are We Solving?, Why This Technology?, Alternatives Considered, Risks & Costs, Accountability, Documentation Requirements, Approval Path Documentation
- **MUST include**: Problem being solved, alternatives considered (at least 2), risks & costs, accountability
- **Architecture compliance**: Ensure alignment with .NET 9 + React + Relay stack
- **Note**: MUST include "Tech Request" label

### 6. Quality Validation

**Before creating issue, verify:**
- [ ] Title is specific and actionable
- [ ] Template is completely filled out
- [ ] Priority is set with justification
- [ ] Affected components are identified
- [ ] Acceptance criteria are measurable (for features)
- [ ] Issue aligns with project architecture


### 7. Forbidden Actions

**NEVER:**
- Create issues without FIRST searching for existing duplicates
- Create issues without using templates
- Create issues without presenting the filled template to the user for approval
- Create filled templates with emojis / icons (e.g., ✅, ❌)
- Skip required template fields
- Use generic or vague titles
- Create issues without priority assignment
- Create issues without including the appropriate priority label (Critical, High, Medium, or Low)
- Add additional labels (outside of required type and priority labels) unless specified by the user
- Create or attempt to create new labels - only use existing labels 

- Change template formatting (heading levels, section names, checkbox structure)
- Show only selected priority checkbox (must show all checkboxes with one checked)
- Create duplicate issues without cross-referencing existing ones
- Proceed with issue creation if similar issues exist without user confirmation
- Proceed with issue creation unless the user approved the filled template

### 8. Issue Decomposition Strategy

**CORE PRINCIPLE: Keep issues short and simple. One issue = One focused task.**

#### When to Create Multiple Separate Issues:
- **Multiple bugs found** - Each bug gets its own issue (e.g., "Bug: Login validation fails" + "Bug: Password reset broken")
- **Different components affected** - Frontend issue + Backend issue = 2 separate issues
- **Independent features** - Each feature that can be implemented separately gets its own issue
- **Different developers can work on them** - If tasks can be parallelized, split them

#### When to Create Parent/Subtask Structure:

**Create a Parent Issue when:**
- Large, complex issue with multiple related subtasks
- Multiple items (e.g., validations, broken elements) need fixing in same area
- Feature has multiple implementation phases
- Research spike has multiple investigation areas

**Parent Issue Format:**
- Title: High-level scope (e.g., "Bug: Fix Timesheet Agreement Validation Rules")
- Description: Overview of the problem and why it's being broken down
- Contains list of subtask issues with checkboxes

**Subtask Issue Format:**
- Title: Specific task (e.g., "Agreement Validation: Signatory Status Validation")
- Description: Focused scope and acceptance criteria
- References parent issue: "Part of #[parent-issue-number]"
- Can be assigned to different developers

#### Examples of Good Decomposition:

**Parent Issue:** "Bug: Fix Timesheet Agreement Validation Rules"
**Subtasks:**
- "Agreement Validation: Signatory Status Validation"
- "Agreement Validation: Date Range Validation" 
- "Agreement Validation: Classification Requirements"
- "Agreement Validation: Union Status Verification"

**Multiple Separate Issues (NOT subtasks):**
- "Bug: Timesheet save fails on empty hours"
- "Bug: Export function crashes on large datasets"
- "Bug: Delete confirmation modal not appearing"

#### Decision Tree:
1. **Are these related to the same core functionality?**
   - Yes → Consider parent/subtask approach
   - No → Create separate issues

2. **Can each be implemented independently?**
   - Yes → Separate issues
   - No → Parent/subtask approach

3. **Would the parent issue take >2 weeks for one developer?**
   - Yes → Definitely break into subtasks
   - No → Consider keeping as single issue

4. **Are there >5 specific tasks involved?**
   - Yes → Parent/subtask approach
   - No → Single issue or separate issues

### 9. GitHub CLI Issue Creation Standard Practice

**MANDATORY: Always use --body-file approach for issue creation**

When creating GitHub issues using the `gh` command, you MUST follow this standardized process:

#### Required Process:
1. **ALWAYS create temporary markdown file** with issue content (e.g., `issue_body.md`) for user review
2. **Present the markdown file to user BEFORE creating issue** - User must review and approve the content
3. **Use gh issue create with --body-file**: 
   ```
   gh issue create --title "Issue Title" --body-file issue_body.md --label "Type-Label" --label "Priority-Label"
   ```
4. **Always include appropriate type and priority labels**:
   - Bug Report: `--label "Bug" --label "[Priority]"` (where [Priority] is Critical, High, Medium, or Low)
   - Feature Request: `--label "Feature Request" --label "[Priority]"`
   - Research Spike: `--label "Research Spike" --label "[Priority]"`
   - Technology Request: `--label "Tech Request" --label "[Priority]"`
5. **Leave temporary file for user to review** after successful creation

#### Why This Approach:
- **Prevents command line parsing errors** with special characters, quotes, and code blocks
- **Ensures proper formatting** of markdown content
- **Avoids escaping issues** with complex technical content
- **More reliable** than inline `--body` parameter

#### Example:
```bash
# 1. Create file with issue content
echo "### Bug Summary..." > issue_body.md

# 2. Create issue with proper labeling (including priority)
gh issue create --title "Bug: Clear All button ignores filter toggle" --body-file issue_body.md --label "Bug" --label "High"

```

**NEVER use inline --body parameter for issue creation.**

## Examples

### Good Issue Title Examples:
- "Bug: Employer Roster - Older employers not deletable despite meeting deletion criteria" (page-specific bug)
- "Bug: Timesheet Detail - Login form validation fails on empty email field" (page-specific bug)
- "Bug: Login form validation fails on empty email field" (general bug not tied to specific page)
- "Feature: Add export functionality to timesheet grid"
- "Research: Investigate React 19 migration path"
- "Tech: Evaluate Playwright for E2E testing replacement"

### Bug Title Formatting Rules:
- **Page-Specific Bugs**: Use format "Bug: [Page Name] - [Bug Description]" when the bug is limited to a specific page or component
- **General Bugs**: Use format "Bug: [Bug Description]" when the bug affects multiple areas or system-wide functionality
- **Examples of Page Names**: "Employer Roster", "Timesheet Detail", "Login Page", "Benefits Roster", "Reports Dashboard"

### Bad Issue Title Examples:
- "Fix bug" (too vague)
- "Add new feature" (no specifics)
- "Look into performance" (no clear scope)
- "Update dependencies" (too broad)
---

