namespace EPREvents.Configuration
{
    public class KafkaConsumerOptions
    {
        public string BootstrapServers { get; set; } = "localhost:29092";
        public string GroupId { get; set; } = "EPRIdentity";
        public string DefaultTopic { get; set; } = "EPRLive";
        public int SocketTimeoutMs { get; set; } = 30000;
        public int ConnectionsMaxIdleMs { get; set; } = 60000;
        public int SessionTimeoutMs { get; set; } = 45000;
        public int ReconnectBackoffMs { get; set; } = 1000;
        public int ReconnectBackoffMaxMs { get; set; } = 60000;
        public int TopicMetadataRefreshIntervalMs { get; set; } = 300000;
        public bool EnableAutoOffsetStore { get; set; } = false;
        public bool AllowAutoCreateTopics { get; set; } = true;
        public string? Debug { get; set; }
        public string AutoOffsetReset { get; set; } = "Earliest"; // To be parsed to Confluent.Kafka.AutoOffsetReset enum

        // For ConsumerService.Subscribe retry loop
        public int MaxSubscriptionRetries { get; set; } = 5;
        public int SubscriptionRetryInitialDelayMs { get; set; } = 1000;

        // For IdentityConsumerService connection retries
        public int MaxConnectionRetriesIdentity { get; set; } = 10;
        public int InitialConnectionRetryDelayMsIdentity { get; set; } = 5000;

        // DLQ Configuration
        public string DLQTopicName { get; set; } = "EPRLive-DLQ";
        public int DLQMaxRetries { get; set; } = 3;
        public bool DLQEnabled { get; set; } = true;
    }
}
