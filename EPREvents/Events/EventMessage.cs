﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Nodes;
using System.Threading.Tasks;

namespace EPREvents.Events
{
    public class EventMessage
    {
        public EventTypes EventType { get; set; }
        public JsonNode Payload { get; set; }

        public EventMessage(EventTypes eventType, JsonNode payload)
        {
            EventType = eventType;
            Payload = payload;
        }
    }
}
