﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Confluent.Kafka;
using EPREvents.Interfaces;
using Microsoft.Extensions.Configuration;

namespace EPREvents.Producers
{
    public class ProducerService : IProducerService
    {
        private readonly IConfiguration _configuration;
        private readonly IProducer<Null, string> _producer;
        private readonly string _topic;

        public ProducerService(IConfiguration configuration)
        {
            _configuration = configuration;
            var config = new ProducerConfig
            {
                BootstrapServers = _configuration["Kafka:BootstrapServers"],
            };
            _producer = new ProducerBuilder<Null, string>(config).Build();
            _topic =
                _configuration["Kafka:Topic"]
                ?? throw new ArgumentNullException(nameof(_topic), "Kafka topic cannot be null");
        }

        public async Task ProduceEventAsync<T>(T message)
        {
            try
            {
                var jsonMessage = JsonSerializer.Serialize(
                    new { EventType = message?.GetType().Name, Payload = message }
                );

                var deliveryResult = await _producer.ProduceAsync(
                    _topic,
                    new Message<Null, string> { Value = jsonMessage }
                );
                Console.WriteLine(
                    $"Delivered '{deliveryResult.Value}' to '{deliveryResult.TopicPartitionOffset}'"
                );
            }
            catch (ProduceException<Null, string> e)
            {
                Console.WriteLine($"Delivery failed: {e.Error.Reason}");
            }
        }
    }
}
