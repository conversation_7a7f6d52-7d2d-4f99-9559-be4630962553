using System;
using System.Text.Json;
using System.Threading.Tasks;
using Confluent.Kafka;
using EPREvents.Configuration;
using EPREvents.Interfaces;
using EPREvents.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace EPREvents.Producers
{
    /// <summary>
    /// Service for producing messages to a Dead Letter Queue (DLQ)
    /// </summary>
    public class DLQProducerService : IDLQProducerService, IDisposable
    {
        private readonly IProducer<Null, string> _producer;
        private readonly string _dlqTopic;
        private readonly ILogger<DLQProducerService> _logger;
        private readonly bool _dlqEnabled;

        /// <summary>
        /// Creates a new instance of the DLQProducerService
        /// </summary>
        /// <param name="kafkaOptionsAccessor">The Kafka consumer options</param>
        /// <param name="logger">The logger</param>
        public DLQProducerService(
            IOptions<KafkaConsumerOptions> kafkaOptionsAccessor,
            ILogger<DLQProducerService> logger
        )
        {
            var kafkaOptions = kafkaOptionsAccessor.Value ?? throw new ArgumentNullException(nameof(kafkaOptionsAccessor));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _dlqTopic = kafkaOptions.DLQTopicName;
            _dlqEnabled = kafkaOptions.DLQEnabled;

            var config = new ProducerConfig
            {
                BootstrapServers = kafkaOptions.BootstrapServers,
                // Add any additional producer configuration here
            };

            _producer = new ProducerBuilder<Null, string>(config).Build();
            
            _logger.LogInformation("DLQProducerService initialized with topic: {DLQTopic}, enabled: {DLQEnabled}", 
                _dlqTopic, _dlqEnabled);
        }

        /// <summary>
        /// Produces a message to the Dead Letter Queue
        /// </summary>
        /// <param name="dlqMessage">The message to send to the DLQ</param>
        /// <returns>True if the message was successfully sent to the DLQ, false otherwise</returns>
        public async Task<bool> ProduceAsync(DLQMessage dlqMessage)
        {
            if (!_dlqEnabled)
            {
                _logger.LogWarning("DLQ is disabled. Message not sent to DLQ. Original topic: {Topic}, partition: {Partition}, offset: {Offset}",
                    dlqMessage.OriginalTopic, dlqMessage.OriginalPartition, dlqMessage.OriginalOffset);
                return false;
            }

            try
            {
                string serializedMessage = JsonSerializer.Serialize(dlqMessage);
                
                var message = new Message<Null, string>
                {
                    Value = serializedMessage
                };

                var deliveryResult = await _producer.ProduceAsync(_dlqTopic, message);
                
                _logger.LogInformation(
                    "Message sent to DLQ. Topic: {Topic}, Partition: {Partition}, Offset: {Offset}. Original message from: {OriginalTopic}, {OriginalPartition}, {OriginalOffset}",
                    deliveryResult.Topic, 
                    deliveryResult.Partition, 
                    deliveryResult.Offset,
                    dlqMessage.OriginalTopic,
                    dlqMessage.OriginalPartition,
                    dlqMessage.OriginalOffset
                );
                
                return true;
            }
            catch (ProduceException<Null, string> ex)
            {
                _logger.LogError(
                    ex,
                    "Failed to produce message to DLQ. Topic: {Topic}, Error: {ErrorReason}. Original message from: {OriginalTopic}, {OriginalPartition}, {OriginalOffset}",
                    _dlqTopic,
                    ex.Error.Reason,
                    dlqMessage.OriginalTopic,
                    dlqMessage.OriginalPartition,
                    dlqMessage.OriginalOffset
                );
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Unexpected error producing message to DLQ. Topic: {Topic}. Original message from: {OriginalTopic}, {OriginalPartition}, {OriginalOffset}",
                    _dlqTopic,
                    dlqMessage.OriginalTopic,
                    dlqMessage.OriginalPartition,
                    dlqMessage.OriginalOffset
                );
                return false;
            }
        }

        /// <summary>
        /// Disposes the producer
        /// </summary>
        public void Dispose()
        {
            _producer?.Dispose();
        }
    }
}
