using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Threading;
using System.Threading.Tasks;
using Confluent.Kafka;
using Confluent.Kafka.Admin;
using EPREvents.Configuration;
using EPREvents.Events;
using EPREvents.Interfaces;
using EPREvents.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace EPREvents.Consumers
{
    /// <summary>
    /// Event-driven implementation of a Kafka consumer service
    /// </summary>
    public class EventDrivenConsumerService : IEventDrivenConsumerService, IDisposable
    {
        private readonly KafkaConsumerOptions _kafkaOptions;
        private readonly ILogger<EventDrivenConsumerService> _logger;
        private IConsumer<Ignore, string> _consumer;
        private Task? _consumeTask;
        private CancellationTokenSource? _cts;
        private readonly Dictionary<Type, Delegate> _eventHandlers = new();
        private Func<EventMessage, Task>? _genericHandler;
        private readonly IDLQProducerService _dlqProducerService;
        private readonly ConcurrentDictionary<TopicPartitionOffset, int> _messageRetryCount = new();
        private bool _isStarted = false;

        /// <summary>
        /// Creates a new instance of the EventDrivenConsumerService
        /// </summary>
        /// <param name="kafkaOptionsAccessor">The Kafka consumer options</param>
        /// <param name="logger">The logger</param>
        /// <param name="dlqProducerService">The DLQ producer service</param>
        public EventDrivenConsumerService(
            IOptions<KafkaConsumerOptions> kafkaOptionsAccessor,
            ILogger<EventDrivenConsumerService> logger,
            IDLQProducerService dlqProducerService
        )
        {
            _kafkaOptions =
                kafkaOptionsAccessor.Value
                ?? throw new ArgumentNullException(nameof(kafkaOptionsAccessor));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _dlqProducerService =
                dlqProducerService ?? throw new ArgumentNullException(nameof(dlqProducerService));

            // Create the consumer configuration
            var config = new ConsumerConfig
            {
                BootstrapServers = _kafkaOptions.BootstrapServers,
                GroupId = _kafkaOptions.GroupId,
                EnableAutoOffsetStore = _kafkaOptions.EnableAutoOffsetStore,
                AutoOffsetReset = Enum.Parse<AutoOffsetReset>(_kafkaOptions.AutoOffsetReset),
                SocketTimeoutMs = _kafkaOptions.SocketTimeoutMs,
                SessionTimeoutMs = _kafkaOptions.SessionTimeoutMs,
                ReconnectBackoffMs = _kafkaOptions.ReconnectBackoffMs,
                ReconnectBackoffMaxMs = _kafkaOptions.ReconnectBackoffMaxMs,
                ConnectionsMaxIdleMs = _kafkaOptions.ConnectionsMaxIdleMs,
                TopicMetadataRefreshIntervalMs = _kafkaOptions.TopicMetadataRefreshIntervalMs,
                AllowAutoCreateTopics = _kafkaOptions.AllowAutoCreateTopics,
            };

            // Add debug configuration if specified
            if (!string.IsNullOrEmpty(_kafkaOptions.Debug))
            {
                config.Debug = _kafkaOptions.Debug;
            }

            // Create the consumer
            _consumer = new ConsumerBuilder<Ignore, string>(config).Build();

            _logger.LogInformation(
                "EventDrivenConsumerService initialized with configuration: {Config}",
                JsonSerializer.Serialize(config, new JsonSerializerOptions { WriteIndented = true })
            );
        }

        /// <inheritdoc />
        public async Task StartAsync(string topic, CancellationToken cancellationToken = default)
        {
            if (_isStarted)
            {
                _logger.LogWarning("Consumer already started. Ignoring StartAsync call.");
                return;
            }

            _cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);

            await SubscribeToTopicAsync(topic, _cts.Token);

            _consumeTask = Task.Run(
                async () =>
                {
                    try
                    {
                        _logger.LogInformation(
                            "Starting event-driven consumer loop for topic: {Topic}",
                            topic
                        );

                        while (!_cts.Token.IsCancellationRequested)
                        {
                            try
                            {
                                // Consume messages continuously without timeouts
                                var consumeResult = _consumer.Consume(_cts.Token);

                                if (consumeResult?.Message?.Value == null)
                                {
                                    _logger.LogDebug(
                                        "Consumed null or empty message. Continuing..."
                                    );
                                    continue;
                                }

                                // Process the message
                                await ProcessMessageAsync(consumeResult, _cts.Token);
                            }
                            catch (OperationCanceledException)
                                when (_cts.Token.IsCancellationRequested)
                            {
                                // Normal cancellation, exit the loop
                                _logger.LogInformation("Consumer loop cancelled. Exiting...");
                                break;
                            }
                            catch (KafkaException kex)
                            {
                                _logger.LogError(
                                    kex,
                                    "Kafka error during consumption: {ErrorCode} - {Reason}",
                                    kex.Error.Code,
                                    kex.Error.Reason
                                );

                                // For critical Kafka errors, we might want to break and restart
                                if (kex.Error.IsFatal)
                                {
                                    _logger.LogCritical(
                                        "Fatal Kafka error. Consumer will be restarted."
                                    );
                                    throw;
                                }

                                // For non-fatal errors, continue processing
                                await Task.Delay(1000, _cts.Token); // Small delay to prevent tight error loops
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "Error consuming message");
                                // Continue processing (don't break the loop)
                                await Task.Delay(1000, _cts.Token); // Small delay to prevent tight error loops
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Fatal error in consume loop");
                        throw; // Rethrow to signal the service to restart
                    }
                },
                _cts.Token
            );

            _isStarted = true;
            _logger.LogInformation(
                "EventDrivenConsumerService started successfully for topic: {Topic}",
                topic
            );
        }

        /// <inheritdoc />
        public async Task StopAsync(CancellationToken cancellationToken = default)
        {
            if (!_isStarted)
            {
                _logger.LogWarning("Consumer not started. Ignoring StopAsync call.");
                return;
            }

            _logger.LogInformation("Stopping EventDrivenConsumerService...");

            try
            {
                // Cancel the token to stop the consume loop
                _cts?.Cancel();

                // Wait for the consume task to complete with a timeout
                if (_consumeTask != null)
                {
                    var timeoutTask = Task.Delay(TimeSpan.FromSeconds(10), cancellationToken);
                    var completedTask = await Task.WhenAny(_consumeTask, timeoutTask);

                    if (completedTask == timeoutTask)
                    {
                        _logger.LogWarning("Timed out waiting for consume task to complete");
                    }
                }

                // Unsubscribe and close the consumer
                try
                {
                    _consumer?.Unsubscribe();
                    _logger.LogInformation("Consumer unsubscribed successfully");
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error unsubscribing consumer");
                }

                _isStarted = false;
                _logger.LogInformation("EventDrivenConsumerService stopped successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error stopping EventDrivenConsumerService");
                throw;
            }
        }

        /// <inheritdoc />
        public void RegisterEventHandler<TEvent>(Func<TEvent, Task> handler)
            where TEvent : class
        {
            if (handler == null)
            {
                throw new ArgumentNullException(nameof(handler));
            }

            _eventHandlers[typeof(TEvent)] = handler;
            _logger.LogInformation(
                "Registered handler for event type: {EventType}",
                typeof(TEvent).Name
            );
        }

        /// <inheritdoc />
        public void RegisterGenericHandler(Func<EventMessage, Task> handler)
        {
            _genericHandler = handler ?? throw new ArgumentNullException(nameof(handler));
            _logger.LogInformation("Registered generic event handler");
        }

        /// <summary>
        /// Subscribes to the specified Kafka topic
        /// </summary>
        private async Task SubscribeToTopicAsync(string topic, CancellationToken cancellationToken)
        {
            int retryCount = 0;
            int maxRetries = _kafkaOptions.MaxSubscriptionRetries;
            int retryDelayMs = _kafkaOptions.SubscriptionRetryInitialDelayMs;

            while (true)
            {
                try
                {
                    // Check if topic exists and create it if needed (before subscribing)
                    if (_kafkaOptions.AllowAutoCreateTopics && !IsTopicExists(topic))
                    {
                        _logger.LogInformation(
                            "Topic {Topic} does not exist. Attempting to create.",
                            topic
                        );
                        await CreateTopicAsync(topic, cancellationToken);
                    }
                    else if (!_kafkaOptions.AllowAutoCreateTopics && !IsTopicExists(topic))
                    {
                        _logger.LogWarning(
                            "Topic {Topic} does not exist and auto-creation is disabled. Consumer may not receive messages.",
                            topic
                        );
                    }

                    // Also check and create DLQ topic if enabled and auto-creation is allowed
                    if (
                        _kafkaOptions.AllowAutoCreateTopics
                        && _kafkaOptions.DLQEnabled
                        && !IsTopicExists(_kafkaOptions.DLQTopicName)
                    )
                    {
                        _logger.LogInformation(
                            "DLQ Topic {DLQTopic} does not exist. Attempting to create.",
                            _kafkaOptions.DLQTopicName
                        );
                        await CreateTopicAsync(_kafkaOptions.DLQTopicName, cancellationToken);
                    }

                    _logger.LogInformation("Subscribing to topic: {Topic}", topic);
                    _consumer.Subscribe(topic);
                    _logger.LogInformation("Successfully subscribed to topic: {Topic}", topic);
                    return;
                }
                catch (KafkaException kex)
                {
                    retryCount++;

                    if (retryCount > maxRetries)
                    {
                        _logger.LogError(
                            kex,
                            "Failed to subscribe to topic {Topic} after {MaxRetries} retries",
                            topic,
                            maxRetries
                        );
                        throw;
                    }

                    _logger.LogWarning(
                        kex,
                        "Error subscribing to topic {Topic}. Retry {RetryCount}/{MaxRetries} in {RetryDelayMs}ms",
                        topic,
                        retryCount,
                        maxRetries,
                        retryDelayMs
                    );

                    await Task.Delay(retryDelayMs, cancellationToken);

                    // Exponential backoff with jitter
                    retryDelayMs = (int)(retryDelayMs * (1.5 + new Random().NextDouble() * 0.5));
                }
            }
        }

        /// <summary>
        /// Checks if a topic exists
        /// </summary>
        private bool IsTopicExists(string topicName)
        {
            using var adminClient = new AdminClientBuilder(
                new AdminClientConfig { BootstrapServers = _kafkaOptions.BootstrapServers }
            ).Build();

            try
            {
                return adminClient
                    .GetMetadata(topicName, TimeSpan.FromSeconds(10))
                    .Topics.Any(x => x.Topic == topicName);
            }
            catch (KafkaException ex)
            {
                _logger.LogError(ex, "Error checking topic existence for {TopicName}", topicName);
                return false;
            }
        }

        /// <summary>
        /// Creates a topic if it doesn't exist
        /// </summary>
        private async Task CreateTopicAsync(string topicName, CancellationToken cancellationToken)
        {
            using var adminClient = new AdminClientBuilder(
                new AdminClientConfig { BootstrapServers = _kafkaOptions.BootstrapServers }
            ).Build();

            try
            {
                await adminClient.CreateTopicsAsync(
                    new[]
                    {
                        new TopicSpecification
                        {
                            Name = topicName,
                            NumPartitions = 1,
                            ReplicationFactor = 1,
                        },
                    }
                );
                _logger.LogInformation("Topic {TopicName} created successfully.", topicName);
            }
            catch (CreateTopicsException createTopicsException)
            {
                if (
                    createTopicsException.Results.Any(r =>
                        r.Error.Code == ErrorCode.TopicAlreadyExists
                    )
                )
                {
                    _logger.LogInformation("Topic {TopicName} already exists.", topicName);
                    return;
                }
                _logger.LogError(
                    "Error creating topic {TopicName}: {ErrorReason}",
                    topicName,
                    createTopicsException.Results[0].Error.Reason
                );
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating topic {TopicName}", topicName);
                throw;
            }
        }

        /// <summary>
        /// Processes a Kafka message
        /// </summary>
        private async Task ProcessMessageAsync(
            ConsumeResult<Ignore, string> consumeResult,
            CancellationToken cancellationToken
        )
        {
            try
            {
                // Parse the message
                var message = JsonNode.Parse(consumeResult.Message.Value);

                if (message == null)
                {
                    _logger.LogWarning(
                        "Received invalid message format: parsed JSON message is null. Topic: {Topic}, Partition: {Partition}, Offset: {Offset}",
                        consumeResult.TopicPartitionOffset.Topic,
                        consumeResult.TopicPartitionOffset.Partition,
                        consumeResult.TopicPartitionOffset.Offset
                    );

                    await SendToDLQAsync(
                        consumeResult,
                        "DeserializationError",
                        "Parsed JSON message is null"
                    );
                    return;
                }

                var eventTypeNode = message["EventType"];

                if (eventTypeNode == null)
                {
                    _logger.LogWarning(
                        "Received invalid message format: missing EventType. Topic: {Topic}, Partition: {Partition}, Offset: {Offset}",
                        consumeResult.TopicPartitionOffset.Topic,
                        consumeResult.TopicPartitionOffset.Partition,
                        consumeResult.TopicPartitionOffset.Offset
                    );

                    await SendToDLQAsync(
                        consumeResult,
                        "DeserializationError",
                        "Missing EventType in message"
                    );
                    return;
                }

                var eventType = Enum.Parse<EventTypes>(eventTypeNode.ToString());

                var payload = message["Payload"];

                if (payload == null)
                {
                    _logger.LogWarning(
                        "Received invalid message format: missing Payload. Topic: {Topic}, Partition: {Partition}, Offset: {Offset}",
                        consumeResult.TopicPartitionOffset.Topic,
                        consumeResult.TopicPartitionOffset.Partition,
                        consumeResult.TopicPartitionOffset.Offset
                    );

                    await SendToDLQAsync(
                        consumeResult,
                        "DeserializationError",
                        "Missing Payload in message"
                    );
                    return;
                }

                // Create the event message
                var eventMessage = new EventMessage(eventType, payload);

                // Try to invoke the specific handler first
                bool handled = false;

                // Get the handler type based on the event type
                Type? handlerType = null;
                switch (eventType)
                {
                    case EventTypes.UserPasswordChangeEvent:
                        handlerType = typeof(UserPasswordChangeEvent);
                        break;
                    case EventTypes.UserCreationEvent:
                        handlerType = typeof(UserCreationEvent);
                        break;
                    case EventTypes.UserUpdateEvent:
                        handlerType = typeof(UserUpdateEvent);
                        break;
                    default:
                        _logger.LogWarning("Unknown event type: {EventType}", eventType);
                        break;
                }

                // If we have a handler for this event type, try to invoke it
                if (handlerType != null && _eventHandlers.TryGetValue(handlerType, out var handler))
                {
                    try
                    {
                        // Deserialize the payload to the specific event type
                        var typedEvent = payload.Deserialize(handlerType);

                        if (typedEvent != null)
                        {
                            // Invoke the handler
                            var result = handler.DynamicInvoke(typedEvent);
                            if (result is Task task)
                            {
                                await task;
                            }
                            handled = true;

                            _logger.LogInformation(
                                "Successfully processed message with specific handler. Topic: {Topic}, Partition: {Partition}, Offset: {Offset}, EventType: {EventType}",
                                consumeResult.TopicPartitionOffset.Topic,
                                consumeResult.TopicPartitionOffset.Partition,
                                consumeResult.TopicPartitionOffset.Offset,
                                eventType
                            );
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "Error invoking specific handler for event type {EventType}. Topic: {Topic}, Partition: {Partition}, Offset: {Offset}",
                            eventType,
                            consumeResult.TopicPartitionOffset.Topic,
                            consumeResult.TopicPartitionOffset.Partition,
                            consumeResult.TopicPartitionOffset.Offset
                        );

                        // Check if we should retry or send to DLQ
                        if (!await HandleProcessingErrorAsync(consumeResult, ex))
                        {
                            return; // Message sent to DLQ or will be retried
                        }
                    }
                }

                // If not handled by a specific handler, try the generic handler
                if (!handled && _genericHandler != null)
                {
                    try
                    {
                        await _genericHandler(eventMessage);

                        _logger.LogInformation(
                            "Successfully processed message with generic handler. Topic: {Topic}, Partition: {Partition}, Offset: {Offset}, EventType: {EventType}",
                            consumeResult.TopicPartitionOffset.Topic,
                            consumeResult.TopicPartitionOffset.Partition,
                            consumeResult.TopicPartitionOffset.Offset,
                            eventType
                        );
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "Error invoking generic handler for event type {EventType}. Topic: {Topic}, Partition: {Partition}, Offset: {Offset}",
                            eventType,
                            consumeResult.TopicPartitionOffset.Topic,
                            consumeResult.TopicPartitionOffset.Partition,
                            consumeResult.TopicPartitionOffset.Offset
                        );

                        // Check if we should retry or send to DLQ
                        if (!await HandleProcessingErrorAsync(consumeResult, ex))
                        {
                            return; // Message sent to DLQ or will be retried
                        }
                    }
                }

                // If we get here, the message was processed successfully or there was no handler
                // Commit the offset
                try
                {
                    _consumer.Commit(consumeResult);
                    _logger.LogDebug(
                        "Committed offset. Topic: {Topic}, Partition: {Partition}, Offset: {Offset}",
                        consumeResult.TopicPartitionOffset.Topic,
                        consumeResult.TopicPartitionOffset.Partition,
                        consumeResult.TopicPartitionOffset.Offset
                    );
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Error committing offset. Topic: {Topic}, Partition: {Partition}, Offset: {Offset}",
                        consumeResult.TopicPartitionOffset.Topic,
                        consumeResult.TopicPartitionOffset.Partition,
                        consumeResult.TopicPartitionOffset.Offset
                    );
                }
            }
            catch (JsonException jex)
            {
                _logger.LogError(
                    jex,
                    "Error parsing message JSON. Topic: {Topic}, Partition: {Partition}, Offset: {Offset}. Message: {MessageValue}",
                    consumeResult.TopicPartitionOffset.Topic,
                    consumeResult.TopicPartitionOffset.Partition,
                    consumeResult.TopicPartitionOffset.Offset,
                    consumeResult.Message.Value
                );

                await SendToDLQAsync(
                    consumeResult,
                    "DeserializationError",
                    jex.Message,
                    jex.StackTrace
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Unexpected error processing message. Topic: {Topic}, Partition: {Partition}, Offset: {Offset}",
                    consumeResult.TopicPartitionOffset.Topic,
                    consumeResult.TopicPartitionOffset.Partition,
                    consumeResult.TopicPartitionOffset.Offset
                );

                await HandleProcessingErrorAsync(consumeResult, ex);
            }
        }

        /// <summary>
        /// Handles a processing error by either retrying or sending to DLQ
        /// </summary>
        /// <returns>True if the error was handled and processing should continue, false otherwise</returns>
        private async Task<bool> HandleProcessingErrorAsync(
            ConsumeResult<Ignore, string> consumeResult,
            Exception ex
        )
        {
            // Get the current retry count for this message
            int retryCount = _messageRetryCount.AddOrUpdate(
                consumeResult.TopicPartitionOffset,
                1, // Initial value if key doesn't exist
                (key, existingCount) => existingCount + 1 // Increment existing value
            );

            if (retryCount <= _kafkaOptions.DLQMaxRetries)
            {
                _logger.LogWarning(
                    "Processing error for message. Retry {RetryCount}/{MaxRetries}. Topic: {Topic}, Partition: {Partition}, Offset: {Offset}",
                    retryCount,
                    _kafkaOptions.DLQMaxRetries,
                    consumeResult.TopicPartitionOffset.Topic,
                    consumeResult.TopicPartitionOffset.Partition,
                    consumeResult.TopicPartitionOffset.Offset
                );

                // We'll retry this message next time it's consumed
                return false;
            }
            else
            {
                // Max retries reached, send to DLQ
                _logger.LogError(
                    ex,
                    "Max retries ({MaxRetries}) reached for message. Topic: {Topic}, Partition: {Partition}, Offset: {Offset}. Sending to DLQ.",
                    _kafkaOptions.DLQMaxRetries,
                    consumeResult.TopicPartitionOffset.Topic,
                    consumeResult.TopicPartitionOffset.Partition,
                    consumeResult.TopicPartitionOffset.Offset
                );

                await SendToDLQAsync(
                    consumeResult,
                    "ProcessingFailureMaxRetries",
                    ex.Message,
                    ex.StackTrace
                );

                return false;
            }
        }

        /// <summary>
        /// Sends a message to the Dead Letter Queue
        /// </summary>
        private async Task SendToDLQAsync(
            ConsumeResult<Ignore, string> consumeResult,
            string failureReason,
            string errorMessage,
            string? stackTrace = null
        )
        {
            // Create DLQ message
            var dlqMessage = new DLQMessage
            {
                OriginalTopic = consumeResult.Topic,
                OriginalPartition = consumeResult.Partition.Value,
                OriginalOffset = consumeResult.Offset.Value,
                FailureReason = failureReason,
                ErrorMessage = errorMessage,
                StackTrace = stackTrace,
                OriginalMessagePayload = consumeResult.Message?.Value ?? string.Empty,
            };

            // Send to DLQ
            bool dlqPublishSuccess = await _dlqProducerService.ProduceAsync(dlqMessage);

            if (dlqPublishSuccess)
            {
                // If DLQ publish is successful, commit the original message's offset
                _consumer.Commit(consumeResult);

                // Reset retry count for this message
                _messageRetryCount.TryRemove(consumeResult.TopicPartitionOffset, out _);

                _logger.LogInformation(
                    "Message sent to DLQ and original offset committed. Topic: {Topic}, Partition: {Partition}, Offset: {Offset}",
                    consumeResult.TopicPartitionOffset.Topic,
                    consumeResult.TopicPartitionOffset.Partition,
                    consumeResult.TopicPartitionOffset.Offset
                );
            }
            else
            {
                _logger.LogCritical(
                    "Failed to publish message to DLQ. Original offset NOT committed. Topic: {Topic}, Partition: {Partition}, Offset: {Offset}",
                    consumeResult.TopicPartitionOffset.Topic,
                    consumeResult.TopicPartitionOffset.Partition,
                    consumeResult.TopicPartitionOffset.Offset
                );
            }
        }

        /// <summary>
        /// Disposes the consumer
        /// </summary>
        public void Dispose()
        {
            try
            {
                _cts?.Cancel();
                _consumer?.Unsubscribe();
                _consumer?.Dispose();
                _consumer = null!;
                _logger.LogInformation("EventDrivenConsumerService disposed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disposing EventDrivenConsumerService");
            }
        }
    }
}
