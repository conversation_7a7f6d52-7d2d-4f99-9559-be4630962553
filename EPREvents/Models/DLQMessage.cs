using System;
using System.Text.Json.Serialization;

namespace EPREvents.Models
{
    /// <summary>
    /// Represents a message that will be sent to the Dead Letter Queue (DLQ)
    /// </summary>
    public class DLQMessage
    {
        /// <summary>
        /// The original topic from which the message was consumed
        /// </summary>
        [JsonPropertyName("originalTopic")]
        public required string OriginalTopic { get; set; }

        /// <summary>
        /// The original partition from which the message was consumed
        /// </summary>
        [Json<PERSON>ropertyName("originalPartition")]
        public int OriginalPartition { get; set; }

        /// <summary>
        /// The original offset of the message in the partition
        /// </summary>
        [JsonPropertyName("originalOffset")]
        public long OriginalOffset { get; set; }

        /// <summary>
        /// The timestamp when the failure occurred (UTC)
        /// </summary>
        [JsonPropertyName("failureTimestampUTC")]
        public DateTime FailureTimestampUTC { get; set; }

        /// <summary>
        /// The reason for the failure (e.g., "DeserializationError", "ProcessingErrorMaxRetries")
        /// </summary>
        [JsonPropertyName("failureReason")]
        public required string FailureReason { get; set; }

        /// <summary>
        /// The error message from the exception
        /// </summary>
        [JsonPropertyName("errorMessage")]
        public required string ErrorMessage { get; set; }

        /// <summary>
        /// The stack trace of the exception (optional)
        /// </summary>
        [JsonPropertyName("stackTrace")]
        public string? StackTrace { get; set; }

        /// <summary>
        /// The original message payload as a string
        /// </summary>
        [JsonPropertyName("originalMessagePayload")]
        public required string OriginalMessagePayload { get; set; }

        /// <summary>
        /// Creates a new DLQMessage
        /// </summary>
        public DLQMessage()
        {
            FailureTimestampUTC = DateTime.UtcNow;
        }
    }
}
