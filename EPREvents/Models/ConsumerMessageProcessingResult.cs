using Confluent.Kafka;
using EPREvents.Events;

namespace EPREvents.Models
{
    public class ConsumerMessageProcessingResult
    {
        public EventMessage? EventMsg { get; }
        public ConsumeResult<Ignore, string> KafkaConsumeResult { get; }
        public bool DeserializationSuccessful { get; }
        public Exception? DeserializationException { get; } // To hold JsonException etc.

        public ConsumerMessageProcessingResult(EventMessage? eventMsg, ConsumeResult<Ignore, string> kafkaResult, bool success, Exception? exception = null)
        {
            EventMsg = eventMsg;
            KafkaConsumeResult = kafkaResult;
            DeserializationSuccessful = success;
            DeserializationException = exception;
        }
    }
}
