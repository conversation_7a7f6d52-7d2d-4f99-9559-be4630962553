using System.Threading.Tasks;
using EPREvents.Models;

namespace EPREvents.Interfaces
{
    /// <summary>
    /// Interface for a service that produces messages to a Dead Letter Queue (DLQ)
    /// </summary>
    public interface IDLQProducerService
    {
        /// <summary>
        /// Produces a message to the Dead Letter Queue
        /// </summary>
        /// <param name="dlqMessage">The message to send to the DLQ</param>
        /// <returns>A task that completes when the message has been sent to the DLQ</returns>
        Task<bool> ProduceAsync(DLQMessage dlqMessage);
    }
}
