using System;
using System.Threading;
using System.Threading.Tasks;
using EPREvents.Events;

namespace EPREvents.Interfaces
{
    /// <summary>
    /// Interface for an event-driven Kafka consumer service that uses Confluent.Kafka's event-based pattern
    /// </summary>
    public interface IEventDrivenConsumerService
    {
        /// <summary>
        /// Starts consuming messages from the specified topic
        /// </summary>
        /// <param name="topic">The Kafka topic to consume from</param>
        /// <param name="cancellationToken">Cancellation token to stop the consumer</param>
        /// <returns>A task that completes when the consumer has started</returns>
        Task StartAsync(string topic, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Stops the consumer
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>A task that completes when the consumer has stopped</returns>
        Task StopAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Registers a handler for a specific event type
        /// </summary>
        /// <typeparam name="TEvent">The type of event to handle</typeparam>
        /// <param name="handler">The handler function that processes the event</param>
        void RegisterEventHandler<TEvent>(Func<TEvent, Task> handler) where TEvent : class;
        
        /// <summary>
        /// Registers a generic handler for all event messages
        /// </summary>
        /// <param name="handler">The handler function that processes any event message</param>
        void RegisterGenericHandler(Func<EventMessage, Task> handler);
    }
}
