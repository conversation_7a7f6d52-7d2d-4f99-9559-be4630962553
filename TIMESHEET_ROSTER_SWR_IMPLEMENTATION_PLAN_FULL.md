# Timesheet Roster SWR – **Full-Stack Implementation Plan**

_Last updated: 2025-08-05_

This plan merges insights from both internal reviews (`code-review-windsurf.md` & `code-review-cursor-gk4.md`). It is intentionally verbose and self-contained so an engineer **new to the code-base** can understand the why and the how without jumping between docs.

---

## 1. Background & Problem Statement

The Timesheet Roster feature relies on a **stale-while-revalidate (SWR)** strategy implemented on top of **Relay**. Two major pain-points surfaced:

1. **TTL Ignored / Chatty Network** – A 30-second TTL (`REVALIDATE_MS`) should prevent redundant fetches, but per-operation timestamps occasionally fail to persist, causing revalidation every reload.
2. **Pagination Loss** – When background fetches arrive, Relay’s default connection handler replaces `edges`, wiping earlier pages.

Secondary issues identified during reviews:

* Hash comparison too sensitive (volatile fields)
* Limited connection detection (hard-coded list)
* Insufficient debug clarity / naming
* Unit & integration test gaps (concurrency, quota errors, hash collision, etc.)

---

## 2. Business & Engineering Goals

| ID | Goal | KPI / Definition of Done |
|----|------|--------------------------|
| G1 | Honour 30-second TTL across reloads | Reload <30 s after fetch results in **0** network requests (DevTools) |
| G2 | Preserve pagination slices after revalidate | After navigating to page 3, revalidate → still on page 3, `edges.length` unchanged |
| G3 | Reduce no-op publishes | Identical payloads do **not** trigger `commitUpdate` or UI re-render |
| G4 | Observability & DX | Debug logs readable; failure modes surfaced to Sentry |
| G5 | Robust test coverage | Jest unit + Playwright e2e for all critical paths |

---

## 3. Architectural Overview (SWR flow)

```mermaid
sequenceDiagram
  User->>UI: Load Timesheet page
  UI->>Relay Store: readQuery(store-only)
  alt Cache Hit & Fresh
      UI-->>User: render cached data
  else Stale OR Missing
      UI->>GraphQL: fetchQuery(network-only)
  end
  note over UI,Relay Store: Background revalidation scheduled if TTL expired
```

### Key Actors
* `ui/src/relay/useSWRQuery.ts` – custom hook orchestrating SWR
* `ui/src/relay/createPersistedStore.ts` – IndexedDB cache & per-op timestamps
* `ui/src/relay/cacheFilters.ts` – selective persistence, edge merge helper

---

## 4. Implementation Tasks (by File)

Paths **relative to repo root** `/home/<USER>/repos/eprlive24/subscriptions`.

### 4.1  `ui/src/relay/createPersistedStore.ts`

| Task | Lines / Function | Notes |
|------|------------------|-------|
| 1. Convert debounce → immediate `flushNow()` when timestamps updated | `notifyStoreUpdated`, `flushNow` | Already staged ✓ |
| 2. **Guard initialise**: export `ensurePersistenceReady()` that returns a promise resolved after callbacks wired | NEW | Prevent early `notifyStoreUpdated()` null-callback risk (windsurf) |
| 3. Handle IndexedDB quota errors in tests | `persist()` | Add flag to simulate quota exceeded |
| 4. Unit tests for flushNow in Jest-jsdom | tests | Provide mock for `navigator.sendBeacon` |

### 4.2  `ui/src/relay/useSWRQuery.ts`

| Task | Lines | Notes |
|------|-------|-------|
| 5. **Fix Snapshot Bug** – capture edges inside `environment.commitUpdate()` so `rootRecordProxy` is a `RecordProxy` (windsurf critical)  | replace code in lines ~340-415 | Could also call `store.getSource().get()` but RecordProxy safer. |
| 6. De-dup reflection scan cost: wrap `source.toJSON()` behind `if (NODE_ENV==='development')` or memoise | perf |
| 7. Update all enum usages: replace `network_first_policy` with `forced_network_due_to_missing_timestamp` across repo | grep |
| 8. Expand `stableBusinessFields` into constant `STABLE_EDGE_FIELDS` in `connectionUtils.ts` | DRY |
| 9. Concurrency guard: track in-flight revalidation per cacheKey to avoid duplicate snapshots | new Map |

### 4.3  `ui/src/relay/cacheFilters.ts`

| Task | Notes |
|------|-------|
| 10. Optionally remove unused `store` param or document future GC use.
| 11. Add metrics counter for merge duration & edges processed (perf profiling).

### 4.4  `ui/src/relay/debug.ts`

| Task | Notes |
|------|-------|
| 12. Ensure new `CacheMissReason` members are handled wherever switch-cases exist (`analytics.ts`, etc.)
| 13. Tree-shaking: wrap heavy helpers in `if(process.env.NODE_ENV!=='production')`

### 4.5  **New helper** `ui/src/relay/connectionUtils.ts`

* Contains `discoverConnectionFields(store: Store): string[]`
* Exports `STABLE_EDGE_FIELDS` constant.

### 4.6  Tests

| Layer | File | Scenario |
|-------|------|----------|
| Unit | `ui/src/relay/__tests__/edgeMerge.test.ts` | 1k existing edges + 50 new → time < 50 ms, memory < 2× baseline |
| Unit | `ui/src/relay/__tests__/persistenceQuota.test.ts` | Simulate quota error and verify essential-records retry |
| Unit | `ui/src/relay/__tests__/hashCollision.test.ts` | Force collision → ensure fallbacks replace edges |
| Integration (Jest/jsdom) | `ui/src/relay/__tests__/concurrentRevalidate.test.ts` | Two queries revalidate simultaneously, timestamps correctly updated once |
| E2E (Playwright) | `e2e/swr-pagination.spec.ts` | Navigate→page 3→reload (<30 s)→no network call & still on page 3 |

---

## 5. Timeline & Milestones

| Day | Deliverable |
|-----|-------------|
| 0 | Kick-off, branch `feat/swr-edge-preservation` |
| 1 | Tasks 1-4 finished, CI green |
| 2 | Implement snapshot bug fix (Task 5) + refactor constants (Task 8) |
| 3 | Finish Tasks 6-9, run `tsc --noEmit` and Jest suite |
| 4 | Add new helper + cacheFilters metrics (Tasks 10-11) |
| 5 | Finish debug updates (Task 12-13); write/green unit tests |
| 6 | Integrations & Playwright e2e; verify in staging behind flag `VITE_SWR_EDGE_PRESERVATION_V2` |
| 7 | Observability review, metrics dashboards, docs │
| 8 | Gradual rollout 10% → 100%; monitor | 

---

## 6. Risks & Mitigations

| Risk | Mitigation |
|------|-----------|
| Snapshot merge memory blow-up | Cap merged edges at 2× `first` arg; slice oldest edges |
| Hash collision false negative | Fallback to merge-then-replace on visual diff metric (edge length change >50%) |
| IndexedDB unavailable (Safari PP) | Persist in-memory only; surface warning toast |
| Test flakiness with timers | Prefer async/await over `runAllTimers` |

---

## 7. Verification Checklist

- [ ] Reload <30 s shows **0** network requests (DevTools)  
- [ ] Navigate to page 3, revalidate, still on page 3  
- [ ] Jest unit & integration tests green  
- [ ] Playwright e2e green  
- [ ] Sentry shows <0.5% persistence errors  
- [ ] Payload hashes stable over identical responses  

---

## 8. Rollback Strategy

1. Toggle env flag `VITE_SWR_EDGE_PRESERVATION_V2="0"` → reverts to legacy behaviour.  
2. If critical, revert PR and deploy previous container image.  
3. IndexedDB can be cleared via version bump in `CACHE_VERSION`.

---

## 9. Further Reading

* `TIMESHEET_ROSTER_SWR_VALIDATION.md` – original design vs reality
* `code-review-windsurf.md`, `code-review-cursor-gk4.md` – detailed critiques
* Relay docs – [Connection spec](https://relay.dev/docs/guides/connections/)  

---

© 2025 EPR Live Engineering
