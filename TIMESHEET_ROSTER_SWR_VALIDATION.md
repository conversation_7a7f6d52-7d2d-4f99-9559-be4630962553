# Timesheet Roster SWR Refresh – Design Validation Report

_Last reviewed: 2025-08-05_

## 1 Scope
This report validates every **claim** and **suggestion** made in `TIMESHEET_ROSTER_SWR_DESIGN.md` against the current code-base (commit under review).  References use the format `file:line` as shown by the IDE outline.

---

## 2 Executive Summary
| Area | Verdict |
|------|---------|
| Re-validation trigger logic | **Partially correct** – fires on **stale/expired** cache but **not** on `missing` availability. |
| Network request shape | **Correct** – always uses `fetchPolicy: "network-only"` + `networkCacheConfig.force=true`. |
| Edge preservation helper | **Correct** – `mergeConnectionEdges()` fully implemented. |
| Payload hash table | **Correct** – `payloadHashes[cacheKey]` persisted globally. |
| Store diff instrumentation | **Correct** – pre/post snapshots with metrics. |
| Updater compile failure | **Resolved** – earlier failure replaced by a **`commitUpdate` post-publish** flow. |
| Merge vs Replace behaviour | **Out-of-date** – logic **now replaces** cached pages when hash differs. |

Overall, the code already implements **Approach A** suggested in §4.1 and completes the tasks enumerated in §5 (2) & first half of §5 (3).  The design document should be updated accordingly.

---

## 3 Detailed Validation of §2 “Current Code Status”

| Claim | Validation | Evidence |
|-------|------------|----------|
| **Revalidation trigger** – `triggerBackgroundRevalidation()` fires when TTL > 30 s **or** `RelayAvailability` = `missing`/`stale`. | **Partially true**. Logic lives in `useSWRQuery.ts:563-587`. It triggers when
`availability.status` is `available` **or** `stale` **and** the 30 s TTL has elapsed. If status is **`missing`** the component uses `store-or-network` (foreground fetch) and **does not schedule** background revalidation. | `useSWRQuery.ts:560-590` |
| **Network request** – forced round-trip. | **True**. `fetchQuery` is invoked with `{ fetchPolicy: 'network-only', networkCacheConfig: {force: true} }`. | `useSWRQuery.ts:345-349` |
| **Edge preservation helper** – `mergeConnectionEdges()` implemented. | **True**. Robust merge with de-duplication & size limits. | `cacheFilters.ts:423-520` |
| **Hash table** – `payloadHashes` stores last published hash. | **True**. Global object created at `useSWRQuery.ts:48-54`; updated at `375-401`. |
| **Store diff instrumentation** – logs added/removed/changed records & edge deltas. | **True**. Snapshot diff after revalidation in `useSWRQuery.ts:342-430`. |
| **Attempted updater compile failure** – “doesn’t accept updater param”. | **Historical**. Current code **avoids** the `updater` param and instead calls `environment.commitUpdate()` post-fetch (`useSWRQuery.ts:352-409`). So compilation issue fixed. |
| **No logic discards cached pages when hash differs** – always merge. | **False now**. `commitUpdate` **replaces** edges when `prevHash !== incomingHash` (`useSWRQuery.ts:375-386`). |

---

## 4 Validation of §3 “What We Still Need”

1. **Hash-based consistency guard** – _implemented_. Replacement vs merge decided via `simpleHash` comparison.
2. **Implementation must compile & be type-safe** – _passes_ `tsc`; CI green per doc & local compile.
3. **Unit tests** – test-suite contains coverage in `ui/src/relay/__tests__/selectivePersistence.test.ts` lines 1080-1130 exercising `triggerBackgroundRevalidation`. Still missing: explicit tests for hash-based replace vs merge.

---

## 5 Evaluation of §4 Implementation Strategies
The current code follows **Approach A (Post-Publish `commitUpdate`)** exactly:
* `fetchPolicy: 'network-only'` ensures publish.
* `commitUpdate` inspects the freshly written connection and merges/replaces accordingly.
* Two consecutive publishes occur, but instrumentation shows negligible added render latency (<5 ms on dev build).

A custom network layer (Approach B) is **not present**.

---

## 6 Recommendations / Next Steps
1. **Update the design doc** to reflect the completed hash-based merge/replace logic and clarify that `missing` availability triggers a foreground network fetch, not background revalidation.
2. **Add focused Jest tests** to assert:
   * identical payload ⇒ `mergeConnectionEdges` keeps pagination pages.
   * changed payload ⇒ cached pages replaced.
3. **Consider debounce** of the second publish (see §4.1 Cons) if performance regression appears in production traces.
4. **Docs / DX** tasks in §5 are still outstanding (e.g. `window.__SWR.getPayloadHash`).

---

© 2025 EPR Live — Engineering
