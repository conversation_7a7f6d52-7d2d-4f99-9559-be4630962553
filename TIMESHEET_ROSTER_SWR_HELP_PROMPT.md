# Prompt for AI Assistant: Investigate Incorrect Timestamp Updates during SWR Background Revalidation

## Context
We are working in a Relay + React code-base that implements a custom **stale-while-revalidate (SWR)** hook (`useSWRQuery`).  A per-operation timestamp map (`window.__RELAY_OP_SAVED_AT__`) is used to decide when cached queries are stale.  The background revalidation _should_ only update the timestamp **after a real network fetch**.

After our latest changes the timestamp for `TimesheetRosterQuery` is still being updated even when the background revalidation never leaves the browser (i.e. Relay serves the data directly from the store).  This resets the TTL clock erroneously and prevents future true refreshes.

### Relevant Files (paths are relative to repo root)
| File | Purpose |
|---|---|
| `ui/src/relay/useSWRQuery.ts` | Main SWR hook.  Background revalidation logic lives in `triggerBackgroundRevalidation()` (≈ lines 230-350).  Timestamp is written in the same function. |
| `ui/src/relay/cacheFilters.ts` | Contains `mergeConnectionEdges()` helper (not yet wired). |
| `ui/src/relay/createPersistedStore.ts` | Exposes global `__RELAY_OP_SAVED_AT__` when the store is hydrated. |

### Key Code Snippet (`useSWRQuery.ts`)
```ts
// inside triggerBackgroundRevalidation()
await fetchQuery(environment, requestNode, revalidationVariables, {
  networkCacheConfig: { force: true },
  fetchPolicy: 'store-or-network'
}).toPromise();

const networkDuration = Date.now() - networkStart;
// … debug logs …

// Timestamp update – SHOULD run only after a genuine network payload
if (window.__RELAY_OP_SAVED_AT__) {
  window.__RELAY_OP_SAVED_AT__[cacheKey] = Date.now();
}
```
With `fetchPolicy: 'store-or-network'` Relay will **return cached data first** when it is fully available, _ignoring_ the `force` flag; in that case no HTTP request is issued.

### Observed Dev-Console Logs
```
🌐 [SWR] Revalidation fetch (store-or-network) initiated …
✅ [SWR] Background fetch outcome … { durationMs: 1, source: 'store' }
⚠️ Network request likely skipped because fetchPolicy 'store-or-network' returned cached data …
📝 Per-op timestamp write … timestamp: 2025-08-05T02:05:16.496Z
```
Note the 1 ms duration and `source: 'store'` → confirms no network fetch, yet the timestamp is written right afterwards.

## Task for the AI Assistant
1. **Root-cause** exactly _why_ the timestamp write still runs when the data came from the store.  (We suspect that the code does not branch based on `source` or any Relay API that indicates a network payload.)
2. **Propose the safest fix**, keeping the other SWR guarantees intact:
   * Option A: change the revalidation call back to `fetchPolicy: 'network-only'`; _or_
   * Option B: keep `store-or-network` but detect cache hits (e.g. via `networkDuration`, `response.extensions`, or Relay Network observable) and skip the timestamp update.
3. Suggest minimal code changes (file + line numbers) and any additional debug instrumentation that would make the go/no-go decision crystal-clear.
4. Ensure recommendations follow the existing coding guidelines (React + Relay + pnpm, TypeScript).

### Deliverables Expected from You
* A technical explanation of the root cause.
* A concise step-by-step patch outline (no code required yet, just what to change where).
* If helpful, a note on whether to hook up `mergeConnectionEdges()` as an `updater` in the same patch or a follow-up.

Please respond in markdown.
