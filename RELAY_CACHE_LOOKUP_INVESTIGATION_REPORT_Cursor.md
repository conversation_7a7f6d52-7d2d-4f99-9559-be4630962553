# Relay Cache Lookup Investigation – Root Cause Analysis & Fix (Cursor)

## 1. Executive Summary
<PERSON><PERSON>’s cache persistence layer successfully writes and restores >6 K records from IndexedDB, yet every GraphQL query still goes to the network.  A deep code review shows that the root record `client:root` is **explicitly excluded from persistence**, so after hydration the Relay store lacks the graph entry-point required to resolve any query.  With the root record missing, <PERSON><PERSON> classifies every operation as a cache miss; therefore `store-or-network` behaves exactly like `network-only`, and the UI cannot operate offline.

## 2. Key Findings
* `ui/src/relay/cacheFilters.ts` denies persistence of the root record:
```168:174:ui/src/relay/cacheFilters.ts
// (a) Always exclude <PERSON><PERSON>'s root record (auth issue prevention)
if (dataID === 'client:root') {
  return [false, 'denied_client_root'];
}
```
* The root record is the sole owner of the **query root fields** that point to every connection/object in the graph.  When it is absent, <PERSON><PERSON>’s internal `check()` fails and the operation is considered **missing**, even if all child records exist.
* All other persistence code (`createPersistedStore`, `initRelayEnvironment`, React provider) is correct – the hydrated store is the same instance that components query.
* Connection edge trimming, size limits etc. are safe; only the root-record denylist breaks look-ups.

## 3. Answers to the Prompt Questions
1. **Root Cause – Why cache miss with 100 % probability?**
   Because the root record is never persisted.  When Relay checks the store it cannot find the root field payloads, so it marks the query as **not available** and fetches from the network, regardless of the presence of child entity records.

2. **Store Integration – How can data load yet be invisible?**
   `createPersistedStore` correctly loads the JSON blob into a `RecordSource`, but that blob lacks `client:root`.  Relay’s lookup starts at `client:root`; since that key is undefined, the rest of the graph is unreachable, making the data “invisible”.

3. **Cache Key Analysis – Possible key mismatch?**
   Key generation itself is fine; the records use the standard IDs.  The mismatch is that **one critical key is deliberately omitted**, not mis-generated.

4. **Fetch Policy Behavior – Why does `store-or-network` act like `network-only`?**
   The policy first calls `environment.check(operation)`.  Because the root record is missing, `check()` returns `missing`; thus Relay proceeds directly to the network path, indistinguishable from `network-only`.

5. **Offline Failure – Why does the UI die when backend is down?**
   Offline mode relies on cached queries resolving.  Without the root record every query fails its availability check, so no data can be read and the UI cannot render.

## 4. Specific Fix
1. **Persist `client:root`** while still protecting sensitive sub-fields.
   * Replace the unconditional deny rule with a **sanitiser** that strips auth/PII fields but keeps query root payloads.
```diff
- if (dataID === 'client:root') {
-   return [false, 'denied_client_root'];
- }
+ if (dataID === 'client:root') {
+   const safeRecord = { ...record } as Record<string, unknown>;
+   delete safeRecord["userInfo"];        // remove PII
+   delete safeRecord["authError"];
+   return [true, 'allowed_default', safeRecord];
+ }
```
2. Bump `CACHE_VERSION` so existing corrupted blobs are discarded and rebuilt.
3. Deploy; on next run the store will persist & hydrate the root record, enabling cache hits.

## 5. Validation Plan
1. Clear IndexedDB (or bump version) and reload app to rebuild cache.
2. Run a typical workflow online; confirm logs show `💾 Persisted …` with `client:root` count 1.
3. Reload page – expect queries to log `fromCache: true`, `cacheStatus: 'HIT'`.
4. Shut down backend; reload again – UI should render fully from cache.
5. Enable `window.__DEBUG_CACHE_DELAY__` and verify that load completes during the 10 s artificial delay (proves cache HITS before network path).
6. Run automated jest test `relay-cache-offline.test.ts` (if present) to assert `environment.check()` returns `available` for critical operations.

## 6. Prevention Strategy
* Add unit test that serialises a populated store, reloads it, and asserts `environment.check()` is `available` for a representative suite of queries.
* Introduce a **whitelist override**: never deny `client:root`; instead scrub sensitive sub-fields.  Document this invariant in `cacheFilters.ts`.
* Integrate a CI lint that warns if a filter rule excludes `client:root`.
* Extend debug tooling: during persistence log a fatal warning if attempting to save child records while omitting their owner root record.

---
Report generated by Cursor AI – 2025-08-04
