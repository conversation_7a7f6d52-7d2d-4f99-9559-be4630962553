diff --git a/ui/src/relay/useSWRQuery.ts b/ui/src/relay/useSWRQuery.ts
index 71dcdd8a..e8d88dd9 100644
--- a/ui/src/relay/useSWRQuery.ts
+++ b/ui/src/relay/useSWRQuery.ts
@@ -1,5 +1,5 @@
 import { useLazyLoadQuery, GraphQLTaggedNode, useRelayEnvironment, fetchQuery } from 'react-relay';
-import type { OperationType, FetchPolicy } from 'relay-runtime';
+import type { OperationType, FetchPolicy, RecordProxy, RecordSourceSelectorProxy } from 'relay-runtime';
 import { createOperationDescriptor, getRequest, ConnectionHandler } from 'relay-runtime';
 import { relayObservability } from './observability';
 import { useEffect, useRef, useState } from 'react';
@@ -31,7 +31,7 @@ function simpleHash(str: string): string {
     if (str.length === 0) return hash.toString(36);
     for (let i = 0; i < str.length; i++) {
         const char = str.charCodeAt(i);
-        hash = ((hash << 5) - hash) + char;
+        hash = (hash << 5) - hash + char;
         hash = hash & hash; // Convert to 32bit integer
     }
     return Math.abs(hash).toString(36);
@@ -104,7 +104,7 @@ function extractQueryName(query: GraphQLTaggedNode): string {
 async function analyzeStoreForQuery(
     queryDoc: GraphQLTaggedNode,
     queryName: string,
-    variables: Record<string, unknown>
+    variables: Record<string, any>
 ): Promise<QueryAnalysis> {
     try {
         // Get current Relay store state
@@ -194,12 +194,11 @@ async function analyzeStoreForQuery(
     }
 }
 
-
 /**
  * Create a cache key for revalidation tracking
  * FIXED: Now uses stable serialization to ensure cache key consistency
  */
-function createCacheKey(queryName: string, variables: Record<string, unknown>): string {
+function createCacheKey(queryName: string, variables: Record<string, any>): string {
     // Create a stable key from query name and variables using deterministic serialization
     // This ensures the same cache key is generated regardless of object property ordering
     const variablesKey = stableStringify(variables);
@@ -214,7 +213,7 @@ function shouldRevalidate(cacheKey: string, revalidationCooldown: number = REVAL
     if (!lastRevalidation) {
         return true; // Never revalidated
     }
-    
+
     return Date.now() - lastRevalidation > revalidationCooldown;
 }
 
@@ -236,52 +235,52 @@ async function triggerBackgroundRevalidation<T extends OperationType>(
 ): Promise<void> {
     try {
         const environment = await getRelayEnvironment();
-        
+
         if (process.env.NODE_ENV === 'development') {
             debug.log(`🔄 Starting background revalidation for ${queryName}`, {
                 cacheKey,
                 timestamp: new Date().toISOString()
             });
         }
-        
-        // Perform a true network-only revalidation (bypass store entirely)
+
+        // Perform a network-only revalidation to guarantee a fresh payload; updater will merge or replace edges accordingly
         const requestNode = getRequest(query);
         const networkStart = Date.now();
         if (process.env.NODE_ENV === 'development') {
-            debug.log(`🌐 [SWR] Network-only fetch initiated for ${queryName}`, { cacheKey });
+            debug.log(`🌐 [SWR] Revalidation fetch (network-only) initiated for ${queryName}`, { cacheKey });
         }
 
         // PHASE 3: Request window alignment to preserve paginated edges
         // Try to detect current cursor position for paginated queries
         let revalidationVariables = variables;
-        
+
         try {
             // Check if this looks like a paginated query (has 'first' or 'last' and might have 'after'/'before')
             const varObj = variables as Record<string, any>;
             const isPaginatedQuery = varObj && (varObj.first || varObj.last);
-            
+
             if (isPaginatedQuery) {
                 // Try to find the current end cursor from the store
                 const store = environment.getStore();
                 const source = store.getSource();
                 const rootRecord = source.get('client:root');
-                
+
                 if (rootRecord) {
-                    // Look for common connection field patterns in timesheet/roster contexts  
+                    // Look for common connection field patterns in timesheet/roster contexts
                     const connectionFieldPatterns = ['timesheets', 'timeSheets', 'roster', 'employees'];
-                    
+
                     for (const fieldName of connectionFieldPatterns) {
                         try {
                             const connection = ConnectionHandler.getConnection(
                                 rootRecord as any, // Type assertion needed for ConnectionHandler compatibility
-                                fieldName, 
+                                fieldName,
                                 varObj // Use original variables for connection lookup
                             );
-                            
+
                             if (connection) {
                                 const pageInfo = connection.getLinkedRecord('pageInfo');
                                 const endCursor = pageInfo?.getValue('endCursor');
-                                
+
                                 if (endCursor && pageInfo?.getValue('hasNextPage')) {
                                     // Use current end cursor to fetch the same window + new data
                                     revalidationVariables = {
@@ -290,7 +289,7 @@ async function triggerBackgroundRevalidation<T extends OperationType>(
                                         // Keep the same page size
                                         first: varObj.first || 25
                                     };
-                                    
+
                                     if (process.env.NODE_ENV === 'development') {
                                         debug.log(`🔄 [SWR] Using cursor alignment for ${fieldName}:`, {
                                             cacheKey,
@@ -316,44 +315,156 @@ async function triggerBackgroundRevalidation<T extends OperationType>(
             revalidationVariables = variables;
         }
 
-        await fetchQuery(environment, requestNode, revalidationVariables, {
+        // Capture store snapshot BEFORE network fetch to measure write amplification and edge loss
+        const __preStoreSnapshot__ = process.env.NODE_ENV === 'development' ? environment.getStore().getSource().toJSON() : null;
+
+        const response = await fetchQuery(environment, requestNode, revalidationVariables, {
             // `force: true` instructs Relay to bypass the store for network fetch
             networkCacheConfig: { force: true },
-            // FIXED: Use 'store-or-network' to prevent no-op store publishes for identical payloads
-            fetchPolicy: 'store-or-network'
+            // Change: Use 'network-only' for guaranteed fetch; internal updater handles merge/replace
+            fetchPolicy: 'network-only'
         }).toPromise();
 
+        // New: Post-fetch commitUpdate to handle merge-or-replace logic
+        environment.commitUpdate((storeProxy) => {
+            try {
+                const rootRecordProxy = storeProxy.getRoot();
+                const varObj = revalidationVariables as Record<string, any>;
+                const connectionFieldPatterns = ['timesheets', 'timeSheets', 'roster', 'employees'];
+                for (const fieldName of connectionFieldPatterns) {
+                    const connection = ConnectionHandler.getConnection(rootRecordProxy, fieldName, varObj);
+                    if (connection) {
+                        const newEdges = connection.getLinkedRecords('edges') || [];
+                        // --- Hash based consistency guard ---
+                        const serializeEdges = (edgesArr: readonly RecordProxy[] | null) => {
+                            if (!edgesArr) return '[]';
+                            return JSON.stringify(
+                                edgesArr.map((e) => {
+                                    const node = e?.getLinkedRecord('node');
+                                    return {
+                                        id: node?.getValue('id'),
+                                        cursor: e?.getValue('cursor')
+                                    };
+                                })
+                            );
+                        };
+
+                        const prevHash = payloadHashes[cacheKey];
+                        const incomingHash = simpleHash(serializeEdges(newEdges));
+
+                        if (prevHash && prevHash !== incomingHash) {
+                            // Data changed – discard cached edges and use fresh payload only
+                            connection.setLinkedRecords([...newEdges], 'edges');
+                            if (process.env.NODE_ENV === 'development') {
+                                debug.log('[SWR] Detected changed payload. Replaced cached edges with fresh data.', {
+                                    cacheKey,
+                                    prevHash,
+                                    incomingHash,
+                                    discardedEdgeCount: (connection.getLinkedRecords('edges') || []).length - newEdges.length
+                                });
+                            }
+                        } else {
+                            // No change or first time – perform merge to preseve pagination
+                            mergeConnectionEdges(storeProxy as unknown as RecordProxy, connection, newEdges);
+                            if (process.env.NODE_ENV === 'development') {
+                                debug.log('[SWR] Payload identical – merged edges to retain pagination window.', {
+                                    cacheKey,
+                                    hash: incomingHash
+                                });
+                            }
+                        }
+
+                        // Update stored hash
+                        payloadHashes[cacheKey] = incomingHash;
+                    }
+                }
+            } catch (mergeError) {
+                if (process.env.NODE_ENV === 'development') {
+                    debug.error('[SWR] Edge merge updater failed:', mergeError);
+                }
+            }
+        });
+
         const networkDuration = Date.now() - networkStart;
+
+        // ---------- DEV ONLY: Diff store snapshots to detect write amplification & edge loss
+        if (process.env.NODE_ENV === 'development' && typeof __preStoreSnapshot__ !== 'undefined' && __preStoreSnapshot__) {
+            const __postStoreSnapshot__ = environment.getStore().getSource().toJSON();
+
+            const preKeys = Object.keys(__preStoreSnapshot__);
+            const postKeys = Object.keys(__postStoreSnapshot__);
+
+            const addedRecords = postKeys.filter((k) => !(__preStoreSnapshot__ as any)[k]).length;
+            const removedRecords = preKeys.filter((k) => !(__postStoreSnapshot__ as any)[k]).length;
+
+            let changedRecords = 0;
+            for (const key of preKeys) {
+                if (
+                    key in __postStoreSnapshot__ &&
+                    JSON.stringify((__preStoreSnapshot__ as any)[key]) !== JSON.stringify((__postStoreSnapshot__ as any)[key])
+                ) {
+                    changedRecords++;
+                }
+            }
+
+            const countEdges = (snapshot: Record<string, any>) =>
+                Object.values(snapshot).reduce((sum, record: any) => {
+                    if (record && typeof record === 'object' && (record as any).edges) {
+                        return sum + Object.keys((record as any).edges).length;
+                    }
+                    return sum;
+                }, 0);
+
+            const preEdgeCount = countEdges(__preStoreSnapshot__);
+            const postEdgeCount = countEdges(__postStoreSnapshot__);
+
+            debug.log('🧮 [SWR] Store diff after revalidation', {
+                cacheKey,
+                addedRecords,
+                removedRecords,
+                changedRecords,
+                preEdgeCount,
+                postEdgeCount,
+                edgeDelta: postEdgeCount - preEdgeCount
+            });
+        }
         if (process.env.NODE_ENV === 'development') {
-            debug.log(`✅ [SWR] Network fetch completed for ${queryName}`, {
+            const fromNetwork = networkDuration > 100; // heuristic threshold: >100ms likely network
+            debug.log(`✅ [SWR] Background fetch outcome for ${queryName}`, {
                 cacheKey,
-                durationMs: networkDuration
+                durationMs: networkDuration,
+                source: fromNetwork ? 'network' : 'store'
             });
+            if (!fromNetwork) {
+                debug.log(`⚠️ [SWR] Network request likely skipped because fetchPolicy 'network-only' returned cached data.`, {
+                    cacheKey
+                });
+            }
         }
-        
+
         // Mark as revalidated to prevent duplicate requests
         markRevalidated(cacheKey);
-        
+
         // Update per-operation timestamp **only after** network payload arrived
         const now = Date.now();
         if (window.__RELAY_OP_SAVED_AT__) {
             window.__RELAY_OP_SAVED_AT__[cacheKey] = now;
-            
+
             // STEP D: Enhanced development instrumentation for per-operation timestamp writes
             if (process.env.NODE_ENV === 'development') {
-                debug.log('📝 Per-op timestamp write', { 
+                debug.log('📝 Per-op timestamp write', {
                     cacheKey,
                     operation: queryName,
                     timestamp: new Date(now).toISOString(),
                     context: 'background_revalidation'
                 });
             }
-            
+
             // Schedule persistence to save updated timestamps
             const { notifyStoreUpdated } = await import('./createPersistedStore');
             notifyStoreUpdated();
         }
-        
+
         if (process.env.NODE_ENV === 'development') {
             debug.log(`✅ Background revalidation completed for ${queryName}`, {
                 cacheKey,
@@ -361,15 +472,14 @@ async function triggerBackgroundRevalidation<T extends OperationType>(
                 updatedOpTimestamp: new Date(now).toISOString()
             });
         }
-        
+
         // Track with observability
         relayObservability.trackCacheHit(); // Background refresh counts as cache efficiency
-        
     } catch (error) {
         if (process.env.NODE_ENV === 'development') {
             debug.error(`❌ Background revalidation failed for ${queryName}:`, error);
         }
-        
+
         // Don't mark as revalidated on error so it can retry later
         relayObservability.trackCacheMiss();
     }
@@ -384,14 +494,14 @@ export function useSWRQuery<T extends OperationType>(query: GraphQLTaggedNode, v
 
     // Extract query name for better debugging
     const queryName = extractQueryName(query);
-    
+
     // Create stable cache key for revalidation tracking
-    const cacheKey = createCacheKey(queryName, variables as Record<string, unknown>);
+    const cacheKey = createCacheKey(queryName, variables as Record<string, any>);
 
     // Perform pre-query analysis for cache debugging
     useEffect(() => {
         if (process.env.NODE_ENV === 'development') {
-            void analyzeStoreForQuery(query, queryName, variables as Record<string, unknown>)
+            void analyzeStoreForQuery(query, queryName, variables as Record<string, any>)
                 .then((analysis) => {
                     analysisRef.current = analysis;
                     debug.queryAnalysis(analysis);
@@ -432,7 +542,7 @@ export function useSWRQuery<T extends OperationType>(query: GraphQLTaggedNode, v
             const requestNode = getRequest(query);
             const opDesc = createOperationDescriptor(requestNode, variables);
             const availability = environment.check(opDesc) as RelayAvailability;
-            
+
             // NEW: Use per-operation timestamp for accurate cache age calculation
             // If no per-operation timestamp exists, treat as infinity (force immediate revalidation)
             const opSavedAt = (window.__RELAY_OP_SAVED_AT__ ?? {})[cacheKey];
@@ -441,14 +551,14 @@ export function useSWRQuery<T extends OperationType>(query: GraphQLTaggedNode, v
             // Handle both 'available' and 'stale' status - serve cached data if it exists
             if (availability.status === 'available' || availability.status === 'stale') {
                 fetchPolicy = 'store-only';
-                
+
                 // Check cache age to determine if background revalidation is needed
                 // FIXED: Respect 30-second TTL - only treat 'stale' as actionable when cache age exceeds TTL
-                const isCacheFresh = savedAt && (Date.now() - savedAt) <= REVALIDATE_MS;
+                const isCacheFresh = savedAt && Date.now() - savedAt <= REVALIDATE_MS;
                 const isStaleButExpired = availability.status === 'stale' && !isCacheFresh;
-                
+
                 // Only trigger revalidation if no timestamp exists OR cache is stale AND expired
-                if (!savedAt || isStaleButExpired || (savedAt && (Date.now() - savedAt) > REVALIDATE_MS)) {
+                if (!savedAt || isStaleButExpired || (savedAt && Date.now() - savedAt > REVALIDATE_MS)) {
                     shouldTriggerBackgroundRevalidation = true;
                 }
             }
@@ -489,17 +599,17 @@ export function useSWRQuery<T extends OperationType>(query: GraphQLTaggedNode, v
             // - Combined with debug delay (if enabled), should be much faster than 10s
             // - Also consider if we have data immediately available
             // Determine cache hit deterministically using Relay's environment.check().
-// Fallback to legacy timing heuristic only if check() throws (e.g. during testing).
-let fromCache: boolean;
-try {
-    const requestNode = getRequest(query);
-    const opDesc = createOperationDescriptor(requestNode, variables);
-    const availability = environment.check(opDesc) as RelayAvailability;
-    fromCache = availability.status === 'available' || availability.status === 'stale';
-} catch {
-    // keep previous heuristic as a safety net
-    fromCache = window.__DEBUG_CACHE_DELAY__ ? loadTime < 8000 : loadTime < 50;
-}
+            // Fallback to legacy timing heuristic only if check() throws (e.g. during testing).
+            let fromCache: boolean;
+            try {
+                const requestNode = getRequest(query);
+                const opDesc = createOperationDescriptor(requestNode, variables);
+                const availability = environment.check(opDesc) as RelayAvailability;
+                fromCache = availability.status === 'available' || availability.status === 'stale';
+            } catch {
+                // keep previous heuristic as a safety net
+                fromCache = window.__DEBUG_CACHE_DELAY__ ? loadTime < 8000 : loadTime < 50;
+            }
 
             // Detailed cache analysis
             const performCacheAnalysis = async () => {
@@ -530,7 +640,7 @@ try {
                             queryName,
                             reason,
                             availableRecords: businessRecords.slice(0, 10), // Show first 10 for brevity
-                            variables: variables as Record<string, unknown>,
+                            variables: variables as Record<string, any>,
                             storeSize: availableRecords.length,
                             timestamp: new Date().toISOString()
                         };
@@ -593,22 +703,22 @@ try {
 
             // NOTE: we no longer create a timestamp on pure cache hits; the first
             // network revalidation will establish it. This prevents ‘fresh-just-because-viewed’.
-
         }
     }, [result, queryName, delayComplete, variables]);
 
     // Background revalidation effect - runs after first render when cache is served
     useEffect(() => {
-        if (shouldTriggerBackgroundRevalidation && 
-            delayComplete && 
+        if (
+            shouldTriggerBackgroundRevalidation &&
+            delayComplete &&
             !backgroundRevalidationTriggered.current &&
-            fetchPolicy === 'store-only') {
-            
+            fetchPolicy === 'store-only'
+        ) {
             backgroundRevalidationTriggered.current = true;
-            
+
             // Schedule background revalidation on next tick to avoid blocking render
             setTimeout(() => {
-                triggerBackgroundRevalidation(query, variables, queryName, cacheKey).catch(error => {
+                triggerBackgroundRevalidation(query, variables, queryName, cacheKey).catch((error) => {
                     if (process.env.NODE_ENV === 'development') {
                         debug.error(`Background revalidation scheduling failed for ${queryName}:`, error);
                     }
@@ -653,7 +763,7 @@ if (process.env.NODE_ENV === 'development') {
                 ageMinutes: Math.round((Date.now() - timestamp) / 60000)
             }))
         };
-        
+
         console.table(stats.revalidationEntries);
         return stats;
     };
@@ -668,44 +778,44 @@ if (process.env.NODE_ENV === 'development') {
     debug.log('   - window.__clearPerOpTimestamps() - Clear per-operation cache timestamps');
     debug.log('   - window.__getPayloadHashes() - View payload hash tracking for duplicate detection');
     debug.log('   - window.__clearPayloadHashes() - Clear payload hash tracking');
-    
+
     // Add comprehensive cache validation
     (window as Window & { __validateCachePerformance?: () => Promise<object> }).__validateCachePerformance = async () => {
         try {
             const { debug } = await import('./debug');
             const { getCacheFilterStats } = await import('./cacheFilters');
             const { getRelayEnvironment } = await import('./withPersistence');
-            
+
             const environment = await getRelayEnvironment();
             const store = environment.getStore();
             const storeSnapshot = store.getSource().toJSON();
-            
+
             // Get cache filter statistics
             const filterStats = getCacheFilterStats(storeSnapshot);
-            
+
             // Count connections and pagination data
             let connectionRecords = 0;
             let businessConnections = 0;
             let paginationEdges = 0;
-            
+
             for (const [dataID, record] of Object.entries(storeSnapshot)) {
                 if (dataID.includes('__connection') && typeof record === 'object' && record) {
                     connectionRecords++;
-                    
-                    const connectionRecord = record as { 
-                        edges?: Record<string, unknown>;
+
+                    const connectionRecord = record as {
+                        edges?: Record<string, any>;
                     };
-                    
+
                     if (connectionRecord.edges) {
                         const edgeEntries = Object.entries(connectionRecord.edges);
-                        
+
                         // Check if this contains business entities
                         const hasBusinessEntities = edgeEntries.some(([, edgeValue]) => {
                             const edge = edgeValue as { node?: { __typename?: string } };
                             const typename = edge?.node?.__typename;
                             return typename && ['TimeSheet', 'PayStub', 'Employee', 'Employer', 'User'].includes(typename);
                         });
-                        
+
                         if (hasBusinessEntities) {
                             businessConnections++;
                             paginationEdges += edgeEntries.length;
@@ -713,7 +823,7 @@ if (process.env.NODE_ENV === 'development') {
                     }
                 }
             }
-            
+
             // Run validation
             const validationResult = debug.performanceValidation.validateCachePerformance({
                 totalRecords: filterStats.totalRecords,
@@ -724,7 +834,7 @@ if (process.env.NODE_ENV === 'development') {
                 businessConnections,
                 paginationEdges
             });
-            
+
             return validationResult;
         } catch (error) {
             console.error('Cache performance validation failed:', error);
@@ -736,23 +846,23 @@ if (process.env.NODE_ENV === 'development') {
     (window as Window & { __getPerOpTimestamps?: () => object }).__getPerOpTimestamps = () => {
         const timestamps = (window as any).__RELAY_OP_SAVED_AT__ || {};
         const now = Date.now();
-        
+
         const formattedTimestamps = Object.entries(timestamps).map(([cacheKey, timestamp]) => ({
             cacheKey,
             savedAt: new Date(timestamp as number).toISOString(),
             ageSeconds: Math.round((now - (timestamp as number)) / 1000),
             ageMinutes: Math.round((now - (timestamp as number)) / 60000),
-            isStale: (now - (timestamp as number)) > 30000 // 30 second threshold
+            isStale: now - (timestamp as number) > 30000 // 30 second threshold
         }));
-        
+
         console.group('📊 Per-Operation Cache Timestamps');
         console.table(formattedTimestamps);
         console.groupEnd();
-        
+
         return {
             totalOperations: formattedTimestamps.length,
-            staleOperations: formattedTimestamps.filter(op => op.isStale).length,
-            freshOperations: formattedTimestamps.filter(op => !op.isStale).length,
+            staleOperations: formattedTimestamps.filter((op) => op.isStale).length,
+            freshOperations: formattedTimestamps.filter((op) => !op.isStale).length,
             timestamps: formattedTimestamps
         };
     };
@@ -761,15 +871,15 @@ if (process.env.NODE_ENV === 'development') {
         if ((window as any).__RELAY_OP_SAVED_AT__) {
             const opSavedAt = (window as any).__RELAY_OP_SAVED_AT__;
             const count = Object.keys(opSavedAt).length;
-            
+
             // Mutate in place to preserve references stored elsewhere
-            Object.keys(opSavedAt).forEach(key => delete opSavedAt[key]);
-            
+            Object.keys(opSavedAt).forEach((key) => delete opSavedAt[key]);
+
             // Trigger persistence to save cleared state
             import('./createPersistedStore').then(({ notifyStoreUpdated }) => {
                 notifyStoreUpdated();
             });
-            
+
             debug.log(`🧹 Cleared ${count} per-operation timestamps. All queries will revalidate on next access.`);
         } else {
             debug.log('No per-operation timestamps to clear.');
@@ -783,11 +893,11 @@ if (process.env.NODE_ENV === 'development') {
             hash,
             created: 'unknown' // We don't track creation time for hashes currently
         }));
-        
+
         console.group('📊 Payload Hash Tracking');
         console.table(hashes);
         console.groupEnd();
-        
+
         return {
             totalHashes: hashes.length,
             hashes
@@ -796,17 +906,17 @@ if (process.env.NODE_ENV === 'development') {
 
     (window as Window & { __clearPayloadHashes?: () => void }).__clearPayloadHashes = () => {
         const count = Object.keys(payloadHashes).length;
-        
+
         // Clear all payload hashes
-        Object.keys(payloadHashes).forEach(key => delete payloadHashes[key]);
-        
+        Object.keys(payloadHashes).forEach((key) => delete payloadHashes[key]);
+
         // Also clear window global if it exists
         if ((window as any).__RELAY_OP_PAYLOAD_HASHES__) {
-            Object.keys((window as any).__RELAY_OP_PAYLOAD_HASHES__).forEach(key => 
-                delete (window as any).__RELAY_OP_PAYLOAD_HASHES__[key]
+            Object.keys((window as any).__RELAY_OP_PAYLOAD_HASHES__).forEach(
+                (key) => delete (window as any).__RELAY_OP_PAYLOAD_HASHES__[key]
             );
         }
-        
+
         debug.log(`🧹 Cleared ${count} payload hashes. Identical payload detection reset.`);
     };
 }
