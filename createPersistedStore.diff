diff --git a/ui/src/relay/createPersistedStore.ts b/ui/src/relay/createPersistedStore.ts
index 4381f9e4..cbcefb2c 100644
--- a/ui/src/relay/createPersistedStore.ts
+++ b/ui/src/relay/createPersistedStore.ts
@@ -2,12 +2,19 @@ import { Store, RecordSource, DataID } from 'relay-runtime';
 import { openDB } from 'idb';
 import type { CachedBlob, PersistedBlob, RecordMap } from './types';
 import { relayObservability } from './observability';
-import { shouldPersistRecord, shouldPersistRecordWithReason, getProcessedRecord, getCacheFilterStats, type PersistenceReason } from './cacheFilters';
+import {
+    shouldPersistRecord,
+    shouldPersistRecordWithReason,
+    getProcessedRecord,
+    getCacheFilterStats,
+    type PersistenceReason
+} from './cacheFilters';
 import { withWebLock } from './lockUtils';
+import { debug } from './debug';
 
-const DB_NAME   = 'relay-cache-v1';
+const DB_NAME = 'relay-cache-v1';
 const STORE_KEY = 'records';
-const DELAY_MS  = 1500; // debounce
+const DELAY_MS = 1500; // debounce
 
 // Global persistence status tracking
 let persistenceDisabled = false;
@@ -24,392 +31,532 @@ let SCHEMA_HASH: string | null = null;
 
 /**
  * Set schema hash for cache versioning
- * 
- * IMPORTANT: This should be called during app bootstrap, before any 
+ *
+ * IMPORTANT: This should be called during app bootstrap, before any
  * Relay environment creation, to ensure proper cache versioning.
- * 
+ *
  * @param hash - Schema hash from environment or build process
  */
 export const setSchemaHash = (hash: string) => {
-  if (!hash || typeof hash !== 'string') {
-    throw new Error('Schema hash must be a non-empty string');
-  }
-  
-  SCHEMA_HASH = hash;
-  
-  if (process.env.NODE_ENV === 'development') {
-    // eslint-disable-next-line no-console
-    console.log(`[Selective Cache] Schema hash set: ${hash}`);
-  }
+    if (!hash || typeof hash !== 'string') {
+        throw new Error('Schema hash must be a non-empty string');
+    }
+
+    SCHEMA_HASH = hash;
+
+    if (process.env.NODE_ENV === 'development') {
+        // eslint-disable-next-line no-console
+        console.log(`[Selective Cache] Schema hash set: ${hash}`);
+    }
 };
 
 /**
  * Get current schema hash with validation
- * 
+ *
  * @returns Current schema hash or null if not set
  */
 function getSchemaHash(): string | null {
-  return SCHEMA_HASH;
+    return SCHEMA_HASH;
 }
 
 /**
  * Validate schema hash is properly initialized
- * 
+ *
  * This provides early detection of configuration issues and ensures
  * cache versioning works correctly across deployments.
  */
 function validateSchemaHash(): void {
-  if (!SCHEMA_HASH) {
-    if (process.env.NODE_ENV === 'production') {
-      throw new Error(
-        'Schema hash not initialized. This is required for production cache versioning. ' +
-        'Ensure bootstrap.tsx is imported before Relay environment creation.'
-      );
-    }
-    
-    if (process.env.NODE_ENV === 'development') {
-      // eslint-disable-next-line no-console
-      console.warn(
-        '[Selective Cache] Schema hash not set. Cache versioning may not work correctly. ' +
-        'Consider setting VITE_GQL_SCHEMA_HASH environment variable.'
-      );
+    if (!SCHEMA_HASH) {
+        if (process.env.NODE_ENV === 'production') {
+            throw new Error(
+                'Schema hash not initialized. This is required for production cache versioning. ' +
+                    'Ensure bootstrap.tsx is imported before Relay environment creation.'
+            );
+        }
+
+        if (process.env.NODE_ENV === 'development') {
+            // eslint-disable-next-line no-console
+            console.warn(
+                '[Selective Cache] Schema hash not set. Cache versioning may not work correctly. ' +
+                    'Consider setting VITE_GQL_SCHEMA_HASH environment variable.'
+            );
+        }
     }
-  }
 }
 
-
-const CACHE_VERSION = '2025-07-31-selective'; // Bump on breaking changes
+const CACHE_VERSION = '2025-08-04-pagination-enhanced'; // Bump on breaking changes
 const MAX_CACHE_AGE = 30 * 24 * 60 * 60 * 1000; // 30 days
 
+/**
+ * Validate that pagination slices are properly preserved in hydrated store
+ */
+function validatePaginationSlices(recordMap: Record<string, unknown>): {
+    businessConnections: number;
+    totalEdges: number;
+    preservedPageInfo: number;
+} {
+    let businessConnections = 0;
+    let totalEdges = 0;
+    let preservedPageInfo = 0;
+    
+    for (const [dataID, record] of Object.entries(recordMap)) {
+        if (dataID.includes('__connection') && typeof record === 'object' && record) {
+            const connectionRecord = record as { 
+                edges?: Record<string, unknown>; 
+                pageInfo?: unknown;
+            };
+            
+            if (connectionRecord.edges) {
+                const edgeEntries = Object.entries(connectionRecord.edges);
+                
+                // Check if this contains business entities
+                const hasBusinessEntities = edgeEntries.some(([, edgeValue]) => {
+                    const edge = edgeValue as { node?: { __typename?: string } };
+                    const typename = edge?.node?.__typename;
+                    return typename && isBusinessEntityTypeName(typename);
+                });
+                
+                if (hasBusinessEntities) {
+                    businessConnections++;
+                    totalEdges += edgeEntries.length;
+                    
+                    if (connectionRecord.pageInfo) {
+                        preservedPageInfo++;
+                    }
+                }
+            }
+        }
+    }
+    
+    return {
+        businessConnections,
+        totalEdges,
+        preservedPageInfo
+    };
+}
+
+/**
+ * Helper to check business entity types (mirrors cacheFilters.ts logic)
+ */
+function isBusinessEntityTypeName(typename: string): boolean {
+    const businessEntityTypes = [
+        'TimeSheet', 'Timesheet', 'TimesheetRow',
+        'PayStub', 'PayStubDetail', 
+        'Employee', 'EmployeeInfo',
+        'Employer', 'EmployerInfo',
+        'User', 'UserInfo',
+        'Agreement', 'AgreementDetail',
+        'Classification', 'Job', 'Position'
+    ];
+    
+    return businessEntityTypes.includes(typename);
+}
 
 /* ---------- low-level helpers ---------- */
 async function db() {
-  return openDB(DB_NAME, 1, { upgrade: db => db.createObjectStore('kv') });
+    return openDB(DB_NAME, 1, { upgrade: (db) => db.createObjectStore('kv') });
 }
 
 async function loadRecordMap(): Promise<Record<string, unknown>> {
-  const result = await withWebLock(LOCK_NAME, async () => {
-    try {
-      const stored: PersistedBlob | undefined = await (await db()).get('kv', STORE_KEY);
+    const result = await withWebLock(
+        LOCK_NAME,
+        async () => {
+            try {
+                const stored: PersistedBlob | undefined = await (await db()).get('kv', STORE_KEY);
 
-    if (!stored) {
-      if (process.env.NODE_ENV === 'development') {
-        // eslint-disable-next-line no-console
-        console.log('[Selective Cache] No cached data found');
-      }
-      return {};
-    }
+                if (!stored) {
+                    if (process.env.NODE_ENV === 'development') {
+                        debug.persistence('No cached data found - starting with empty cache');
+                    }
+                    return {};
+                }
 
-    // Handle both old and new blob formats for backward compatibility
-    if ('version' in stored) {
-      // New format with version and TTL
-      if (stored.version !== CACHE_VERSION) {
-        if (process.env.NODE_ENV === 'development') {
-          // eslint-disable-next-line no-console
-          console.log(`[Selective Cache] Version mismatch: ${stored.version} != ${CACHE_VERSION}, clearing cache`);
-        }
+                // Handle both old and new blob formats for backward compatibility
+                if ('version' in stored) {
+                    // New format with version and TTL
+                    if (stored.version !== CACHE_VERSION) {
+                        if (process.env.NODE_ENV === 'development') {
+                            debug.persistence(`Version mismatch: ${stored.version} != ${CACHE_VERSION}, clearing cache`);
+                        }
+
+                        // Delete database completely to reclaim space (addresses review concern)
+                        try {
+                            await (await db()).clear('kv');
+                            // For complete cleanup, could also: indexedDB.deleteDatabase(DB_NAME)
+                        } catch (cleanupError) {
+                            if (process.env.NODE_ENV === 'development') {
+                                // eslint-disable-next-line no-console
+                                console.warn('[Selective Cache] Failed to clear old cache:', cleanupError);
+                            }
+                        }
+                        return {};
+                    }
+
+                    // Check schema hash if available
+                    const currentSchemaHash = getSchemaHash();
+                    if (currentSchemaHash && stored.schemaHash && stored.schemaHash !== currentSchemaHash) {
+                        if (process.env.NODE_ENV === 'development') {
+                            debug.persistence(`Schema hash mismatch (${stored.schemaHash} != ${currentSchemaHash}), clearing cache`);
+                        }
+                        await (await db()).clear('kv');
+                        return {};
+                    }
+
+                    // Check TTL
+                    const age = Date.now() - stored.savedAt;
+                    if (age > MAX_CACHE_AGE) {
+                        if (process.env.NODE_ENV === 'development') {
+                            debug.persistence(`Cache expired (${Math.round(age / 1000 / 60 / 60 / 24)} days old), clearing`);
+                        }
+                        await (await db()).clear('kv');
+                        return {};
+                    }
+
+                    const recordCount = Object.keys(stored.records || {}).length;
+                    if (process.env.NODE_ENV === 'development') {
+                        debug.persistence(`Loaded ${recordCount} cached records (${Math.round(age / 1000 / 60)} minutes old)`, {
+                            version: stored.version,
+                            schemaHash: stored.schemaHash || 'not set',
+                            sizeKB: Math.round(JSON.stringify(stored).length / 1024)
+                        });
+                    }
+
+                    return stored.records || {};
+                } else {
+                    // Old format (CachedBlob) - migrate by clearing
+                    if (process.env.NODE_ENV === 'development') {
+                        debug.persistence('Old cache format detected, clearing for migration');
+                    }
+                    await (await db()).clear('kv');
+                    return {};
+                }
+            } catch (error) {
+                if (process.env.NODE_ENV === 'development') {
+                    debug.error('[Selective Cache] Error loading cache:', error);
+                }
+                return {};
+            }
+        },
+        LOCK_TIMEOUT
+    );
 
-        // Delete database completely to reclaim space (addresses review concern)
-        try {
-          await (await db()).clear('kv');
-          // For complete cleanup, could also: indexedDB.deleteDatabase(DB_NAME)
-        } catch (cleanupError) {
-          if (process.env.NODE_ENV === 'development') {
-            // eslint-disable-next-line no-console
-            console.warn('[Selective Cache] Failed to clear old cache:', cleanupError);
-          }
-        }
-        return {};
-      }
+    // Ensure we always return a valid object, even if lock was unavailable
+    return result || {};
+}
 
-      // Check schema hash if available
-      const currentSchemaHash = getSchemaHash();
-      if (currentSchemaHash && stored.schemaHash && stored.schemaHash !== currentSchemaHash) {
+async function persist(recordMap: Record<string, unknown>): Promise<void> {
+    // Skip persistence if it has been disabled due to previous errors
+    if (persistenceDisabled) {
         if (process.env.NODE_ENV === 'development') {
-          // eslint-disable-next-line no-console
-          console.log(`[Selective Cache] Schema hash mismatch (${stored.schemaHash} != ${currentSchemaHash}), clearing cache`);
+            debug.persistence('Persistence disabled, skipping save');
         }
-        await (await db()).clear('kv');
-        return {};
-      }
+        return;
+    }
 
-      // Check TTL
-      const age = Date.now() - stored.savedAt;
-      if (age > MAX_CACHE_AGE) {
-        if (process.env.NODE_ENV === 'development') {
-          // eslint-disable-next-line no-console
-          console.log(`[Selective Cache] Cache expired (${Math.round(age / 1000 / 60 / 60 / 24)} days old), clearing`);
-        }
-        await (await db()).clear('kv');
-        return {};
-      }
+    const result = await withWebLock(
+        LOCK_NAME,
+        async () => {
+            // Apply selective filtering with enhanced stats
+            const filteredRecords: Record<string, unknown> = {};
 
-      const recordCount = Object.keys(stored.records || {}).length;
-      if (process.env.NODE_ENV === 'development') {
-        // eslint-disable-next-line no-console
-        console.log(`[Selective Cache] Loaded ${recordCount} cached records (${Math.round(age / 1000 / 60)} minutes old)`);
-      }
+            try {
+                const reasonStats: Record<PersistenceReason, number> = {
+                    denied_error_indicators: 0,
+                    denied_security_denylist: 0,
+                    denied_size_exceeded: 0,
+                    allowed_default: 0,
+                    allowed_client_root_sanitized: 0
+                };
+
+                for (const [dataID, record] of Object.entries(recordMap)) {
+                    const [shouldPersist, reason, processedRecord] = shouldPersistRecordWithReason(dataID, record);
+                    reasonStats[reason]++;
+
+                    if (shouldPersist) {
+                        // Use the processed record from shouldPersistRecordWithReason
+                        // (handles client:root sanitization and connection edge trimming)
+                        filteredRecords[dataID] = processedRecord ?? record;
+                    }
+                }
 
-      return stored.records || {};
-    } else {
-      // Old format (CachedBlob) - migrate by clearing
-      if (process.env.NODE_ENV === 'development') {
-        // eslint-disable-next-line no-console
-        console.log('[Selective Cache] Old cache format detected, clearing for migration');
-      }
-      await (await db()).clear('kv');
-      return {};
-    }
+                const stats = getCacheFilterStats(recordMap);
+                if (process.env.NODE_ENV === 'development') {
+                    debug.persistence(`Filtered ${stats.totalRecords} -> ${stats.persistedRecords} records`);
+                    debug.table(reasonStats);
+
+                    // Enhanced debugging for high security denial rates
+                    const securityDeniedRate = reasonStats.denied_security_denylist / stats.totalRecords;
+                    if (securityDeniedRate > 0.3) {
+                        debug.warn(
+                            `⚠️ High security denial rate: ${(securityDeniedRate * 100).toFixed(1)}% - review denylist patterns if unexpected`
+                        );
+
+                        // Show sample of denied records for debugging
+                        const deniedSamples: string[] = [];
+                        for (const [dataID, record] of Object.entries(recordMap)) {
+                            const [, reason] = shouldPersistRecordWithReason(dataID, record);
+                            if (reason === 'denied_security_denylist' && deniedSamples.length < 10) {
+                                deniedSamples.push(dataID);
+                            }
+                        }
+                        debug.persistence('Sample denied records:', deniedSamples);
+                    }
+                }
 
-    } catch (error) {
-      if (process.env.NODE_ENV === 'development') {
-        // eslint-disable-next-line no-console
-        console.error('[Selective Cache] Error loading cache:', error);
-      }
-      return {};
-    }
-  }, LOCK_TIMEOUT);
-  
-  // Ensure we always return a valid object, even if lock was unavailable
-  return result || {};
-}
+                // Create versioned blob (updated structure)
+                const currentSchemaHash = getSchemaHash();
+                const blob: PersistedBlob = {
+                    version: CACHE_VERSION,
+                    schemaHash: currentSchemaHash || undefined,
+                    savedAt: Date.now(),
+                    records: filteredRecords
+                };
+
+                // Use existing database logic but with new blob structure
+                await (await db()).put('kv', blob, STORE_KEY);
+
+                const size = JSON.stringify(blob).length;
+                if (process.env.NODE_ENV === 'development') {
+                    debug.persistence(`Persisted ${stats.persistedRecords} records (${Math.round(size / 1024)}KB)`, {
+                        persistenceRate: `${((stats.persistedRecords / stats.totalRecords) * 100).toFixed(1)}%`,
+                        sizeReduction: `${(((stats.totalSize - stats.persistedSize) / stats.totalSize) * 100).toFixed(1)}%`,
+                        schemaHash: currentSchemaHash || 'not set'
+                    });
+                }
 
-async function persist(recordMap: Record<string, unknown>): Promise<void> {
-  // Skip persistence if it has been disabled due to previous errors
-  if (persistenceDisabled) {
-    if (process.env.NODE_ENV === 'development') {
-      // eslint-disable-next-line no-console
-      console.log('[Selective Cache] Persistence disabled, skipping save');
-    }
-    return;
-  }
+                // Track persistence size if method is available
+                if (relayObservability?.trackPersistedSize) {
+                    relayObservability.trackPersistedSize(size);
+                }
+            } catch (error) {
+                // Enhanced error handling with two-strike policy
+                if (error instanceof Error) {
+                    const isQuotaError = error.name === 'QuotaExceededError' || error.name === 'NS_ERROR_DOM_QUOTA_REACHED';
+                    const isCorruptionError = error.name === 'VersionError' || error.name === 'InvalidStateError';
+
+                    if (isQuotaError) {
+                        quotaExceededCount++;
+                        relayObservability.trackQuotaExceeded();
+
+                        // Two-strike policy: try clearing cache once before disabling
+                        if (quotaExceededCount <= MAX_QUOTA_RETRIES) {
+                            if (process.env.NODE_ENV === 'development') {
+                                // eslint-disable-next-line no-console
+                                console.warn(
+                                    `[Selective Cache] Quota exceeded (attempt ${quotaExceededCount}/${MAX_QUOTA_RETRIES}), clearing cache`
+                                );
+                            }
+
+                            try {
+                                await (await db()).clear('kv');
+                                // Retry with reduced dataset (only essential records)
+                                const essentialRecords: Record<string, unknown> = {};
+                                for (const [dataID, record] of Object.entries(filteredRecords)) {
+                                    const typedRecord = record as { __typename?: string };
+                                    if (
+                                        typedRecord?.__typename &&
+                                        ['TimeSheet', 'PayStub', 'Employee', 'User'].includes(typedRecord.__typename)
+                                    ) {
+                                        essentialRecords[dataID] = record;
+                                    }
+                                }
+
+                                const essentialBlob: PersistedBlob = {
+                                    version: CACHE_VERSION,
+                                    schemaHash: getSchemaHash() || undefined,
+                                    savedAt: Date.now(),
+                                    records: essentialRecords
+                                };
+
+                                await (await db()).put('kv', essentialBlob, STORE_KEY);
+
+                                // Reset quota counter after successful essential-record persist
+                                quotaExceededCount = 0;
+
+                                // Emit retry success tracking
+                                if (relayObservability?.trackQuotaRetrySuccess) {
+                                    relayObservability.trackQuotaRetrySuccess();
+                                }
+
+                                if (process.env.NODE_ENV === 'development') {
+                                    // eslint-disable-next-line no-console
+                                    console.log(
+                                        `[Selective Cache] Successfully saved essential records (${Object.keys(essentialRecords).length})`
+                                    );
+                                }
+                                return;
+                            } catch (retryError) {
+                                if (process.env.NODE_ENV === 'development') {
+                                    // eslint-disable-next-line no-console
+                                    console.warn('[Selective Cache] Retry failed:', retryError);
+                                }
+                            }
+                        }
+
+                        // Disable persistence after max retries
+                        persistenceDisabled = true;
+                        relayObservability.setPersistenceDisabled();
+
+                        if (!persistenceErrorLogged) {
+                            persistenceErrorLogged = true;
+                            // eslint-disable-next-line no-console
+                            console.warn(`[Selective Cache] Persistence disabled after ${quotaExceededCount} quota exceeded errors`);
+
+                            if (process.env.NODE_ENV === 'development') {
+                                import('@react-spectrum/toast')
+                                    .then(({ ToastQueue }) => {
+                                        ToastQueue.negative('Cache disabled: storage quota exceeded', { timeout: 5000 });
+                                    })
+                                    .catch(() => {
+                                        // eslint-disable-next-line no-console
+                                        console.error('🚨 Cache disabled: storage quota exceeded');
+                                    });
+                            }
+                        }
+                        return;
+                    }
+
+                    if (isCorruptionError) {
+                        persistenceDisabled = true;
+                        relayObservability.setPersistenceDisabled();
+
+                        if (!persistenceErrorLogged) {
+                            persistenceErrorLogged = true;
+                            // eslint-disable-next-line no-console
+                            console.warn('[Selective Cache] Persistence disabled due to database corruption:', error.message);
+
+                            if (process.env.NODE_ENV === 'development') {
+                                import('@react-spectrum/toast')
+                                    .then(({ ToastQueue }) => {
+                                        ToastQueue.negative('Cache disabled: database corruption', { timeout: 5000 });
+                                    })
+                                    .catch(() => {
+                                        // eslint-disable-next-line no-console
+                                        console.error('🚨 Cache disabled: database corruption');
+                                    });
+                            }
+                        }
+                        return;
+                    }
+                }
 
-  const result = await withWebLock(LOCK_NAME, async () => {
-    // Apply selective filtering with enhanced stats
-    const filteredRecords: Record<string, unknown> = {};
-    
-    try {
-      const reasonStats: Record<PersistenceReason, number> = {
-        denied_client_root: 0,
-        denied_error_indicators: 0,
-        denied_blacklist_pattern: 0,
-        denied_size_exceeded: 0,
-        denied_unknown_record: 0,
-        allowed_business_entity: 0,
-        allowed_whitelist_pattern: 0
-      };
-
-      for (const [dataID, record] of Object.entries(recordMap)) {
-        const [shouldPersist, reason] = shouldPersistRecordWithReason(dataID, record);
-        reasonStats[reason]++;
-        
-        if (shouldPersist) {
-          // Use processed record with edge trimming for connections
-          filteredRecords[dataID] = getProcessedRecord(dataID, record);
-        }
-      }
+                // Re-throw other errors for normal handling
+                throw error;
+            }
+        },
+        LOCK_TIMEOUT
+    );
 
-      const stats = getCacheFilterStats(recordMap);
-      if (process.env.NODE_ENV === 'development') {
-        // eslint-disable-next-line no-console
-        console.log(`[Selective Cache] Filtered ${stats.totalRecords} -> ${stats.persistedRecords} records`);
-        // eslint-disable-next-line no-console
-        console.table(reasonStats);
-      }
-
-    // Create versioned blob (updated structure)
-    const currentSchemaHash = getSchemaHash();
-    const blob: PersistedBlob = {
-      version: CACHE_VERSION,
-      schemaHash: currentSchemaHash || undefined,
-      savedAt: Date.now(),
-      records: filteredRecords
-    };
+    // withWebLock can return undefined if lock unavailable, but persist() returns void
+    // so we don't need to handle the undefined case explicitly here
+}
 
-    // Use existing database logic but with new blob structure
-    await (await db()).put('kv', blob, STORE_KEY);
+/* ---------- public factory ---------- */
+export async function createPersistedStore(): Promise<Store> {
+    // Validate schema hash initialization before proceeding
+    validateSchemaHash();
 
-    const size = JSON.stringify(blob).length;
-    if (process.env.NODE_ENV === 'development') {
-      // eslint-disable-next-line no-console
-      console.log(`[Selective Cache] Persisted ${stats.persistedRecords} records (${Math.round(size / 1024)}KB)`);
+    let initial: Record<string, any> = {};
+    try {
+        debug.storeHydration({ step: 'start', message: 'Beginning store creation and hydration' });
+        const startTime = performance.now();
+
+        initial = await loadRecordMap();
+        const loadDuration = Math.round(performance.now() - startTime);
+        const recordCount = Object.keys(initial).length;
+
+        debug.storeHydration({
+            step: 'indexeddb_read',
+            message: 'Successfully loaded persisted records from IndexedDB',
+            recordCount,
+            duration: loadDuration
+        });
+    } catch (e) {
+        // IndexedDB unavailable (private browsing, quota errors, etc.) – fall back gracefully
+        debug.storeHydration({ step: 'error', message: 'IndexedDB unavailable, using in-memory cache only', data: e });
+        console.warn('[createPersistedStore] IndexedDB unavailable, using in-memory cache only.', e);
+    }
+    const source = new RecordSource(initial);
+    const store = new Store(source, {
+        gcReleaseBufferSize: 10
+    });
+
+    // Complete hydration logging with pagination validation
+    const finalRecordCount = Object.keys(initial).length;
+    const connectionRecords = Object.keys(initial).filter(id => id.includes('__connection'));
+    const paginationSlices = validatePaginationSlices(initial);
+    
+    debug.storeHydration({
+        step: 'complete',
+        message: 'Store hydration completed successfully',
+        recordCount: finalRecordCount,
+        connectionRecords: connectionRecords.length,
+        paginationSlices
+    });
+
+    // Expose hydration timestamp globally for TTL checks in UI hooks
+    if (typeof window !== 'undefined') {
+        (window as any).__RELAY_CACHE_HYDRATED_AT__ = Date.now();
     }
 
-    // Track persistence size if method is available
-    if (relayObservability?.trackPersistedSize) {
-      relayObservability.trackPersistedSize(size);
+    /**
+     * NOTE: Freshness TTL
+     * We expose a constant TTL (in milliseconds) that represents how long
+     * the re-hydrated cache should be considered **fresh**. Components can
+     * read this via `window.__RELAY_CACHE_TTL__` to decide whether they can
+     * safely skip the first network request.
+     *
+     * This is intentionally conservative – once the TTL has elapsed the
+     * normal `store-or-network` policy will resume and Relay will refetch
+     * stale operations as usual.
+     */
+    if (typeof window !== 'undefined' && !(window as any).__RELAY_CACHE_TTL__) {
+        // 5-minute default; override via env var VITE_RELAY_CACHE_TTL_MS if set
+        const ttl = parseInt(import.meta.env?.VITE_RELAY_CACHE_TTL_MS as string) || 5 * 60 * 1000;
+        (window as any).__RELAY_CACHE_TTL__ = ttl;
     }
 
-    } catch (error) {
-      // Enhanced error handling with two-strike policy
-      if (error instanceof Error) {
-        const isQuotaError = error.name === 'QuotaExceededError' ||
-                            error.name === 'NS_ERROR_DOM_QUOTA_REACHED';
-        const isCorruptionError = error.name === 'VersionError' ||
-                                 error.name === 'InvalidStateError';
-
-        if (isQuotaError) {
-          quotaExceededCount++;
-          relayObservability.trackQuotaExceeded();
-
-          // Two-strike policy: try clearing cache once before disabling
-          if (quotaExceededCount <= MAX_QUOTA_RETRIES) {
-            if (process.env.NODE_ENV === 'development') {
-              // eslint-disable-next-line no-console
-              console.warn(`[Selective Cache] Quota exceeded (attempt ${quotaExceededCount}/${MAX_QUOTA_RETRIES}), clearing cache`);
-            }
-            
+    // debounce write-back
+    let t: number | undefined;
+    const schedule = () => {
+        clearTimeout(t);
+        t = window.setTimeout(async () => {
             try {
-              await (await db()).clear('kv');
-              // Retry with reduced dataset (only essential records)
-              const essentialRecords: Record<string, unknown> = {};
-              for (const [dataID, record] of Object.entries(filteredRecords)) {
-                const typedRecord = record as { __typename?: string };
-                if (typedRecord?.__typename && ['TimeSheet', 'PayStub', 'Employee', 'User'].includes(typedRecord.__typename)) {
-                  essentialRecords[dataID] = record;
-                }
-              }
-              
-              const essentialBlob: PersistedBlob = {
-                version: CACHE_VERSION,
-                schemaHash: getSchemaHash() || undefined,
-                savedAt: Date.now(),
-                records: essentialRecords
-              };
-              
-              await (await db()).put('kv', essentialBlob, STORE_KEY);
-              
-              // Reset quota counter after successful essential-record persist
-              quotaExceededCount = 0;
-              
-              // Emit retry success tracking
-              if (relayObservability?.trackQuotaRetrySuccess) {
-                relayObservability.trackQuotaRetrySuccess();
-              }
-              
-              if (process.env.NODE_ENV === 'development') {
-                // eslint-disable-next-line no-console
-                console.log(`[Selective Cache] Successfully saved essential records (${Object.keys(essentialRecords).length})`);
-              }
-              return;
-            } catch (retryError) {
-              if (process.env.NODE_ENV === 'development') {
-                // eslint-disable-next-line no-console
-                console.warn('[Selective Cache] Retry failed:', retryError);
-              }
-            }
-          }
-          
-          // Disable persistence after max retries
-          persistenceDisabled = true;
-          relayObservability.setPersistenceDisabled();
-          
-          if (!persistenceErrorLogged) {
-            persistenceErrorLogged = true;
-            // eslint-disable-next-line no-console
-            console.warn(`[Selective Cache] Persistence disabled after ${quotaExceededCount} quota exceeded errors`);
-            
-            if (process.env.NODE_ENV === 'development') {
-              import('@react-spectrum/toast').then(({ ToastQueue }) => {
-                ToastQueue.negative('Cache disabled: storage quota exceeded', { timeout: 5000 });
-              }).catch(() => {
-                // eslint-disable-next-line no-console
-                console.error('🚨 Cache disabled: storage quota exceeded');
-              });
+                await persist(source.toJSON());
+            } catch (e) {
+                console.warn('[createPersistedStore] Failed to persist to IndexedDB', e);
             }
-          }
-          return;
-        }
-        
-        if (isCorruptionError) {
-          persistenceDisabled = true;
-          relayObservability.setPersistenceDisabled();
-          
-          if (!persistenceErrorLogged) {
-            persistenceErrorLogged = true;
-            // eslint-disable-next-line no-console
-            console.warn('[Selective Cache] Persistence disabled due to database corruption:', error.message);
-            
-            if (process.env.NODE_ENV === 'development') {
-              import('@react-spectrum/toast').then(({ ToastQueue }) => {
-                ToastQueue.negative('Cache disabled: database corruption', { timeout: 5000 });
-              }).catch(() => {
-                // eslint-disable-next-line no-console
-                console.error('🚨 Cache disabled: database corruption');
-              });
-            }
-          }
-          return;
-        }
-      }
+        }, DELAY_MS);
+    };
 
-      // Re-throw other errors for normal handling
-      throw error;
-    }
-  }, LOCK_TIMEOUT);
-  
-  // withWebLock can return undefined if lock unavailable, but persist() returns void
-  // so we don't need to handle the undefined case explicitly here
-}
+    // Monitor store changes by overriding the publish method
+    // The publish method is called whenever data is written to the store
+    // (from network, optimistic updates, local updates, etc.)
+    const originalPublish = store.publish.bind(store);
+    store.publish = (source: RecordSource, idsMarkedForInvalidation?: Set<DataID>) => {
+        originalPublish(source, idsMarkedForInvalidation);
+        schedule(); // Trigger persistence after any publish
+    };
 
-/* ---------- public factory ---------- */
-export async function createPersistedStore(): Promise<Store> {
-  // Validate schema hash initialization before proceeding
-  validateSchemaHash();
-  
-  let initial: Record<string, any> = {};
-  try {
-    initial = await loadRecordMap();
-  } catch (e) {
-    // IndexedDB unavailable (private browsing, quota errors, etc.) – fall back gracefully
-    console.warn('[createPersistedStore] IndexedDB unavailable, using in-memory cache only.', e);
-  }
-  const source  = new RecordSource(initial);
-  const store   = new Store(source, {
-    gcReleaseBufferSize: 10
-  });
-
-  // debounce write-back
-  let t: number | undefined;
-  const schedule = () => {
-    clearTimeout(t);
-    t = window.setTimeout(async () => {
-      try {
-        await persist(source.toJSON());
-      } catch (e) {
-        console.warn('[createPersistedStore] Failed to persist to IndexedDB', e);
-      }
-    }, DELAY_MS);
-  };
-
-  // Monitor store changes by overriding the publish method
-  // The publish method is called whenever data is written to the store
-  // (from network, optimistic updates, local updates, etc.)
-  const originalPublish = store.publish.bind(store);
-  store.publish = (source: RecordSource, idsMarkedForInvalidation?: Set<DataID>) => {
-    originalPublish(source, idsMarkedForInvalidation);
-    schedule(); // Trigger persistence after any publish
-  };
-
-  // Add beforeunload handler to flush data before tab close/reload
-  // This prevents data loss from the debounce window
-  const beforeUnloadHandler = () => {
-    if (t) {
-      clearTimeout(t);
-      try {
-        // Note: beforeunload must be synchronous, so we can't await
-        // The persist function will handle errors gracefully
-        persist(source.toJSON()).catch((e) => {
-          // Ignore errors during unload - data will be recovered on next visit
-          console.warn('[createPersistedStore] Failed to persist on beforeunload', e);
-        });
-      } catch (e) {
-        console.warn('[createPersistedStore] Failed to initiate persist on beforeunload', e);
-      }
-    }
-  };
+    // Add beforeunload handler to flush data before tab close/reload
+    // This prevents data loss from the debounce window
+    const beforeUnloadHandler = () => {
+        if (t) {
+            clearTimeout(t);
+            try {
+                // Note: beforeunload must be synchronous, so we can't await
+                // The persist function will handle errors gracefully
+                persist(source.toJSON()).catch((e) => {
+                    // Ignore errors during unload - data will be recovered on next visit
+                    console.warn('[createPersistedStore] Failed to persist on beforeunload', e);
+                });
+            } catch (e) {
+                console.warn('[createPersistedStore] Failed to initiate persist on beforeunload', e);
+            }
+        }
+    };
 
-  window.addEventListener('beforeunload', beforeUnloadHandler);
+    window.addEventListener('beforeunload', beforeUnloadHandler);
 
-  return store;
-}
\ No newline at end of file
+    return store;
+}
